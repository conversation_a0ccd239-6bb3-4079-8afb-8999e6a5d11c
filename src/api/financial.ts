import api from './index';
import { FinancialTransaction, FinancialCategory, FinancialGoal, FinancialBudget, FinancialAsset, FinancialDebt, DebtPayment, PendingReceivable } from '../types';

// Transaction APIs
export const getAllTransactions = async (): Promise<FinancialTransaction[]> => {
  const response = await api.get('/financial_transactions?_sort=date&_order=desc');
  return response.data;
};

export const getTransactionsByMonth = async (year: number, month: number): Promise<FinancialTransaction[]> => {
  // Get all transactions and filter in JavaScript for more reliable date comparison
  const allTransactions = await getAllTransactions();
  console.log(`🔍 Total transactions in database: ${allTransactions.length}`);
  console.log(`📅 Looking for transactions in ${year}-${month}`);

  const filteredTransactions = allTransactions.filter(transaction => {
    const transactionDate = new Date(transaction.date);
    const transactionYear = transactionDate.getFullYear();
    const transactionMonth = transactionDate.getMonth() + 1; // getMonth() returns 0-11

    console.log(`📊 Transaction: ${transaction.date} → Year: ${transactionYear}, Month: ${transactionMonth}, Match: ${transactionYear === year && transactionMonth === month}`);

    return transactionYear === year && transactionMonth === month;
  });

  console.log(`✅ Filtered transactions: ${filteredTransactions.length}`);

  // Sort by date descending
  return filteredTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

export const createTransaction = async (transaction: Omit<FinancialTransaction, 'id' | 'created_at' | 'updated_at'>): Promise<FinancialTransaction> => {
  const newTransaction = {
    ...transaction,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  // Create the transaction first
  const response = await api.post('/financial_transactions', newTransaction);
  const createdTransaction = response.data;

  // Update asset balance if payment method is specified and it's not cash
  if (transaction.payment_method && transaction.payment_method !== 'Tiền mặt') {
    if (transaction.type === 'expense') {
      // Deduct money from the specified asset for expenses
      await updateAssetBalanceByName(transaction.payment_method, -transaction.amount);
    } else if (transaction.type === 'income') {
      // Add money to the specified asset for income
      await updateAssetBalanceByName(transaction.payment_method, transaction.amount);
    }
  }

  return createdTransaction;
};

export const updateTransaction = async (id: number, updates: Partial<FinancialTransaction>): Promise<FinancialTransaction> => {
  const updatedTransaction = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/financial_transactions/${id}`, updatedTransaction);
  return response.data;
};

export const deleteTransaction = async (id: number): Promise<void> => {
  await api.delete(`/financial_transactions/${id}`);
};

// Category APIs
export const getAllCategories = async (): Promise<FinancialCategory[]> => {
  const response = await api.get('/financial_categories');
  return response.data;
};

export const createCategory = async (category: Omit<FinancialCategory, 'id' | 'created_at'>): Promise<FinancialCategory> => {
  const newCategory = {
    ...category,
    created_at: new Date().toISOString(),
  };

  const response = await api.post('/financial_categories', newCategory);
  return response.data;
};

// Goal APIs
export const getAllGoals = async (): Promise<FinancialGoal[]> => {
  const response = await api.get('/financial_goals?_sort=target_date&_order=asc');
  return response.data;
};

export const createGoal = async (goal: Omit<FinancialGoal, 'id' | 'created_at' | 'updated_at'>): Promise<FinancialGoal> => {
  const newGoal = {
    ...goal,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/financial_goals', newGoal);
  return response.data;
};

export const updateGoal = async (id: number, updates: Partial<FinancialGoal>): Promise<FinancialGoal> => {
  const updatedGoal = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/financial_goals/${id}`, updatedGoal);
  return response.data;
};

export const deleteGoal = async (id: number): Promise<void> => {
  await api.delete(`/financial_goals/${id}`);
};

// Budget APIs
export const getBudgetByMonth = async (year: number, month: number): Promise<FinancialBudget[]> => {
  const monthStr = `${year}-${month.toString().padStart(2, '0')}`;
  const response = await api.get(`/financial_budgets?month=${monthStr}`);
  return response.data;
};

export const createBudget = async (budget: Omit<FinancialBudget, 'id' | 'created_at' | 'updated_at'>): Promise<FinancialBudget> => {
  const newBudget = {
    ...budget,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/financial_budgets', newBudget);
  return response.data;
};

export const updateBudget = async (id: number, updates: Partial<FinancialBudget>): Promise<FinancialBudget> => {
  const updatedBudget = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/financial_budgets/${id}`, updatedBudget);
  return response.data;
};

export const deleteBudget = async (id: number): Promise<void> => {
  await api.delete(`/financial_budgets/${id}`);
};

// Utility functions
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND',
    minimumFractionDigits: 0,
  }).format(amount);
};

export const getMonthlyStats = async (year: number, month: number) => {
  console.log(`🔍 Getting monthly stats for ${year}-${month}`);

  const transactions = await getTransactionsByMonth(year, month);
  console.log(`📊 Found ${transactions.length} transactions for ${year}-${month}:`, transactions);

  // Filter out transfer transactions (Chuyển khoản) as they are internal transfers, not real income/expense
  const realTransactions = transactions.filter(t => t.category !== 'Chuyển khoản');
  console.log(`🔄 Excluding ${transactions.length - realTransactions.length} transfer transactions`);

  const income = realTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expense = realTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const balance = income - expense;

  console.log(`💰 Monthly stats (excluding transfers): Income=${income}, Expense=${expense}, Balance=${balance}`);

  // Category breakdown (excluding transfers)
  const categoryStats: { [key: string]: { amount: number; count: number } } = {};
  realTransactions.forEach(t => {
    if (!categoryStats[t.category]) {
      categoryStats[t.category] = { amount: 0, count: 0 };
    }
    categoryStats[t.category].amount += t.amount;
    categoryStats[t.category].count += 1;
  });

  return {
    income,
    expense,
    balance,
    totalTransactions: realTransactions.length, // Count only real transactions
    categoryStats,
  };
};

export const getYearlyStats = async (year: number) => {
  const allTransactions = await getAllTransactions();
  const yearTransactions = allTransactions.filter(t =>
    new Date(t.date).getFullYear() === year
  );

  const monthlyData = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1;
    const monthTransactions = yearTransactions.filter(t =>
      new Date(t.date).getMonth() + 1 === month
    );

    // Filter out transfer transactions
    const realTransactions = monthTransactions.filter(t => t.category !== 'Chuyển khoản');

    const income = realTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expense = realTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    return {
      month,
      income,
      expense,
      balance: income - expense,
    };
  });

  return monthlyData;
};

export const getWeeklyStats = async () => {
  const now = new Date();
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay()); // Start of current week (Sunday)
  startOfWeek.setHours(0, 0, 0, 0);

  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); // End of current week (Saturday)
  endOfWeek.setHours(23, 59, 59, 999);

  const transactions = await getAllTransactions();
  const weeklyTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate >= startOfWeek && transactionDate <= endOfWeek;
  });

  // Filter out transfer transactions
  const realTransactions = weeklyTransactions.filter(t => t.category !== 'Chuyển khoản');

  const income = realTransactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0);

  const expense = realTransactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0);

  const balance = income - expense;

  // Find top spending category (excluding transfers)
  const categoryExpenses = realTransactions
    .filter(t => t.type === 'expense')
    .reduce((acc, transaction) => {
      if (!acc[transaction.category]) {
        acc[transaction.category] = 0;
      }
      acc[transaction.category] += transaction.amount;
      return acc;
    }, {} as Record<string, number>);

  const topCategory = Object.entries(categoryExpenses)
    .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Không có';

  return {
    income,
    expense,
    balance,
    topCategory,
    transactionCount: realTransactions.length, // Count only real transactions
    startDate: startOfWeek.toISOString(),
    endDate: endOfWeek.toISOString(),
  };
};

// Default categories
export const getDefaultCategories = (): Omit<FinancialCategory, 'id' | 'created_at'>[] => [
  // Income categories
  { name: 'Lương', type: 'income', color: '#4caf50', icon: '💰', subcategories: ['Lương cơ bản', 'Thưởng', 'Phụ cấp', 'Khác'] },
  { name: 'Đầu tư', type: 'income', color: '#2196f3', icon: '📈', subcategories: ['Cổ phiếu', 'Crypto', 'Bất động sản', 'Lãi ngân hàng', 'Binomo', 'Forex', 'Khác'] },
  { name: 'Kinh doanh', type: 'income', color: '#ff9800', icon: '🏪', subcategories: ['Bán hàng', 'Dịch vụ', 'Freelance', 'Khác'] },
  { name: 'Thu nợ', type: 'income', color: '#8bc34a', icon: '💸', subcategories: ['Thu nợ cá nhân', 'Thu nợ kinh doanh', 'Khác'] },

  // Expense categories
  { name: 'Ăn uống', type: 'expense', color: '#f44336', icon: '🍽️', subcategories: ['Ăn sáng', 'Ăn trưa', 'Ăn tối', 'Đồ uống', 'Khác'] },
  { name: 'Đi lại', type: 'expense', color: '#9c27b0', icon: '🚗', subcategories: ['Xăng xe', 'Taxi/Grab', 'Xe bus', 'Bảo dưỡng xe', 'Khác'] },
  { name: 'Mua sắm', type: 'expense', color: '#e91e63', icon: '🛍️', subcategories: ['Quần áo', 'Điện tử', 'Gia dụng', 'Sách', 'Khác'] },
  { name: 'Giải trí', type: 'expense', color: '#673ab7', icon: '🎮', subcategories: ['Phim ảnh', 'Game', 'Du lịch', 'Thể thao', 'Khác'] },
  { name: 'Hóa đơn', type: 'expense', color: '#607d8b', icon: '📄', subcategories: ['Điện', 'Nước', 'Internet', 'Điện thoại', 'Khác'] },
  { name: 'Y tế', type: 'expense', color: '#009688', icon: '🏥', subcategories: ['Khám bệnh', 'Thuốc', 'Bảo hiểm y tế', 'Khác'] },
  { name: 'Giáo dục', type: 'expense', color: '#795548', icon: '📚', subcategories: ['Học phí', 'Sách vở', 'Khóa học online', 'Khác'] },
  { name: 'Chỗ ở', type: 'expense', color: '#8d6e63', icon: '🏠', subcategories: ['Phòng trọ', 'Khách sạn', 'Homestay', 'Airbnb', 'Nhà nghỉ', 'Resort', 'Tiền thuê nhà', 'Khác'] },
  { name: 'Công việc', type: 'expense', color: '#ff9800', icon: '💼', subcategories: ['Thiết kế', 'In ấn', 'Vận chuyển quốc tế', 'Dịch vụ chuyên nghiệp', 'Phí pháp lý', 'Marketing', 'Khác'] },
  { name: 'Đầu tư', type: 'expense', color: '#3f51b5', icon: '📊', subcategories: ['Mua cổ phiếu', 'Mua crypto', 'Đầu tư BĐS', 'Phí giao dịch', 'Binomo', 'Forex', 'Khác'] },
  { name: 'Công nghệ', type: 'expense', color: '#00bcd4', icon: '💻', subcategories: ['Phần mềm', 'Hosting/Domain', 'Thiết bị IT', 'Subscription', 'Khác'] },
  { name: 'Chuyển khoản', type: 'expense', color: '#ff5722', icon: '💳', subcategories: ['Chuyển tiền', 'Rút tiền', 'Phí ngân hàng', 'Khác'] },
];

// Asset APIs
export const getAllAssets = async (): Promise<FinancialAsset[]> => {
  const response = await api.get('/financial_assets?_sort=created_at&_order=desc');
  return response.data;
};

export const createAsset = async (asset: Omit<FinancialAsset, 'id' | 'created_at' | 'last_updated'>): Promise<FinancialAsset> => {
  const newAsset = {
    ...asset,
    last_updated: new Date().toISOString(),
    created_at: new Date().toISOString(),
  };

  const response = await api.post('/financial_assets', newAsset);
  return response.data;
};

export const updateAsset = async (id: number, updates: Partial<FinancialAsset>): Promise<FinancialAsset> => {
  const updatedAsset = {
    ...updates,
    last_updated: new Date().toISOString(),
  };

  const response = await api.patch(`/financial_assets/${id}`, updatedAsset);
  return response.data;
};

export const deleteAsset = async (id: number): Promise<void> => {
  await api.delete(`/financial_assets/${id}`);
};

// Helper function to update asset balance by name
export const updateAssetBalanceByName = async (paymentMethod: string, amountChange: number): Promise<void> => {
  try {
    const assets = await getAllAssets();

    // Extract asset name from payment method (remove balance info)
    // Example: "ACB - Tài khoản chính (2.500.000 ₫)" -> "ACB - Tài khoản chính"
    const assetName = paymentMethod.replace(/\s*\([^)]*\)\s*$/, '').trim();

    // Find asset by exact name match
    const asset = assets.find(a =>
      a.name === assetName ||
      a.name === paymentMethod ||
      a.account_number === assetName ||
      paymentMethod.includes(a.name)
    );

    if (asset) {
      const newAmount = Math.max(0, asset.amount + amountChange); // Prevent negative balance
      await updateAsset(asset.id, { amount: newAmount });
    } else {
      console.warn(`Asset not found for payment method: ${paymentMethod}. Available assets:`, assets.map(a => a.name));
      // Don't create new asset automatically - this was causing the issue
    }
  } catch (error) {
    console.error('Error updating asset balance:', error);
    // Don't throw error to prevent transaction creation from failing
  }
};

// Asset transfer function
export const transferAssetBalance = async (fromAssetId: number, toAssetId: number, amount: number, description?: string): Promise<void> => {
  try {
    const [fromAsset, toAsset] = await Promise.all([
      getAllAssets().then(assets => assets.find(a => a.id === fromAssetId)),
      getAllAssets().then(assets => assets.find(a => a.id === toAssetId)),
    ]);

    if (!fromAsset || !toAsset) {
      throw new Error('Không tìm thấy tài sản nguồn hoặc đích');
    }

    if (fromAsset.amount < amount) {
      throw new Error('Số dư không đủ để chuyển');
    }

    // Update balances
    await Promise.all([
      updateAsset(fromAssetId, { amount: fromAsset.amount - amount }),
      updateAsset(toAssetId, { amount: toAsset.amount + amount }),
    ]);

    // Create transfer transactions for tracking
    const transferDescription = description || `Chuyển từ ${fromAsset.name} sang ${toAsset.name}`;

    await Promise.all([
      createTransaction({
        date: new Date().toISOString().slice(0, 10),
        type: 'expense',
        category: 'Chuyển khoản',
        amount,
        description: `${transferDescription} (Rút từ ${fromAsset.name})`,
        payment_method: fromAsset.name,
      }),
      createTransaction({
        date: new Date().toISOString().slice(0, 10),
        type: 'income',
        category: 'Chuyển khoản',
        amount,
        description: `${transferDescription} (Nạp vào ${toAsset.name})`,
        payment_method: toAsset.name,
      }),
    ]);
  } catch (error) {
    console.error('Error transferring asset balance:', error);
    throw error;
  }
};

// Debt APIs
export const getAllDebts = async (): Promise<FinancialDebt[]> => {
  const response = await api.get('/financial_debts?_sort=created_at&_order=desc');
  return response.data;
};

export const createDebt = async (debt: Omit<FinancialDebt, 'id' | 'created_at' | 'updated_at'>): Promise<FinancialDebt> => {
  const newDebt = {
    ...debt,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/financial_debts', newDebt);
  return response.data;
};

export const updateDebt = async (id: number, updates: Partial<FinancialDebt>): Promise<FinancialDebt> => {
  const updatedDebt = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/financial_debts/${id}`, updatedDebt);
  return response.data;
};

export const deleteDebt = async (id: number): Promise<void> => {
  await api.delete(`/financial_debts/${id}`);
};

// Summary APIs
export const getAssetsSummary = async () => {
  const assets = await getAllAssets();

  const summary = {
    total: 0,
    cash: 0,
    bank: 0,
    e_wallet: 0,
    investment: 0,
    crypto: 0,
    real_estate: 0,
    other: 0,
  };

  assets.forEach(asset => {
    summary.total += asset.amount;
    summary[asset.type] += asset.amount;
  });

  return summary;
};

export const getDebtsSummary = async () => {
  const debts = await getAllDebts();

  const summary = {
    total_owe_to_me: 0, // Tổng số tiền người khác nợ tôi
    total_i_owe: 0,     // Tổng số tiền tôi nợ người khác
    active_debts: 0,    // Số khoản nợ đang hoạt động
    overdue_debts: 0,   // Số khoản nợ quá hạn
  };

  debts.forEach(debt => {
    if (debt.status === 'active' || debt.status === 'overdue') {
      if (debt.type === 'owe_to_me') {
        summary.total_owe_to_me += debt.amount;
      } else {
        summary.total_i_owe += debt.amount;
      }

      if (debt.status === 'active') {
        summary.active_debts++;
      } else {
        summary.overdue_debts++;
      }
    }
  });

  return summary;
};

// Pending Receivables APIs
export const getAllPendingReceivables = async (): Promise<PendingReceivable[]> => {
  const response = await api.get('/pending_receivables?_sort=expected_date&_order=asc');
  return response.data;
};

export const createPendingReceivable = async (receivable: Omit<PendingReceivable, 'id' | 'created_at' | 'updated_at'>): Promise<PendingReceivable> => {
  const newReceivable = {
    ...receivable,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/pending_receivables', newReceivable);
  return response.data;
};

export const updatePendingReceivable = async (id: number, updates: Partial<PendingReceivable>): Promise<PendingReceivable> => {
  const updatedReceivable = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/pending_receivables/${id}`, updatedReceivable);
  return response.data;
};

export const deletePendingReceivable = async (id: number): Promise<void> => {
  await api.delete(`/pending_receivables/${id}`);
};

// Mark receivable as received and create income transaction
export const markReceivableAsReceived = async (id: number, actualAmount?: number): Promise<void> => {
  const receivables = await getAllPendingReceivables();
  const receivable = receivables.find(r => r.id === id);

  if (!receivable) {
    throw new Error('Không tìm thấy khoản tiền chuẩn bị thu');
  }

  const finalAmount = actualAmount || receivable.amount;

  // Update receivable status
  await updatePendingReceivable(id, {
    status: 'received',
    notes: actualAmount ? `Số tiền thực nhận: ${formatCurrency(actualAmount)}` : receivable.notes
  });

  // Create income transaction
  await createTransaction({
    date: new Date().toISOString().slice(0, 10),
    type: 'income',
    category: 'Thu nợ',
    amount: finalAmount,
    description: `Thu tiền từ ${receivable.source_name}: ${receivable.description}`,
    payment_method: 'Tiền mặt', // Default, user can change later
  });
};

// Get receivables summary
export const getReceivablesSummary = async () => {
  const receivables = await getAllPendingReceivables();

  const summary = {
    total_pending: 0,
    total_confirmed: 0,
    high_priority: 0,
    overdue: 0,
    this_week: 0,
    this_month: 0,
  };

  const now = new Date();
  const oneWeekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
  const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

  receivables.forEach(receivable => {
    if (receivable.status === 'pending' || receivable.status === 'confirmed') {
      summary.total_pending += receivable.amount;

      if (receivable.status === 'confirmed') {
        summary.total_confirmed += receivable.amount;
      }

      if (receivable.priority === 'high') {
        summary.high_priority += receivable.amount;
      }

      if (receivable.expected_date) {
        const expectedDate = new Date(receivable.expected_date);

        if (expectedDate < now) {
          summary.overdue += receivable.amount;
        } else if (expectedDate <= oneWeekFromNow) {
          summary.this_week += receivable.amount;
        } else if (expectedDate <= oneMonthFromNow) {
          summary.this_month += receivable.amount;
        }
      }
    }
  });

  return summary;
};

// Asset type helpers
export const getAssetTypeIcon = (type: string): string => {
  const iconMap: { [key: string]: string } = {
    'cash': '💵',
    'bank': '🏦',
    'e_wallet': '📱',
    'investment': '📈',
    'crypto': '₿',
    'real_estate': '🏠',
    'other': '💼',
  };
  return iconMap[type] || '💼';
};

export const getAssetTypeName = (type: string): string => {
  const nameMap: { [key: string]: string } = {
    'cash': 'Tiền mặt',
    'bank': 'Tài khoản ngân hàng',
    'e_wallet': 'Ví điện tử',
    'investment': 'Đầu tư',
    'crypto': 'Tiền điện tử',
    'real_estate': 'Bất động sản',
    'other': 'Khác',
  };
  return nameMap[type] || 'Khác';
};

// Advanced Goal Management APIs
export const calculateGoalProgress = (goal: FinancialGoal) => {
  const progress = (goal.current_amount / goal.target_amount) * 100;
  const daysLeft = Math.ceil((new Date(goal.target_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  const monthsLeft = Math.max(1, Math.ceil(daysLeft / 30));
  const remainingAmount = goal.target_amount - goal.current_amount;
  const monthlyRequired = remainingAmount / monthsLeft;

  return {
    progress: Math.min(progress, 100),
    daysLeft: Math.max(0, daysLeft),
    monthsLeft,
    remainingAmount: Math.max(0, remainingAmount),
    monthlyRequired: Math.max(0, monthlyRequired),
    isOnTrack: monthlyRequired <= (goal.target_amount * 0.1), // Assuming 10% monthly is reasonable
    isOverdue: daysLeft < 0,
    isCompleted: progress >= 100,
  };
};

export const getGoalRecommendations = async (goal: FinancialGoal) => {
  const progress = calculateGoalProgress(goal);
  const recommendations: string[] = [];

  if (progress.isCompleted) {
    recommendations.push('🎉 Chúc mừng! Bạn đã đạt được mục tiêu này!');
  } else if (progress.isOverdue) {
    recommendations.push('⚠️ Mục tiêu đã quá hạn. Hãy xem xét điều chỉnh thời gian hoặc số tiền mục tiêu.');
  } else if (!progress.isOnTrack) {
    recommendations.push(`💪 Bạn cần tiết kiệm ${formatCurrency(progress.monthlyRequired)}/tháng để đạt mục tiêu.`);

    if (progress.monthlyRequired > goal.target_amount * 0.2) {
      recommendations.push('🤔 Mục tiêu có vẻ khó đạt được. Hãy xem xét kéo dài thời gian hoặc giảm số tiền mục tiêu.');
    }
  } else {
    recommendations.push('✅ Bạn đang trên đúng hướng để đạt mục tiêu!');
  }

  // Add specific saving suggestions
  if (progress.remainingAmount > 0) {
    const dailyRequired = progress.remainingAmount / Math.max(1, progress.daysLeft);
    recommendations.push(`💡 Tiết kiệm ${formatCurrency(dailyRequired)}/ngày để đạt mục tiêu đúng hạn.`);
  }

  return recommendations;
};

export const createGoalMilestones = (goal: FinancialGoal) => {
  const milestones: Array<{
    percentage: number;
    amount: number;
    isReached: boolean;
    title: string;
    description: string;
  }> = [];
  const milestonePercentages = [25, 50, 75, 100];

  milestonePercentages.forEach(percentage => {
    const amount = (goal.target_amount * percentage) / 100;
    const isReached = goal.current_amount >= amount;

    milestones.push({
      percentage,
      amount,
      isReached,
      title: `${percentage}% hoàn thành`,
      description: percentage === 100 ? 'Đạt mục tiêu!' : `Đạt ${formatCurrency(amount)}`,
    });
  });

  return milestones;
};

export const getGoalInsights = async () => {
  const goals = await getAllGoals();
  const activeGoals = goals.filter(g => g.status === 'active');

  const insights = {
    totalGoals: goals.length,
    activeGoals: activeGoals.length,
    completedGoals: goals.filter(g => g.status === 'completed').length,
    totalTargetAmount: activeGoals.reduce((sum, g) => sum + g.target_amount, 0),
    totalCurrentAmount: activeGoals.reduce((sum, g) => sum + g.current_amount, 0),
    averageProgress: activeGoals.length > 0
      ? activeGoals.reduce((sum, g) => sum + calculateGoalProgress(g).progress, 0) / activeGoals.length
      : 0,
    goalsOnTrack: activeGoals.filter(g => calculateGoalProgress(g).isOnTrack).length,
    overdueGoals: activeGoals.filter(g => calculateGoalProgress(g).isOverdue).length,
    nearCompletionGoals: activeGoals.filter(g => calculateGoalProgress(g).progress >= 80).length,
  };

  return insights;
};

export const suggestOptimalSavings = async () => {
  const goals = await getAllGoals();
  const activeGoals = goals.filter(g => g.status === 'active');

  if (activeGoals.length === 0) return null;

  // Calculate total monthly requirement
  const totalMonthlyRequired = activeGoals.reduce((sum, goal) => {
    const progress = calculateGoalProgress(goal);
    return sum + progress.monthlyRequired;
  }, 0);

  // Get recent income to suggest percentage
  const currentMonth = new Date().getMonth() + 1;
  const currentYear = new Date().getFullYear();
  const monthlyStats = await getMonthlyStats(currentYear, currentMonth);

  const suggestedPercentage = monthlyStats.income > 0
    ? (totalMonthlyRequired / monthlyStats.income) * 100
    : 0;

  return {
    totalMonthlyRequired,
    suggestedPercentage: Math.min(suggestedPercentage, 50), // Cap at 50%
    isRealistic: suggestedPercentage <= 30, // 30% is considered realistic
    monthlyIncome: monthlyStats.income,
    recommendations: suggestedPercentage > 30
      ? ['Hãy xem xét điều chỉnh mục tiêu hoặc kéo dài thời gian để phù hợp với thu nhập.']
      : ['Kế hoạch tiết kiệm của bạn rất hợp lý!'],
  };
};

// Advanced Budget Management APIs
export const calculateBudgetStatus = async (year: number, month: number) => {
  const budgets = await getBudgetByMonth(year, month);

  const budgetAnalysis = budgets.map(budget => {
    const spentPercentage = budget.planned_amount > 0
      ? (budget.actual_amount / budget.planned_amount) * 100
      : 0;

    let status: 'under' | 'on_track' | 'over' | 'warning' = 'on_track';
    if (spentPercentage >= 100) status = 'over';
    else if (spentPercentage >= 80) status = 'warning';
    else if (spentPercentage < 50) status = 'under';

    return {
      ...budget,
      spentPercentage,
      status,
      remainingAmount: Math.max(0, budget.planned_amount - budget.actual_amount),
      daysLeftInMonth: new Date(year, month, 0).getDate() - new Date().getDate(),
      dailyBudgetRemaining: Math.max(0, (budget.planned_amount - budget.actual_amount) / Math.max(1, new Date(year, month, 0).getDate() - new Date().getDate())),
    };
  });

  const totalPlanned = budgets.reduce((sum, b) => sum + b.planned_amount, 0);
  const totalActual = budgets.reduce((sum, b) => sum + b.actual_amount, 0);
  const totalRemaining = totalPlanned - totalActual;

  return {
    budgets: budgetAnalysis,
    summary: {
      totalPlanned,
      totalActual,
      totalRemaining,
      overallSpentPercentage: totalPlanned > 0 ? (totalActual / totalPlanned) * 100 : 0,
      categoriesOverBudget: budgetAnalysis.filter(b => b.status === 'over').length,
      categoriesOnTrack: budgetAnalysis.filter(b => b.status === 'on_track').length,
      categoriesUnder: budgetAnalysis.filter(b => b.status === 'under').length,
      categoriesWarning: budgetAnalysis.filter(b => b.status === 'warning').length,
    },
  };
};

export const generateBudgetRecommendations = async (year: number, month: number) => {
  const budgetStatus = await calculateBudgetStatus(year, month);
  const recommendations: string[] = [];

  // Overall budget health
  if (budgetStatus.summary.overallSpentPercentage > 100) {
    recommendations.push('🚨 Bạn đã vượt ngân sách tổng thể. Hãy cắt giảm chi tiêu ngay!');
  } else if (budgetStatus.summary.overallSpentPercentage > 80) {
    recommendations.push('⚠️ Bạn đã chi tiêu 80% ngân sách. Hãy cẩn thận với các khoản chi còn lại.');
  } else if (budgetStatus.summary.overallSpentPercentage < 50) {
    recommendations.push('✅ Bạn đang chi tiêu rất tiết kiệm. Có thể cân nhắc tăng ngân sách cho một số danh mục.');
  }

  // Category-specific recommendations
  budgetStatus.budgets.forEach(budget => {
    if (budget.status === 'over') {
      recommendations.push(`🔴 ${budget.category}: Đã vượt ngân sách ${(budget.spentPercentage - 100).toFixed(1)}%. Cần cắt giảm ngay.`);
    } else if (budget.status === 'warning') {
      recommendations.push(`🟡 ${budget.category}: Đã chi ${budget.spentPercentage.toFixed(1)}% ngân sách. Hãy cẩn thận.`);
    } else if (budget.status === 'under' && budget.spentPercentage < 30) {
      recommendations.push(`🟢 ${budget.category}: Chỉ chi ${budget.spentPercentage.toFixed(1)}% ngân sách. Có thể tăng chi tiêu hợp lý.`);
    }
  });

  return recommendations;
};

export const suggestBudgetAllocation = async (totalBudget: number) => {
  // Standard budget allocation percentages (50/30/20 rule adapted for Vietnamese context)
  const suggestions = {
    'Ăn uống': { percentage: 25, amount: totalBudget * 0.25 },
    'Đi lại': { percentage: 15, amount: totalBudget * 0.15 },
    'Hóa đơn': { percentage: 20, amount: totalBudget * 0.20 },
    'Mua sắm': { percentage: 10, amount: totalBudget * 0.10 },
    'Giải trí': { percentage: 10, amount: totalBudget * 0.10 },
    'Y tế': { percentage: 5, amount: totalBudget * 0.05 },
    'Giáo dục': { percentage: 5, amount: totalBudget * 0.05 },
    'Tiết kiệm': { percentage: 10, amount: totalBudget * 0.10 },
  };

  return suggestions;
};

export const getBudgetInsights = async (year: number, month: number) => {
  const [currentBudget, lastMonthBudget] = await Promise.all([
    calculateBudgetStatus(year, month),
    calculateBudgetStatus(month === 1 ? year - 1 : year, month === 1 ? 12 : month - 1),
  ]);

  const insights = {
    currentMonth: currentBudget.summary,
    lastMonth: lastMonthBudget.summary,
    trends: {
      spendingChange: currentBudget.summary.totalActual - lastMonthBudget.summary.totalActual,
      spendingChangePercentage: lastMonthBudget.summary.totalActual > 0
        ? ((currentBudget.summary.totalActual - lastMonthBudget.summary.totalActual) / lastMonthBudget.summary.totalActual) * 100
        : 0,
      budgetEfficiencyChange: currentBudget.summary.overallSpentPercentage - lastMonthBudget.summary.overallSpentPercentage,
    },
    alerts: {
      hasOverspending: currentBudget.summary.categoriesOverBudget > 0,
      hasWarnings: currentBudget.summary.categoriesWarning > 0,
      improvementFromLastMonth: currentBudget.summary.overallSpentPercentage < lastMonthBudget.summary.overallSpentPercentage,
    },
  };

  return insights;
};

export const createBudgetFromTemplate = async (year: number, month: number, templateType: 'previous_month' | 'average_spending' | 'suggested') => {
  const monthStr = `${year}-${month.toString().padStart(2, '0')}`;

  if (templateType === 'previous_month') {
    const prevMonth = month === 1 ? 12 : month - 1;
    const prevYear = month === 1 ? year - 1 : year;
    const prevBudgets = await getBudgetByMonth(prevYear, prevMonth);

    const newBudgets = prevBudgets.map(budget => ({
      month: monthStr,
      category: budget.category,
      planned_amount: budget.planned_amount,
      actual_amount: 0,
      remaining_amount: budget.planned_amount,
      status: 'under' as const,
    }));

    return Promise.all(newBudgets.map(budget => createBudget(budget)));
  }

  if (templateType === 'average_spending') {
    const stats = await getMonthlyStats(year, month);
    const categories = Object.keys(stats.categoryStats || {});

    const newBudgets = categories.map(category => {
      const avgSpending = stats.categoryStats[category]?.amount || 0;
      const suggestedBudget = Math.round(avgSpending * 1.1); // 10% buffer

      return {
        month: monthStr,
        category,
        planned_amount: suggestedBudget,
        actual_amount: 0,
        remaining_amount: suggestedBudget,
        status: 'under' as const,
      };
    });

    return Promise.all(newBudgets.map(budget => createBudget(budget)));
  }

  if (templateType === 'suggested') {
    const monthlyStats = await getMonthlyStats(year, month);
    const totalIncome = monthlyStats.income || 0;
    const suggestions = await suggestBudgetAllocation(totalIncome * 0.7); // 70% of income for expenses

    const newBudgets = Object.entries(suggestions).map(([category, data]) => ({
      month: monthStr,
      category,
      planned_amount: Math.round(data.amount),
      actual_amount: 0,
      remaining_amount: Math.round(data.amount),
      status: 'under' as const,
    }));

    return Promise.all(newBudgets.map(budget => createBudget(budget)));
  }

  return [];
};

// Investment Management APIs
export const getAllInvestments = async (): Promise<any[]> => {
  const response = await api.get('/investments');
  return response.data;
};

export const createInvestment = async (investment: any): Promise<any> => {
  const newInvestment = {
    ...investment,
    market_value: investment.quantity * investment.current_price,
    total_invested: investment.quantity * investment.average_price,
    unrealized_pnl: (investment.current_price - investment.average_price) * investment.quantity,
    realized_pnl: 0,
    last_updated: new Date().toISOString(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/investments', newInvestment);
  return response.data;
};

export const updateInvestment = async (id: number, updates: any): Promise<any> => {
  const updatedInvestment = {
    ...updates,
    updated_at: new Date().toISOString(),
  };

  const response = await api.patch(`/investments/${id}`, updatedInvestment);
  return response.data;
};

export const deleteInvestment = async (id: number): Promise<void> => {
  await api.delete(`/investments/${id}`);
};

export const getInvestmentTransactions = async (investmentId?: number): Promise<any[]> => {
  const url = investmentId ? `/investment_transactions?investment_id=${investmentId}` : '/investment_transactions';
  const response = await api.get(url);
  return response.data;
};

export const createInvestmentTransaction = async (transaction: any): Promise<any> => {
  const newTransaction = {
    ...transaction,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/investment_transactions', newTransaction);
  return response.data;
};

export const getPortfolios = async (): Promise<any[]> => {
  const response = await api.get('/portfolios');
  return response.data;
};

export const createPortfolio = async (portfolio: any): Promise<any> => {
  const newPortfolio = {
    ...portfolio,
    total_value: 0,
    total_invested: 0,
    total_pnl: 0,
    pnl_percentage: 0,
    cash_balance: 0,
    allocation: [],
    performance: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/portfolios', newPortfolio);
  return response.data;
};

export const getWatchlists = async (): Promise<any[]> => {
  const response = await api.get('/watchlists');
  return response.data;
};

export const createWatchlist = async (watchlist: any): Promise<any> => {
  const newWatchlist = {
    ...watchlist,
    symbols: [],
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  const response = await api.post('/watchlists', newWatchlist);
  return response.data;
};

export const addToWatchlist = async (watchlistId: number, symbol: any): Promise<any> => {
  const newItem = {
    ...symbol,
    watchlist_id: watchlistId,
    last_updated: new Date().toISOString(),
  };

  const response = await api.post('/watchlist_items', newItem);
  return response.data;
};

// Portfolio Analytics
export const calculatePortfolioMetrics = async (portfolioId?: number) => {
  const investments = await getAllInvestments();
  const filteredInvestments = portfolioId
    ? investments.filter(inv => inv.portfolio_id === portfolioId)
    : investments;

  const totalValue = filteredInvestments.reduce((sum, inv) => sum + inv.market_value, 0);
  const totalInvested = filteredInvestments.reduce((sum, inv) => sum + inv.total_invested, 0);
  const totalPnL = totalValue - totalInvested;
  const pnlPercentage = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0;

  // Asset allocation
  const allocation = filteredInvestments.reduce((acc, inv) => {
    const existing = acc.find(a => a.type === inv.type);
    if (existing) {
      existing.value += inv.market_value;
      existing.percentage = (existing.value / totalValue) * 100;
    } else {
      acc.push({
        type: inv.type,
        value: inv.market_value,
        percentage: (inv.market_value / totalValue) * 100,
      });
    }
    return acc;
  }, [] as any[]);

  // Top performers
  const topGainers = filteredInvestments
    .filter(inv => inv.unrealized_pnl > 0)
    .sort((a, b) => b.unrealized_pnl - a.unrealized_pnl)
    .slice(0, 5);

  const topLosers = filteredInvestments
    .filter(inv => inv.unrealized_pnl < 0)
    .sort((a, b) => a.unrealized_pnl - b.unrealized_pnl)
    .slice(0, 5);

  return {
    totalValue,
    totalInvested,
    totalPnL,
    pnlPercentage,
    allocation,
    topGainers,
    topLosers,
    totalInvestments: filteredInvestments.length,
    diversificationScore: allocation.length * 20, // Simple diversification score
  };
};

// Market data simulation (in real app, this would fetch from external APIs)
export const getMarketData = async (symbols: string[]): Promise<any[]> => {
  // Mock market data - in real app, integrate with APIs like Alpha Vantage, Yahoo Finance, etc.
  const mockData = {
    'VCB': { name: 'Vietcombank', price: 92500, change: 1500, change_percentage: 1.65 },
    'VIC': { name: 'Vingroup', price: 45200, change: -800, change_percentage: -1.74 },
    'VHM': { name: 'Vinhomes', price: 55300, change: 2100, change_percentage: 3.95 },
    'BTC': { name: 'Bitcoin', price: **********, change: ********, change_percentage: 1.93 },
    'ETH': { name: 'Ethereum', price: ********, change: -1200000, change_percentage: -1.38 },
    'BNB': { name: 'Binance Coin', price: ********, change: 320000, change_percentage: 2.26 },
  };

  return symbols.map(symbol => ({
    symbol,
    name: mockData[symbol as keyof typeof mockData]?.name || symbol,
    price: mockData[symbol as keyof typeof mockData]?.price || Math.random() * 100000,
    change: mockData[symbol as keyof typeof mockData]?.change || (Math.random() - 0.5) * 10000,
    change_percentage: mockData[symbol as keyof typeof mockData]?.change_percentage || (Math.random() - 0.5) * 10,
    volume: Math.floor(Math.random() * 1000000),
    last_updated: new Date().toISOString(),
  }));
};

// Update investment prices
export const updateInvestmentPrices = async (): Promise<void> => {
  const investments = await getAllInvestments();
  const symbols = [...new Set(investments.map(inv => inv.symbol))];
  const marketData = await getMarketData(symbols);

  for (const investment of investments) {
    const marketPrice = marketData.find(data => data.symbol === investment.symbol);
    if (marketPrice) {
      const updatedInvestment = {
        ...investment,
        current_price: marketPrice.price,
        market_value: investment.quantity * marketPrice.price,
        unrealized_pnl: (marketPrice.price - investment.average_price) * investment.quantity,
        last_updated: new Date().toISOString(),
      };

      await updateInvestment(investment.id, updatedInvestment);
    }
  }
};

// Investment insights and recommendations
export const getInvestmentInsights = async () => {
  const investments = await getAllInvestments();
  const portfolioMetrics = await calculatePortfolioMetrics();

  const insights = {
    totalPortfolioValue: portfolioMetrics.totalValue,
    totalPnL: portfolioMetrics.totalPnL,
    pnlPercentage: portfolioMetrics.pnlPercentage,
    bestPerformer: portfolioMetrics.topGainers[0],
    worstPerformer: portfolioMetrics.topLosers[0],
    diversificationScore: portfolioMetrics.diversificationScore,
    riskLevel: portfolioMetrics.pnlPercentage > 20 ? 'high' :
               portfolioMetrics.pnlPercentage > 10 ? 'medium' : 'low',
    recommendations: generateInvestmentRecommendations(portfolioMetrics),
  };

  return insights;
};

const generateInvestmentRecommendations = (metrics: any): string[] => {
  const recommendations = [];

  if (metrics.diversificationScore < 60) {
    recommendations.push('Danh mục đầu tư chưa đa dạng. Hãy xem xét đầu tư vào nhiều lĩnh vực khác nhau.');
  }

  if (metrics.pnlPercentage < -10) {
    recommendations.push('Danh mục đang lỗ nhiều. Hãy xem xét cắt lỗ hoặc tăng cường nghiên cứu.');
  }

  if (metrics.allocation.find((a: any) => a.type === 'crypto' && a.percentage > 30)) {
    recommendations.push('Tỷ trọng crypto cao có thể rủi ro. Hãy cân nhắc giảm tỷ trọng.');
  }

  if (metrics.allocation.length === 1) {
    recommendations.push('Đầu tư vào một loại tài sản duy nhất rất rủi ro. Hãy đa dạng hóa danh mục.');
  }

  if (recommendations.length === 0) {
    recommendations.push('Danh mục đầu tư của bạn đang khá tốt. Tiếp tục theo dõi và điều chỉnh khi cần.');
  }

  return recommendations;
};