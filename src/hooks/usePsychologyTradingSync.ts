import { useState, useEffect, useMemo } from 'react';
import { getAllBinaryTrades } from '../api/binaryTrades';
import { BinaryOptionTrade } from '../types/binaryTrades';

interface PsychologyData {
  date: string;
  mentalState: string;
  stressLevel: number;
  confidenceLevel: number;
  emotionalStability: number;
  disciplineScore: number;
  meditationMinutes: number;
  violations: number;
}

interface TradingPerformance {
  date: string;
  totalTrades: number;
  winRate: number;
  profit: number;
  avgTradeAmount: number;
  maxDrawdown: number;
  emotionalTrades: number; // Trades made in high emotional state
}

interface CorrelationMetrics {
  stressVsWinRate: number;
  confidenceVsProfit: number;
  meditationVsPerformance: number;
  disciplineVsViolations: number;
  emotionalStateImpact: number;
}

interface PsychologyTradingSync {
  psychologyData: PsychologyData[];
  tradingData: TradingPerformance[];
  correlationMetrics: CorrelationMetrics;
  insights: string[];
  loading: boolean;
  error: string | null;
  refreshData: () => void;
}

export const usePsychologyTradingSync = (): PsychologyTradingSync => {
  const [trades, setTrades] = useState<BinaryOptionTrade[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load trading data
  const loadTradingData = async () => {
    try {
      setLoading(true);
      const tradesData = await getAllBinaryTrades();
      setTrades(tradesData);
      setError(null);
    } catch (err) {
      setError('Failed to load trading data');
      console.error('Error loading trades:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadTradingData();
  }, []);

  // Load psychology data from localStorage
  const psychologyData = useMemo((): PsychologyData[] => {
    const dailyChecks = JSON.parse(localStorage.getItem('daily_psychology_checks') || '[]');
    const emotions = JSON.parse(localStorage.getItem('emotion_entries') || '[]');
    const meditations = JSON.parse(localStorage.getItem('meditation_sessions') || '[]');
    const disciplines = JSON.parse(localStorage.getItem('discipline_violations') || '[]');

    // Group data by date
    const dataByDate: { [date: string]: PsychologyData } = {};

    // Process daily checks
    dailyChecks.forEach((check: any) => {
      const date = check.date;
      if (!dataByDate[date]) {
        dataByDate[date] = {
          date,
          mentalState: check.mentalState || 'calm',
          stressLevel: check.stressLevel || 5,
          confidenceLevel: check.confidenceLevel || 5,
          emotionalStability: 5,
          disciplineScore: 85,
          meditationMinutes: 0,
          violations: 0
        };
      }
      dataByDate[date].mentalState = check.mentalState;
      dataByDate[date].stressLevel = check.stressLevel;
      dataByDate[date].confidenceLevel = check.confidenceLevel;
    });

    // Process emotions for emotional stability
    emotions.forEach((emotion: any) => {
      const date = new Date(emotion.timestamp).toISOString().split('T')[0];
      if (!dataByDate[date]) {
        dataByDate[date] = {
          date,
          mentalState: 'calm',
          stressLevel: 5,
          confidenceLevel: 5,
          emotionalStability: 5,
          disciplineScore: 85,
          meditationMinutes: 0,
          violations: 0
        };
      }
      // Calculate emotional stability based on emotion intensity variance
      const dayEmotions = emotions.filter((e: any) => 
        new Date(e.timestamp).toISOString().split('T')[0] === date
      );
      const avgIntensity = dayEmotions.reduce((sum: number, e: any) => sum + e.intensity, 0) / dayEmotions.length;
      const variance = dayEmotions.reduce((sum: number, e: any) => sum + Math.pow(e.intensity - avgIntensity, 2), 0) / dayEmotions.length;
      dataByDate[date].emotionalStability = Math.max(1, 10 - Math.sqrt(variance));
    });

    // Process meditation data
    meditations.forEach((session: any) => {
      const date = new Date(session.date).toISOString().split('T')[0];
      if (!dataByDate[date]) {
        dataByDate[date] = {
          date,
          mentalState: 'calm',
          stressLevel: 5,
          confidenceLevel: 5,
          emotionalStability: 5,
          disciplineScore: 85,
          meditationMinutes: 0,
          violations: 0
        };
      }
      dataByDate[date].meditationMinutes += session.duration || 0;
    });

    // Process discipline violations
    disciplines.forEach((violation: any) => {
      const date = new Date(violation.timestamp).toISOString().split('T')[0];
      if (!dataByDate[date]) {
        dataByDate[date] = {
          date,
          mentalState: 'calm',
          stressLevel: 5,
          confidenceLevel: 5,
          emotionalStability: 5,
          disciplineScore: 85,
          meditationMinutes: 0,
          violations: 0
        };
      }
      dataByDate[date].violations += 1;
      dataByDate[date].disciplineScore = Math.max(0, 100 - (dataByDate[date].violations * 15));
    });

    return Object.values(dataByDate).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, []);

  // Process trading data by date
  const tradingData = useMemo((): TradingPerformance[] => {
    const dataByDate: { [date: string]: TradingPerformance } = {};

    trades.forEach(trade => {
      const date = new Date(trade.entry_datetime).toISOString().split('T')[0];
      if (!dataByDate[date]) {
        dataByDate[date] = {
          date,
          totalTrades: 0,
          winRate: 0,
          profit: 0,
          avgTradeAmount: 0,
          maxDrawdown: 0,
          emotionalTrades: 0
        };
      }

      const dayData = dataByDate[date];
      dayData.totalTrades += 1;
      dayData.profit += trade.profit;

      // Calculate win rate
      const dayTrades = trades.filter(t => 
        new Date(t.entry_datetime).toISOString().split('T')[0] === date
      );
      const wins = dayTrades.filter(t => t.trade_status === 'win').length;
      dayData.winRate = (wins / dayTrades.length) * 100;

      // Calculate average trade amount
      dayData.avgTradeAmount = dayTrades.reduce((sum, t) => sum + t.trade_amount, 0) / dayTrades.length;

      // Calculate max drawdown (simplified)
      let runningProfit = 0;
      let peak = 0;
      let maxDD = 0;
      dayTrades.forEach(t => {
        runningProfit += t.profit;
        if (runningProfit > peak) peak = runningProfit;
        const drawdown = peak - runningProfit;
        if (drawdown > maxDD) maxDD = drawdown;
      });
      dayData.maxDrawdown = maxDD;

      // Estimate emotional trades (trades with high amounts or during high stress periods)
      const psychDay = psychologyData.find(p => p.date === date);
      if (psychDay && (psychDay.stressLevel > 7 || trade.trade_amount > dayData.avgTradeAmount * 1.5)) {
        dayData.emotionalTrades += 1;
      }
    });

    return Object.values(dataByDate).sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [trades, psychologyData]);

  // Calculate correlation metrics
  const correlationMetrics = useMemo((): CorrelationMetrics => {
    const calculateCorrelation = (x: number[], y: number[]): number => {
      if (x.length !== y.length || x.length === 0) return 0;
      
      const n = x.length;
      const sumX = x.reduce((a, b) => a + b, 0);
      const sumY = y.reduce((a, b) => a + b, 0);
      const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
      const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
      const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);
      
      const numerator = n * sumXY - sumX * sumY;
      const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
      
      return denominator === 0 ? 0 : numerator / denominator;
    };

    // Match psychology and trading data by date
    const matchedData = psychologyData
      .map(psych => ({
        psych,
        trading: tradingData.find(t => t.date === psych.date)
      }))
      .filter(item => item.trading);

    if (matchedData.length < 2) {
      return {
        stressVsWinRate: 0,
        confidenceVsProfit: 0,
        meditationVsPerformance: 0,
        disciplineVsViolations: 0,
        emotionalStateImpact: 0
      };
    }

    const stressLevels = matchedData.map(item => item.psych.stressLevel);
    const winRates = matchedData.map(item => item.trading!.winRate);
    const confidenceLevels = matchedData.map(item => item.psych.confidenceLevel);
    const profits = matchedData.map(item => item.trading!.profit);
    const meditationMinutes = matchedData.map(item => item.psych.meditationMinutes);
    const disciplineScores = matchedData.map(item => item.psych.disciplineScore);
    const violations = matchedData.map(item => item.psych.violations);
    const emotionalTrades = matchedData.map(item => item.trading!.emotionalTrades);

    return {
      stressVsWinRate: -calculateCorrelation(stressLevels, winRates), // Negative because high stress should correlate with low win rate
      confidenceVsProfit: calculateCorrelation(confidenceLevels, profits),
      meditationVsPerformance: calculateCorrelation(meditationMinutes, winRates),
      disciplineVsViolations: -calculateCorrelation(disciplineScores, violations),
      emotionalStateImpact: -calculateCorrelation(emotionalTrades, winRates)
    };
  }, [psychologyData, tradingData]);

  // Generate insights based on correlations
  const insights = useMemo((): string[] => {
    const insights: string[] = [];
    
    if (correlationMetrics.stressVsWinRate > 0.3) {
      insights.push("🔴 Mức stress cao có tương quan mạnh với tỷ lệ thắng thấp. Hãy tập trung vào quản lý stress.");
    }
    
    if (correlationMetrics.confidenceVsProfit > 0.4) {
      insights.push("🟢 Mức độ tự tin cao có tương quan tích cực với lợi nhuận. Tiếp tục xây dựng sự tự tin.");
    }
    
    if (correlationMetrics.meditationVsPerformance > 0.3) {
      insights.push("🧘 Thiền định có tác động tích cực đến hiệu suất giao dịch. Hãy duy trì thói quen thiền.");
    }
    
    if (correlationMetrics.disciplineVsViolations > 0.3) {
      insights.push("⚖️ Kỷ luật tốt giúp giảm vi phạm. Hãy tuân thủ nghiêm ngặt các nguyên tắc giao dịch.");
    }
    
    if (correlationMetrics.emotionalStateImpact > 0.3) {
      insights.push("😤 Giao dịch trong trạng thái cảm xúc cao có tác động tiêu cực. Hãy kiểm soát cảm xúc trước khi giao dịch.");
    }

    if (insights.length === 0) {
      insights.push("📊 Cần thêm dữ liệu để phân tích tương quan giữa tâm lý và hiệu suất giao dịch.");
    }

    return insights;
  }, [correlationMetrics]);

  return {
    psychologyData,
    tradingData,
    correlationMetrics,
    insights,
    loading,
    error,
    refreshData: loadTradingData
  };
};
