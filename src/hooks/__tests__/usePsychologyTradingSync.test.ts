import { renderHook, waitFor } from '@testing-library/react';
import { usePsychologyTradingSync } from '../usePsychologyTradingSync';
import { getAllBinaryTrades } from '../../api/binaryTrades';
import { BinaryOptionTrade } from '../../types/binaryTrades';

// Mock the API
jest.mock('../../api/binaryTrades');
const mockGetAllBinaryTrades = getAllBinaryTrades as jest.MockedFunction<typeof getAllBinaryTrades>;

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage });

const mockTrades: BinaryOptionTrade[] = [
  {
    id: 1,
    entry_datetime: '2024-01-01T10:00:00Z',
    currency_pair: 'EUR/USD',
    trade_amount: 100000,
    profit: 85000,
    trade_status: 'win',
    profit_percentage: 85,
    exit_datetime: '2024-01-01T10:05:00Z',
    notes: ''
  },
  {
    id: 2,
    entry_datetime: '2024-01-01T11:00:00Z',
    currency_pair: 'GBP/USD',
    trade_amount: 150000,
    profit: -150000,
    trade_status: 'loss',
    profit_percentage: 85,
    exit_datetime: '2024-01-01T11:05:00Z',
    notes: ''
  },
  {
    id: 3,
    entry_datetime: '2024-01-02T10:00:00Z',
    currency_pair: 'USD/JPY',
    trade_amount: 200000,
    profit: 170000,
    trade_status: 'win',
    profit_percentage: 85,
    exit_datetime: '2024-01-02T10:05:00Z',
    notes: ''
  }
];

const mockPsychologyData = [
  {
    date: '2024-01-01',
    mentalState: 'calm',
    stressLevel: 3,
    confidenceLevel: 8,
    emotionalStability: 7,
    disciplineScore: 90,
    meditationMinutes: 20,
    violations: 0
  },
  {
    date: '2024-01-02',
    mentalState: 'excited',
    stressLevel: 6,
    confidenceLevel: 6,
    emotionalStability: 5,
    disciplineScore: 75,
    meditationMinutes: 0,
    violations: 1
  }
];

describe('usePsychologyTradingSync', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockImplementation((key: string) => {
      switch (key) {
        case 'daily_psychology_checks':
          return JSON.stringify(mockPsychologyData);
        case 'emotion_entries':
          return JSON.stringify([]);
        case 'meditation_sessions':
          return JSON.stringify([
            { date: '2024-01-01', duration: 20 }
          ]);
        case 'discipline_violations':
          return JSON.stringify([
            { timestamp: '2024-01-02T09:00:00Z' }
          ]);
        default:
          return '[]';
      }
    });
  });

  test('should load and sync psychology and trading data', async () => {
    mockGetAllBinaryTrades.mockResolvedValue(mockTrades);

    const { result } = renderHook(() => usePsychologyTradingSync());

    // Initially loading
    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should have psychology data
    expect(result.current.psychologyData).toHaveLength(2);
    expect(result.current.psychologyData[0].date).toBe('2024-01-02');
    expect(result.current.psychologyData[1].date).toBe('2024-01-01');

    // Should have trading data
    expect(result.current.tradingData).toHaveLength(2);
    expect(result.current.tradingData[0].date).toBe('2024-01-02');
    expect(result.current.tradingData[1].date).toBe('2024-01-01');

    // Should calculate trading performance correctly
    const jan1Data = result.current.tradingData.find(d => d.date === '2024-01-01');
    expect(jan1Data?.totalTrades).toBe(2);
    expect(jan1Data?.winRate).toBe(50); // 1 win out of 2 trades
    expect(jan1Data?.profit).toBe(-65000); // 85000 - 150000

    const jan2Data = result.current.tradingData.find(d => d.date === '2024-01-02');
    expect(jan2Data?.totalTrades).toBe(1);
    expect(jan2Data?.winRate).toBe(100); // 1 win out of 1 trade
    expect(jan2Data?.profit).toBe(170000);
  });

  test('should calculate correlation metrics', async () => {
    mockGetAllBinaryTrades.mockResolvedValue(mockTrades);

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should have correlation metrics
    expect(result.current.correlationMetrics).toBeDefined();
    expect(typeof result.current.correlationMetrics.stressVsWinRate).toBe('number');
    expect(typeof result.current.correlationMetrics.confidenceVsProfit).toBe('number');
    expect(typeof result.current.correlationMetrics.meditationVsPerformance).toBe('number');
    expect(typeof result.current.correlationMetrics.disciplineVsViolations).toBe('number');
    expect(typeof result.current.correlationMetrics.emotionalStateImpact).toBe('number');
  });

  test('should generate insights based on correlations', async () => {
    mockGetAllBinaryTrades.mockResolvedValue(mockTrades);

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Should have insights
    expect(result.current.insights).toBeDefined();
    expect(Array.isArray(result.current.insights)).toBe(true);
    expect(result.current.insights.length).toBeGreaterThan(0);
  });

  test('should handle API errors gracefully', async () => {
    mockGetAllBinaryTrades.mockRejectedValue(new Error('API Error'));

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe('Failed to load trading data');
    expect(result.current.trades).toEqual([]);
  });

  test('should handle empty data correctly', async () => {
    mockGetAllBinaryTrades.mockResolvedValue([]);
    mockLocalStorage.getItem.mockReturnValue('[]');

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.psychologyData).toEqual([]);
    expect(result.current.tradingData).toEqual([]);
    expect(result.current.insights).toContain('📊 Cần thêm dữ liệu để phân tích tương quan giữa tâm lý và hiệu suất giao dịch.');
  });

  test('should refresh data when refreshData is called', async () => {
    mockGetAllBinaryTrades.mockResolvedValue(mockTrades);

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear mock and set new data
    mockGetAllBinaryTrades.mockClear();
    const newTrades = [...mockTrades, {
      id: 4,
      entry_datetime: '2024-01-03T10:00:00Z',
      currency_pair: 'AUD/USD',
      trade_amount: 100000,
      profit: 85000,
      trade_status: 'win',
      profit_percentage: 85,
      exit_datetime: '2024-01-03T10:05:00Z',
      notes: ''
    }];
    mockGetAllBinaryTrades.mockResolvedValue(newTrades);

    // Call refresh
    result.current.refreshData();

    await waitFor(() => {
      expect(mockGetAllBinaryTrades).toHaveBeenCalledTimes(1);
    });
  });

  test('should calculate emotional stability from emotion entries', async () => {
    mockGetAllBinaryTrades.mockResolvedValue([]);
    
    // Mock emotion entries with varying intensities
    mockLocalStorage.getItem.mockImplementation((key: string) => {
      if (key === 'emotion_entries') {
        return JSON.stringify([
          { timestamp: '2024-01-01T10:00:00Z', intensity: 8 },
          { timestamp: '2024-01-01T11:00:00Z', intensity: 3 },
          { timestamp: '2024-01-01T12:00:00Z', intensity: 9 },
        ]);
      }
      return '[]';
    });

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const jan1Data = result.current.psychologyData.find(d => d.date === '2024-01-01');
    expect(jan1Data?.emotionalStability).toBeDefined();
    expect(jan1Data?.emotionalStability).toBeGreaterThan(0);
    expect(jan1Data?.emotionalStability).toBeLessThanOrEqual(10);
  });

  test('should calculate discipline score based on violations', async () => {
    mockGetAllBinaryTrades.mockResolvedValue([]);
    
    // Mock multiple violations
    mockLocalStorage.getItem.mockImplementation((key: string) => {
      if (key === 'discipline_violations') {
        return JSON.stringify([
          { timestamp: '2024-01-01T10:00:00Z' },
          { timestamp: '2024-01-01T11:00:00Z' },
          { timestamp: '2024-01-01T12:00:00Z' },
        ]);
      }
      return '[]';
    });

    const { result } = renderHook(() => usePsychologyTradingSync());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const jan1Data = result.current.psychologyData.find(d => d.date === '2024-01-01');
    expect(jan1Data?.violations).toBe(3);
    expect(jan1Data?.disciplineScore).toBe(55); // 100 - (3 * 15)
  });
});
