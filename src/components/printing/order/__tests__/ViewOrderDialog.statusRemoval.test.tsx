import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ViewOrderDialog from '../ViewOrderDialog';
import { Order } from '../../../../types/printing';

const mockOrder: Order = {
  id: 1,
  order_number: 'ORD-2024-001',
  customer_id: 1,
  customer_name: '<PERSON><PERSON><PERSON><PERSON> Văn A',
  customer_phone: '0123456789',
  items: [
    {
      id: 1,
      product_name: 'Business Card',
      description: 'Premium business cards',
      quantity: 100,
      unit_price: 50000,
      total_price: 5000000,
      weight: 0.5,
      printer_name: 'Printer ABC',
      production_time: 3,
      delivery_time: 2
    },
    {
      id: 2,
      product_name: '<PERSON><PERSON>chure',
      description: 'Company brochure A4',
      quantity: 50,
      unit_price: 80000,
      total_price: 4000000,
      weight: 0.8,
      printer_name: 'Printer XYZ',
      production_time: 5,
      delivery_time: 3
    }
  ],
  subtotal: 9000000,
  discount_amount: 0,
  shipping_fee: 100000,
  total_amount: 9100000,
  paid_amount: 0,
  bonus_amount: 0,
  total_weight: 90,
  order_type: 'printing',
  payment_status: 'pending',
  order_status: 'pending',
  payment_method: 'cash',
  shipping_address: '123 Test Street',
  shipping_method: 'delivery',
  notes: 'Test order',
  created_by: 'admin',
  created_at: '2024-01-01T10:00:00Z',
  updated_at: '2024-01-01T10:00:00Z'
};

describe('ViewOrderDialog - Status Column Removal', () => {
  test('should not display status column in order items table', () => {
    render(
      <ViewOrderDialog
        open={true}
        order={mockOrder}
        onClose={() => {}}
      />
    );

    // Check that status column header is not present
    expect(screen.queryByText('Trạng thái')).not.toBeInTheDocument();
    
    // Check that other column headers are still present
    expect(screen.getByText('Sản phẩm')).toBeInTheDocument();
    expect(screen.getByText('Mô tả')).toBeInTheDocument();
    expect(screen.getByText('Số lượng')).toBeInTheDocument();
    expect(screen.getByText('Đơn giá')).toBeInTheDocument();
    expect(screen.getByText('Cân nặng')).toBeInTheDocument();
    expect(screen.getByText('Thành tiền')).toBeInTheDocument();
  });

  test('should display all order items without status chips', () => {
    render(
      <ViewOrderDialog
        open={true}
        order={mockOrder}
        onClose={() => {}}
      />
    );

    // Check that product names are displayed
    expect(screen.getByText('Business Card')).toBeInTheDocument();
    expect(screen.getByText('Brochure')).toBeInTheDocument();

    // Check that descriptions are displayed
    expect(screen.getByText('Premium business cards')).toBeInTheDocument();
    expect(screen.getByText('Company brochure A4')).toBeInTheDocument();

    // Check that quantities are displayed
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('50')).toBeInTheDocument();

    // Check that prices are displayed
    expect(screen.getByText('50,000₫')).toBeInTheDocument();
    expect(screen.getByText('80,000₫')).toBeInTheDocument();

    // Check that total prices are displayed
    expect(screen.getByText('5,000,000₫')).toBeInTheDocument();
    expect(screen.getByText('4,000,000₫')).toBeInTheDocument();

    // Check that no status-related chips are present
    expect(screen.queryByText('pending')).not.toBeInTheDocument();
    expect(screen.queryByText('production')).not.toBeInTheDocument();
    expect(screen.queryByText('ready')).not.toBeInTheDocument();
    expect(screen.queryByText('shipped')).not.toBeInTheDocument();
  });

  test('should display correct number of table columns', () => {
    render(
      <ViewOrderDialog
        open={true}
        order={mockOrder}
        onClose={() => {}}
      />
    );

    // Count table headers - should be 6 columns (without status)
    const tableHeaders = screen.getAllByRole('columnheader');
    expect(tableHeaders).toHaveLength(6);

    // Verify the exact headers
    expect(tableHeaders[0]).toHaveTextContent('Sản phẩm');
    expect(tableHeaders[1]).toHaveTextContent('Mô tả');
    expect(tableHeaders[2]).toHaveTextContent('Số lượng');
    expect(tableHeaders[3]).toHaveTextContent('Đơn giá');
    expect(tableHeaders[4]).toHaveTextContent('Cân nặng');
    expect(tableHeaders[5]).toHaveTextContent('Thành tiền');
  });

  test('should display printer information correctly', () => {
    render(
      <ViewOrderDialog
        open={true}
        order={mockOrder}
        onClose={() => {}}
      />
    );

    // Check that printer names are displayed as subtitles
    expect(screen.getByText('Nhà in: Printer ABC')).toBeInTheDocument();
    expect(screen.getByText('Nhà in: Printer XYZ')).toBeInTheDocument();
  });

  test('should handle order with no items gracefully', () => {
    const orderWithNoItems: Order = {
      ...mockOrder,
      items: []
    };

    render(
      <ViewOrderDialog
        open={true}
        order={orderWithNoItems}
        onClose={() => {}}
      />
    );

    // Should still show table headers
    expect(screen.getByText('Sản phẩm')).toBeInTheDocument();
    expect(screen.getByText('Mô tả')).toBeInTheDocument();
    expect(screen.getByText('Số lượng')).toBeInTheDocument();
    expect(screen.getByText('Đơn giá')).toBeInTheDocument();
    expect(screen.getByText('Cân nặng')).toBeInTheDocument();
    expect(screen.getByText('Thành tiền')).toBeInTheDocument();

    // Should not show status column
    expect(screen.queryByText('Trạng thái')).not.toBeInTheDocument();
  });

  test('should handle items with missing optional fields', () => {
    const orderWithIncompleteItems: Order = {
      ...mockOrder,
      items: [
        {
          id: 1,
          product_name: 'Test Product',
          description: '',
          quantity: 1,
          unit_price: 100000,
          total_price: 100000,
          weight: 0.1,
          production_time: 1,
          delivery_time: 1
          // Missing printer_name and other optional fields
        }
      ]
    };

    render(
      <ViewOrderDialog
        open={true}
        order={orderWithIncompleteItems}
        onClose={() => {}}
      />
    );

    // Should display the product
    expect(screen.getByText('Test Product')).toBeInTheDocument();
    
    // Should handle missing description
    expect(screen.getByText('N/A')).toBeInTheDocument();
    
    // Should not display printer info if not available
    expect(screen.queryByText('Nhà in:')).not.toBeInTheDocument();
    
    // Should not show status column
    expect(screen.queryByText('Trạng thái')).not.toBeInTheDocument();
  });
});
