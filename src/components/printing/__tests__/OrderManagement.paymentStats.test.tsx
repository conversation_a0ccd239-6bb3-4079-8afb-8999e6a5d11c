import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import OrderManagement from '../OrderManagement';
import { Order } from '../../../types/printing';

// Mock the API functions
jest.mock('../../../api/printing', () => ({
  getAllOrders: jest.fn(),
  getAllOrderGroupsWithOrders: jest.fn(() => Promise.resolve([])),
  deleteOrder: jest.fn(),
  deleteOrderGroup: jest.fn(),
}));

// Mock other components to focus on statistics
jest.mock('../order/OrdersTable', () => () => <div>OrdersTable</div>);
jest.mock('../order/OrderGroupsTable', () => () => <div>OrderGroupsTable</div>);
jest.mock('../order/OrderGroupsFilters', () => () => <div>OrderGroupsFilters</div>);
jest.mock('../order/OrderGroupsBulkActions', () => () => <div>OrderGroupsBulkActions</div>);

const mockOrdersWithPayments: Order[] = [
  {
    id: 1,
    order_number: 'ORD-001',
    customer_name: 'Nguyễn Văn A',
    created_at: '2024-01-01T10:00:00Z',
    total_amount: 1000000,
    paid_amount: 1000000,
    order_status: 'ready',
    order_type: 'printing',
    payment_status: 'paid', // Fully paid
    items: []
  },
  {
    id: 2,
    order_number: 'ORD-002',
    customer_name: 'Trần Thị B',
    created_at: '2024-01-02T10:00:00Z',
    total_amount: 2000000,
    paid_amount: 1200000,
    order_status: 'production',
    order_type: 'design',
    payment_status: 'partial', // Partially paid
    items: []
  },
  {
    id: 3,
    order_number: 'ORD-003',
    customer_name: 'Lê Văn C',
    created_at: '2024-01-03T10:00:00Z',
    total_amount: 1500000,
    paid_amount: 0,
    order_status: 'pending',
    order_type: 'printing',
    payment_status: 'unpaid', // Not paid
    items: []
  },
  {
    id: 4,
    order_number: 'ORD-004',
    customer_name: 'Phạm Thị D',
    created_at: '2024-01-04T10:00:00Z',
    total_amount: 800000,
    paid_amount: 800000,
    order_status: 'ready',
    order_type: 'design',
    payment_status: 'paid', // Fully paid
    items: []
  },
  {
    id: 5,
    order_number: 'ORD-005',
    customer_name: 'Hoàng Văn E',
    created_at: '2024-01-05T10:00:00Z',
    total_amount: 3000000,
    paid_amount: 500000,
    order_status: 'production',
    order_type: 'printing',
    payment_status: 'partial', // Partially paid
    items: []
  }
];

describe('OrderManagement - Payment Statistics', () => {
  beforeEach(() => {
    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(mockOrdersWithPayments);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should calculate payment statistics correctly', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      // Should be on individual orders tab by default
      expect(screen.getByText('Đơn hàng riêng lẻ')).toBeInTheDocument();
    });

    // Check total orders count
    expect(screen.getByText('5')).toBeInTheDocument(); // Total 5 orders

    // Check paid orders count (2 fully paid + 2 partially paid = 4)
    expect(screen.getByText('4')).toBeInTheDocument(); // Should show in paid card

    // Check paid amount (1,000,000 + 800,000 + 1,200,000 + 500,000 = 3,500,000)
    expect(screen.getByText('3,500,000₫')).toBeInTheDocument();

    // Check total revenue (1,000,000 + 2,000,000 + 1,500,000 + 800,000 + 3,000,000 = 8,300,000)
    expect(screen.getByText('8,300,000₫')).toBeInTheDocument();

    // Check unpaid amount (0 + 800,000 + 1,500,000 + 0 + 2,500,000 = 4,800,000)
    expect(screen.getByText('4,800,000₫')).toBeInTheDocument();
  });

  test('should show correct payment card title', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByText('Đã thanh toán')).toBeInTheDocument();
    });
  });

  test('should display payment icon in paid card', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      // The PaymentIcon should be rendered (we can't directly test the icon, but we can test the card structure)
      const paidCard = screen.getByText('Đã thanh toán').closest('.MuiCard-root');
      expect(paidCard).toBeInTheDocument();
    });
  });

  test('should handle orders with zero paid amount correctly', async () => {
    const ordersWithZeroPaid: Order[] = [
      {
        id: 1,
        order_number: 'ORD-001',
        customer_name: 'Test Customer',
        created_at: '2024-01-01T10:00:00Z',
        total_amount: 1000000,
        paid_amount: 0,
        order_status: 'pending',
        order_type: 'printing',
        payment_status: 'unpaid',
        items: []
      }
    ];

    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(ordersWithZeroPaid);

    render(<OrderManagement />);
    
    await waitFor(() => {
      // Should show 0 paid orders
      expect(screen.getByText('0')).toBeInTheDocument(); // In paid card
      
      // Should show 0₫ paid amount
      expect(screen.getByText('0₫')).toBeInTheDocument(); // In paid card
      
      // Should show full amount as unpaid
      expect(screen.getByText('1,000,000₫')).toBeInTheDocument(); // In unpaid card
    });
  });

  test('should handle orders with no payment_status correctly', async () => {
    const ordersWithoutPaymentStatus: Order[] = [
      {
        id: 1,
        order_number: 'ORD-001',
        customer_name: 'Test Customer',
        created_at: '2024-01-01T10:00:00Z',
        total_amount: 1000000,
        order_status: 'pending',
        order_type: 'printing',
        // payment_status is undefined
        items: []
      }
    ];

    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(ordersWithoutPaymentStatus);

    render(<OrderManagement />);
    
    await waitFor(() => {
      // Should treat undefined payment_status as unpaid
      expect(screen.getByText('0')).toBeInTheDocument(); // In paid card (0 paid orders)
      expect(screen.getByText('1,000,000₫')).toBeInTheDocument(); // In unpaid card
    });
  });

  test('should calculate partial payments correctly', async () => {
    const partialPaymentOrders: Order[] = [
      {
        id: 1,
        order_number: 'ORD-001',
        customer_name: 'Test Customer',
        created_at: '2024-01-01T10:00:00Z',
        total_amount: 1000000,
        paid_amount: 300000,
        order_status: 'production',
        order_type: 'printing',
        payment_status: 'partial',
        items: []
      }
    ];

    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(partialPaymentOrders);

    render(<OrderManagement />);
    
    await waitFor(() => {
      // Should count partial payment as 1 paid order
      expect(screen.getByText('1')).toBeInTheDocument(); // In paid card
      
      // Should show only the paid amount (300,000₫)
      expect(screen.getByText('300,000₫')).toBeInTheDocument(); // In paid card
      
      // Should show remaining unpaid amount (700,000₫)
      expect(screen.getByText('700,000₫')).toBeInTheDocument(); // In unpaid card
    });
  });
});
