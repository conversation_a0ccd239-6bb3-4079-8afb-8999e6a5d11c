import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import OrderManagement from '../OrderManagement';
import { Order } from '../../../types/printing';

// Mock the API functions
jest.mock('../../../api/printing', () => ({
  getAllOrders: jest.fn(),
  getAllOrderGroupsWithOrders: jest.fn(() => Promise.resolve([])),
  deleteOrder: jest.fn(),
  deleteOrderGroup: jest.fn(),
}));

// Mock OrdersTable component to test sorting props
jest.mock('../order/OrdersTable', () => {
  return function MockOrdersTable({ 
    orders, 
    sortField, 
    sortDirection, 
    onSort 
  }: any) {
    return (
      <div data-testid="orders-table">
        <div data-testid="sort-field">{sortField}</div>
        <div data-testid="sort-direction">{sortDirection}</div>
        <div data-testid="orders-count">{orders.length}</div>
        <button onClick={() => onSort('created_at')}>Sort by Date</button>
        <button onClick={() => onSort('order_number')}>Sort by Order Number</button>
        <button onClick={() => onSort('total_amount')}>Sort by Amount</button>
        <button onClick={() => onSort('order_status')}>Sort by Status</button>
        {orders.map((order: Order, index: number) => (
          <div key={order.id} data-testid={`order-${index}`}>
            {order.order_number} - {order.created_at}
          </div>
        ))}
      </div>
    );
  };
});

// Mock other components
jest.mock('../order/OrderGroupsTable', () => () => <div>OrderGroupsTable</div>);
jest.mock('../order/OrderGroupsFilters', () => () => <div>OrderGroupsFilters</div>);
jest.mock('../order/OrderGroupsBulkActions', () => () => <div>OrderGroupsBulkActions</div>);

const mockOrders: Order[] = [
  {
    id: 1,
    order_number: 'ORD-001',
    created_at: '2024-01-01T10:00:00Z',
    total_amount: 100000,
    order_status: 'pending',
    customer_id: 1,
    order_type: 'printing',
    payment_status: 'unpaid',
    items: []
  },
  {
    id: 2,
    order_number: 'ORD-002',
    created_at: '2024-01-02T10:00:00Z',
    total_amount: 200000,
    order_status: 'production',
    customer_id: 2,
    order_type: 'design',
    payment_status: 'paid',
    items: []
  },
  {
    id: 3,
    order_number: 'ORD-003',
    created_at: '2024-01-03T10:00:00Z',
    total_amount: 150000,
    order_status: 'ready',
    customer_id: 3,
    order_type: 'printing',
    payment_status: 'partial',
    items: []
  }
];

describe('OrderManagement - Sorting Functionality', () => {
  beforeEach(() => {
    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(mockOrders);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should sort orders by created_at desc by default (newest first)', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Check default sorting
    expect(screen.getByTestId('sort-field')).toHaveTextContent('created_at');
    expect(screen.getByTestId('sort-direction')).toHaveTextContent('desc');

    // Check order is newest first (ORD-003, ORD-002, ORD-001)
    const firstOrder = screen.getByTestId('order-0');
    expect(firstOrder).toHaveTextContent('ORD-003');
  });

  test('should show sort indicator with current sorting info', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByText(/Đơn hàng được sắp xếp theo:/)).toBeInTheDocument();
    });

    expect(screen.getByText(/Ngày tạo/)).toBeInTheDocument();
    expect(screen.getByText(/Mới nhất trước/)).toBeInTheDocument();
  });

  test('should change sort direction when clicking same field', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Click sort by date again to change direction
    const sortByDateButton = screen.getByText('Sort by Date');
    fireEvent.click(sortByDateButton);

    await waitFor(() => {
      expect(screen.getByTestId('sort-direction')).toHaveTextContent('asc');
    });

    expect(screen.getByText(/Cũ nhất trước/)).toBeInTheDocument();
  });

  test('should change sort field when clicking different field', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Click sort by order number
    const sortByOrderNumberButton = screen.getByText('Sort by Order Number');
    fireEvent.click(sortByOrderNumberButton);

    await waitFor(() => {
      expect(screen.getByTestId('sort-field')).toHaveTextContent('order_number');
      expect(screen.getByTestId('sort-direction')).toHaveTextContent('asc');
    });

    expect(screen.getByText(/Mã đơn hàng/)).toBeInTheDocument();
  });

  test('should show reset button when not using default sorting', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Change sorting to show reset button
    const sortByAmountButton = screen.getByText('Sort by Amount');
    fireEvent.click(sortByAmountButton);

    await waitFor(() => {
      expect(screen.getByText('Reset về mặc định')).toBeInTheDocument();
    });
  });

  test('should reset to default sorting when reset button is clicked', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Change sorting
    const sortByAmountButton = screen.getByText('Sort by Amount');
    fireEvent.click(sortByAmountButton);

    await waitFor(() => {
      expect(screen.getByText('Reset về mặc định')).toBeInTheDocument();
    });

    // Click reset
    const resetButton = screen.getByText('Reset về mặc định');
    fireEvent.click(resetButton);

    await waitFor(() => {
      expect(screen.getByTestId('sort-field')).toHaveTextContent('created_at');
      expect(screen.getByTestId('sort-direction')).toHaveTextContent('desc');
    });

    // Reset button should be hidden
    expect(screen.queryByText('Reset về mặc định')).not.toBeInTheDocument();
  });

  test('should display correct orders count', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    expect(screen.getByTestId('orders-count')).toHaveTextContent('3');
    expect(screen.getByText('3 đơn hàng')).toBeInTheDocument();
  });
});
