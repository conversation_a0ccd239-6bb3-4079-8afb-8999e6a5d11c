import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import OrderManagement from '../OrderManagement';
import { Order } from '../../../types/printing';

// Mock the API functions
jest.mock('../../../api/printing', () => ({
  getAllOrders: jest.fn(),
  getAllOrderGroupsWithOrders: jest.fn(() => Promise.resolve([])),
  deleteOrder: jest.fn(),
  deleteOrderGroup: jest.fn(),
}));

// Mock OrdersTable component to test filtering
jest.mock('../order/OrdersTable', () => {
  return function MockOrdersTable({ orders }: any) {
    return (
      <div data-testid="orders-table">
        <div data-testid="orders-count">{orders.length}</div>
        {orders.map((order: Order, index: number) => (
          <div key={order.id} data-testid={`order-${index}`}>
            {order.customer_name} - {order.order_number}
          </div>
        ))}
      </div>
    );
  };
});

// Mock other components
jest.mock('../order/OrderGroupsTable', () => () => <div>OrderGroupsTable</div>);
jest.mock('../order/OrderGroupsFilters', () => () => <div>OrderGroupsFilters</div>);
jest.mock('../order/OrderGroupsBulkActions', () => () => <div>OrderGroupsBulkActions</div>);

const mockOrders: Order[] = [
  {
    id: 1,
    order_number: 'ORD-001',
    customer_name: 'Nguyễn Văn A',
    created_at: '2024-01-01T10:00:00Z',
    total_amount: 100000,
    order_status: 'pending',
    order_type: 'printing',
    payment_status: 'unpaid',
    items: []
  },
  {
    id: 2,
    order_number: 'ORD-002',
    customer_name: 'Trần Thị B',
    created_at: '2024-01-02T10:00:00Z',
    total_amount: 200000,
    order_status: 'production',
    order_type: 'design',
    payment_status: 'paid',
    items: []
  },
  {
    id: 3,
    order_number: 'ORD-003',
    customer_name: 'Nguyễn Văn A',
    created_at: '2024-01-03T10:00:00Z',
    total_amount: 150000,
    order_status: 'ready',
    order_type: 'printing',
    payment_status: 'partial',
    items: []
  },
  {
    id: 4,
    order_number: 'ORD-004',
    customer_name: 'Lê Văn C',
    created_at: '2024-01-04T10:00:00Z',
    total_amount: 300000,
    order_status: 'pending',
    order_type: 'design',
    payment_status: 'unpaid',
    items: []
  }
];

describe('OrderManagement - Customer Filter', () => {
  beforeEach(() => {
    const { getAllOrders } = require('../../../api/printing');
    getAllOrders.mockResolvedValue(mockOrders);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should display customer filter dropdown in OrdersTable', async () => {
    render(<OrderManagement />);

    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });
  });

  test('should show all customers in dropdown', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });

    // Click dropdown to open
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);

    // Should show "Tất cả khách hàng" option
    expect(screen.getByText('Tất cả khách hàng')).toBeInTheDocument();
    
    // Should show unique customers
    expect(screen.getByText('Nguyễn Văn A')).toBeInTheDocument();
    expect(screen.getByText('Trần Thị B')).toBeInTheDocument();
    expect(screen.getByText('Lê Văn C')).toBeInTheDocument();
  });

  test('should filter orders by selected customer', async () => {
    render(<OrderManagement />);
    
    await waitFor(() => {
      expect(screen.getByTestId('orders-table')).toBeInTheDocument();
    });

    // Initially should show all 4 orders
    expect(screen.getByTestId('orders-count')).toHaveTextContent('4');

    // Select customer filter
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);

    const customerOption = screen.getByText('Nguyễn Văn A');
    fireEvent.click(customerOption);

    await waitFor(() => {
      // Should show only 2 orders for "Nguyễn Văn A"
      expect(screen.getByTestId('orders-count')).toHaveTextContent('2');
    });

    // Should show only orders from selected customer
    expect(screen.getByTestId('order-0')).toHaveTextContent('Nguyễn Văn A');
    expect(screen.getByTestId('order-1')).toHaveTextContent('Nguyễn Văn A');
  });

  test('should show customer filter chip when filter is applied', async () => {
    render(<OrderManagement />);

    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });

    // Select customer filter
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);

    const customerOption = screen.getByText('Trần Thị B');
    fireEvent.click(customerOption);

    await waitFor(() => {
      // Should show filter chip
      expect(screen.getByText('Khách hàng: Trần Thị B')).toBeInTheDocument();
    });
  });

  test('should show reset all button when filter is applied', async () => {
    render(<OrderManagement />);

    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });

    // Apply customer filter
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);
    fireEvent.click(screen.getByText('Trần Thị B'));

    await waitFor(() => {
      // Should show reset all button
      expect(screen.getByText('Reset tất cả')).toBeInTheDocument();
    });
  });

  test('should reset all filters and sorting when reset all is clicked', async () => {
    render(<OrderManagement />);

    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });

    // Apply customer filter
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);
    fireEvent.click(screen.getByText('Nguyễn Văn A'));

    await waitFor(() => {
      expect(screen.getByTestId('orders-count')).toHaveTextContent('2');
    });

    // Click reset all
    const resetAllButton = screen.getByText('Reset tất cả');
    fireEvent.click(resetAllButton);

    await waitFor(() => {
      // Should show all orders
      expect(screen.getByTestId('orders-count')).toHaveTextContent('4');

      // Filter chip should be gone
      expect(screen.queryByText(/Khách hàng:/)).not.toBeInTheDocument();

      // Reset all button should be gone
      expect(screen.queryByText('Reset tất cả')).not.toBeInTheDocument();
    });
  });

  test('should update statistics when filter is applied', async () => {
    render(<OrderManagement />);

    await waitFor(() => {
      expect(screen.getByLabelText('Khách hàng')).toBeInTheDocument();
    });

    // Initially should show total count
    expect(screen.getByText('4')).toBeInTheDocument(); // Total orders in stats

    // Apply filter for customer with 2 orders
    const dropdown = screen.getByLabelText('Khách hàng');
    fireEvent.mouseDown(dropdown);
    fireEvent.click(screen.getByText('Nguyễn Văn A'));

    await waitFor(() => {
      // Stats should update to show filtered count
      expect(screen.getByText('2')).toBeInTheDocument(); // Filtered count
      expect(screen.getByText('(đã lọc)')).toBeInTheDocument(); // Filter indicator
    });
  });
});
