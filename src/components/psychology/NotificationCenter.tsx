import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Badge,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Chip,
  Divider,
  Switch,
  FormControlLabel,
  Tooltip,
  Snackbar
} from '@mui/material';
import {
  Notifications as NotificationIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  CheckCircle as SuccessIcon,
  Psychology as PsychIcon,
  TrendingDown as StressIcon,
  SelfImprovement as MeditationIcon,
  Schedule as ScheduleIcon,
  Close as CloseIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { usePsychologyTradingSync } from '../../hooks/usePsychologyTradingSync';

interface Notification {
  id: string;
  type: 'warning' | 'info' | 'success' | 'reminder';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionable: boolean;
  action?: () => void;
  actionLabel?: string;
}

interface NotificationSettings {
  stressAlerts: boolean;
  tradingReminders: boolean;
  meditationReminders: boolean;
  performanceInsights: boolean;
  dailyReports: boolean;
}

const NotificationCenter: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>({
    stressAlerts: true,
    tradingReminders: true,
    meditationReminders: true,
    performanceInsights: true,
    dailyReports: true
  });
  const [snackbar, setSnackbar] = useState({ open: false, message: '' });
  
  const { psychologyData, correlationMetrics, insights } = usePsychologyTradingSync();

  // Generate smart notifications based on psychology data
  useEffect(() => {
    const generateNotifications = () => {
      const newNotifications: Notification[] = [];
      const today = new Date().toISOString().split('T')[0];
      const todayData = psychologyData.find(d => d.date === today);

      // Stress level alerts
      if (settings.stressAlerts && todayData && todayData.stressLevel > 7) {
        newNotifications.push({
          id: `stress-${Date.now()}`,
          type: 'warning',
          title: 'Mức stress cao!',
          message: `Mức stress hiện tại: ${todayData.stressLevel}/10. Hãy nghỉ ngơi và thực hiện bài tập thở sâu.`,
          timestamp: new Date(),
          read: false,
          actionable: true,
          action: () => {
            // Navigate to meditation or breathing exercise
            setSnackbar({ open: true, message: 'Chuyển đến bài tập thở sâu...' });
          },
          actionLabel: 'Bài tập thở'
        });
      }

      // Low confidence alerts
      if (settings.stressAlerts && todayData && todayData.confidenceLevel < 4) {
        newNotifications.push({
          id: `confidence-${Date.now()}`,
          type: 'info',
          title: 'Mức tự tin thấp',
          message: `Mức tự tin: ${todayData.confidenceLevel}/10. Hãy xem lại các thành công gần đây.`,
          timestamp: new Date(),
          read: false,
          actionable: true,
          action: () => {
            setSnackbar({ open: true, message: 'Xem lại journal thành công...' });
          },
          actionLabel: 'Xem thành công'
        });
      }

      // Meditation reminders
      if (settings.meditationReminders && todayData && todayData.meditationMinutes === 0) {
        const currentHour = new Date().getHours();
        if (currentHour >= 8 && currentHour <= 20) { // Only during waking hours
          newNotifications.push({
            id: `meditation-${Date.now()}`,
            type: 'reminder',
            title: 'Nhắc nhở thiền định',
            message: 'Bạn chưa thiền hôm nay. Hãy dành 10 phút để thiền định.',
            timestamp: new Date(),
            read: false,
            actionable: true,
            action: () => {
              setSnackbar({ open: true, message: 'Mở timer thiền định...' });
            },
            actionLabel: 'Thiền ngay'
          });
        }
      }

      // Trading readiness check
      if (settings.tradingReminders && todayData) {
        const currentHour = new Date().getHours();
        const isMarketHours = currentHour >= 8 && currentHour <= 17;
        
        if (isMarketHours && (todayData.stressLevel > 6 || todayData.confidenceLevel < 5)) {
          newNotifications.push({
            id: `trading-readiness-${Date.now()}`,
            type: 'warning',
            title: 'Cảnh báo sẵn sàng giao dịch',
            message: 'Trạng thái tâm lý chưa tối ưu cho giao dịch. Hãy cân nhắc nghỉ ngơi.',
            timestamp: new Date(),
            read: false,
            actionable: true,
            action: () => {
              setSnackbar({ open: true, message: 'Mở daily psychology check...' });
            },
            actionLabel: 'Kiểm tra tâm lý'
          });
        }
      }

      // Performance insights
      if (settings.performanceInsights && insights.length > 0) {
        const latestInsight = insights[0];
        if (latestInsight.includes('🔴') || latestInsight.includes('😤')) {
          newNotifications.push({
            id: `insight-${Date.now()}`,
            type: 'info',
            title: 'Insight mới từ AI',
            message: latestInsight.substring(0, 100) + '...',
            timestamp: new Date(),
            read: false,
            actionable: true,
            action: () => {
              setSnackbar({ open: true, message: 'Mở advanced analytics...' });
            },
            actionLabel: 'Xem chi tiết'
          });
        }
      }

      // Daily report reminder
      if (settings.dailyReports) {
        const currentHour = new Date().getHours();
        if (currentHour === 18) { // 6 PM reminder
          newNotifications.push({
            id: `daily-report-${Date.now()}`,
            type: 'reminder',
            title: 'Báo cáo cuối ngày',
            message: 'Đã đến lúc hoàn thành báo cáo tâm lý cuối ngày.',
            timestamp: new Date(),
            read: false,
            actionable: true,
            action: () => {
              setSnackbar({ open: true, message: 'Mở daily psychology check...' });
            },
            actionLabel: 'Hoàn thành'
          });
        }
      }

      // Only add new notifications that don't already exist
      const existingIds = notifications.map(n => n.id);
      const uniqueNew = newNotifications.filter(n => !existingIds.includes(n.id));
      
      if (uniqueNew.length > 0) {
        setNotifications(prev => [...uniqueNew, ...prev].slice(0, 50)); // Keep max 50 notifications
      }
    };

    generateNotifications();
    
    // Check every 5 minutes
    const interval = setInterval(generateNotifications, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [psychologyData, insights, settings, notifications]);

  const unreadCount = notifications.filter(n => !n.read).length;

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  };

  const deleteNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return <WarningIcon color="warning" />;
      case 'success': return <SuccessIcon color="success" />;
      case 'reminder': return <ScheduleIcon color="info" />;
      default: return <InfoIcon color="info" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'warning': return 'warning';
      case 'success': return 'success';
      case 'reminder': return 'info';
      default: return 'default';
    }
  };

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Badge badgeContent={unreadCount} color="error">
            <NotificationIcon fontSize="large" color="primary" />
          </Badge>
          <Box>
            <Typography variant="h5" fontWeight={600}>
              🔔 Trung tâm thông báo
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Thông báo thông minh về tâm lý và giao dịch
            </Typography>
          </Box>
        </Box>

        <Box display="flex" gap={1}>
          {unreadCount > 0 && (
            <Button size="small" onClick={markAllAsRead}>
              Đánh dấu đã đọc
            </Button>
          )}
          <Tooltip title="Cài đặt thông báo">
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Notification Settings */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            ⚙️ Cài đặt thông báo
          </Typography>
          
          <Box display="flex" flexWrap="wrap" gap={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.stressAlerts}
                  onChange={(e) => setSettings(prev => ({ ...prev, stressAlerts: e.target.checked }))}
                />
              }
              label="Cảnh báo stress"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.tradingReminders}
                  onChange={(e) => setSettings(prev => ({ ...prev, tradingReminders: e.target.checked }))}
                />
              }
              label="Nhắc nhở giao dịch"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.meditationReminders}
                  onChange={(e) => setSettings(prev => ({ ...prev, meditationReminders: e.target.checked }))}
                />
              }
              label="Nhắc nhở thiền"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.performanceInsights}
                  onChange={(e) => setSettings(prev => ({ ...prev, performanceInsights: e.target.checked }))}
                />
              }
              label="Insights hiệu suất"
            />
            <FormControlLabel
              control={
                <Switch
                  checked={settings.dailyReports}
                  onChange={(e) => setSettings(prev => ({ ...prev, dailyReports: e.target.checked }))}
                />
              }
              label="Báo cáo hàng ngày"
            />
          </Box>
        </CardContent>
      </Card>

      {/* Notifications List */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 Thông báo ({notifications.length})
          </Typography>
          
          {notifications.length === 0 ? (
            <Alert severity="info">
              Không có thông báo nào. Hệ thống sẽ tự động tạo thông báo dựa trên dữ liệu tâm lý của bạn.
            </Alert>
          ) : (
            <List>
              {notifications.map((notification, index) => (
                <React.Fragment key={notification.id}>
                  <ListItem
                    sx={{
                      backgroundColor: notification.read ? 'transparent' : 'action.hover',
                      borderRadius: 1,
                      mb: 1
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar sx={{ backgroundColor: `${getNotificationColor(notification.type)}.light` }}>
                        {getNotificationIcon(notification.type)}
                      </Avatar>
                    </ListItemAvatar>
                    
                    <ListItemText
                      primary={
                        <Box display="flex" alignItems="center" gap={1}>
                          <Typography variant="subtitle2" fontWeight={600}>
                            {notification.title}
                          </Typography>
                          {!notification.read && (
                            <Chip label="Mới" size="small" color="primary" />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary" mb={1}>
                            {notification.message}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {notification.timestamp.toLocaleTimeString('vi-VN')}
                          </Typography>
                        </Box>
                      }
                    />
                    
                    <Box display="flex" flexDirection="column" gap={1}>
                      {notification.actionable && notification.action && (
                        <Button
                          size="small"
                          variant="outlined"
                          onClick={() => {
                            notification.action!();
                            markAsRead(notification.id);
                          }}
                        >
                          {notification.actionLabel}
                        </Button>
                      )}
                      
                      <Box display="flex" gap={1}>
                        {!notification.read && (
                          <Button
                            size="small"
                            onClick={() => markAsRead(notification.id)}
                          >
                            Đánh dấu đã đọc
                          </Button>
                        )}
                        <IconButton
                          size="small"
                          onClick={() => deleteNotification(notification.id)}
                        >
                          <CloseIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </Box>
                  </ListItem>
                  
                  {index < notifications.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Snackbar for actions */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={3000}
        onClose={() => setSnackbar({ open: false, message: '' })}
        message={snackbar.message}
      />
    </Box>
  );
};

export default NotificationCenter;
