import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Chip,
  Alert,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  LinearProgress,
  Tooltip,
  IconButton,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendIcon,
  Psychology as PsychIcon,
  ShowChart as ChartIcon,
  Insights as InsightIcon,
  Refresh as RefreshIcon,
  Download as ExportIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon
} from '@mui/icons-material';
import { usePsychologyTradingSync } from '../../hooks/usePsychologyTradingSync';

const AdvancedAnalytics: React.FC = () => {
  const [timeRange, setTimeRange] = useState('30'); // days
  const { 
    psychologyData, 
    tradingData, 
    correlationMetrics, 
    insights, 
    loading, 
    error,
    refreshData 
  } = usePsychologyTradingSync();

  const getCorrelationColor = (value: number) => {
    const abs = Math.abs(value);
    if (abs >= 0.7) return 'error';
    if (abs >= 0.5) return 'warning';
    if (abs >= 0.3) return 'info';
    return 'default';
  };

  const getCorrelationLabel = (value: number) => {
    const abs = Math.abs(value);
    if (abs >= 0.7) return 'Rất mạnh';
    if (abs >= 0.5) return 'Mạnh';
    if (abs >= 0.3) return 'Trung bình';
    return 'Yếu';
  };

  const formatCorrelation = (value: number) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  // Filter data by time range
  const filteredPsychData = psychologyData.slice(0, parseInt(timeRange));
  const filteredTradingData = tradingData.slice(0, parseInt(timeRange));

  // Calculate summary stats
  const avgStress = filteredPsychData.reduce((sum, d) => sum + d.stressLevel, 0) / filteredPsychData.length || 0;
  const avgConfidence = filteredPsychData.reduce((sum, d) => sum + d.confidenceLevel, 0) / filteredPsychData.length || 0;
  const avgWinRate = filteredTradingData.reduce((sum, d) => sum + d.winRate, 0) / filteredTradingData.length || 0;
  const totalProfit = filteredTradingData.reduce((sum, d) => sum + d.profit, 0);

  if (loading) {
    return (
      <Box p={3}>
        <Typography variant="h6" gutterBottom>
          📊 Phân tích nâng cao Psychology vs Trading
        </Typography>
        <LinearProgress />
        <Typography variant="body2" color="text.secondary" mt={2}>
          Đang tải và phân tích dữ liệu...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={refreshData}>
          Thử lại
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            📊 Phân tích nâng cao Psychology vs Trading
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Phân tích tương quan giữa trạng thái tâm lý và hiệu suất giao dịch
          </Typography>
        </Box>
        
        <Box display="flex" gap={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Thời gian</InputLabel>
            <Select
              value={timeRange}
              label="Thời gian"
              onChange={(e) => setTimeRange(e.target.value)}
            >
              <MenuItem value="7">7 ngày</MenuItem>
              <MenuItem value="30">30 ngày</MenuItem>
              <MenuItem value="90">90 ngày</MenuItem>
              <MenuItem value="365">1 năm</MenuItem>
            </Select>
          </FormControl>
          
          <Tooltip title="Làm mới dữ liệu">
            <IconButton onClick={refreshData}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="Xuất báo cáo">
            <IconButton>
              <ExportIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <PsychIcon color="primary" />
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {avgStress.toFixed(1)}/10
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Stress trung bình
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <TrendIcon color="success" />
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {avgConfidence.toFixed(1)}/10
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tự tin trung bình
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <ChartIcon color="info" />
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {avgWinRate.toFixed(1)}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tỷ lệ thắng TB
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 3 }}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" gap={2}>
                <InsightIcon color={totalProfit >= 0 ? 'success' : 'error'} />
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    {totalProfit.toLocaleString()}₫
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tổng P&L
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Correlation Analysis */}
      <Grid container spacing={3} mb={4}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🔗 Phân tích tương quan
              </Typography>
              
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <Box p={2} border={1} borderColor="divider" borderRadius={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" fontWeight={600}>
                        Stress vs Tỷ lệ thắng
                      </Typography>
                      <Chip 
                        label={getCorrelationLabel(correlationMetrics.stressVsWinRate)}
                        color={getCorrelationColor(correlationMetrics.stressVsWinRate)}
                        size="small"
                      />
                    </Box>
                    <Typography variant="h6" color="primary">
                      {formatCorrelation(correlationMetrics.stressVsWinRate)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(correlationMetrics.stressVsWinRate) * 100}
                      color={getCorrelationColor(correlationMetrics.stressVsWinRate)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6 }}>
                  <Box p={2} border={1} borderColor="divider" borderRadius={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" fontWeight={600}>
                        Tự tin vs Lợi nhuận
                      </Typography>
                      <Chip 
                        label={getCorrelationLabel(correlationMetrics.confidenceVsProfit)}
                        color={getCorrelationColor(correlationMetrics.confidenceVsProfit)}
                        size="small"
                      />
                    </Box>
                    <Typography variant="h6" color="primary">
                      {formatCorrelation(correlationMetrics.confidenceVsProfit)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(correlationMetrics.confidenceVsProfit) * 100}
                      color={getCorrelationColor(correlationMetrics.confidenceVsProfit)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6 }}>
                  <Box p={2} border={1} borderColor="divider" borderRadius={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" fontWeight={600}>
                        Thiền vs Hiệu suất
                      </Typography>
                      <Chip 
                        label={getCorrelationLabel(correlationMetrics.meditationVsPerformance)}
                        color={getCorrelationColor(correlationMetrics.meditationVsPerformance)}
                        size="small"
                      />
                    </Box>
                    <Typography variant="h6" color="primary">
                      {formatCorrelation(correlationMetrics.meditationVsPerformance)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(correlationMetrics.meditationVsPerformance) * 100}
                      color={getCorrelationColor(correlationMetrics.meditationVsPerformance)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>

                <Grid size={{ xs: 12, sm: 6 }}>
                  <Box p={2} border={1} borderColor="divider" borderRadius={2}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                      <Typography variant="body2" fontWeight={600}>
                        Cảm xúc vs Giao dịch
                      </Typography>
                      <Chip 
                        label={getCorrelationLabel(correlationMetrics.emotionalStateImpact)}
                        color={getCorrelationColor(correlationMetrics.emotionalStateImpact)}
                        size="small"
                      />
                    </Box>
                    <Typography variant="h6" color="primary">
                      {formatCorrelation(correlationMetrics.emotionalStateImpact)}
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={Math.abs(correlationMetrics.emotionalStateImpact) * 100}
                      color={getCorrelationColor(correlationMetrics.emotionalStateImpact)}
                      sx={{ mt: 1 }}
                    />
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* AI Insights */}
        <Grid size={{ xs: 12, md: 4 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🤖 AI Insights
              </Typography>
              
              <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>
                {insights.map((insight, index) => (
                  <Alert 
                    key={index}
                    severity={
                      insight.includes('🔴') ? 'error' :
                      insight.includes('🟢') ? 'success' :
                      insight.includes('🧘') ? 'info' :
                      insight.includes('⚖️') ? 'warning' : 'info'
                    }
                    sx={{ mb: 2 }}
                    icon={
                      insight.includes('🔴') ? <WarningIcon /> :
                      insight.includes('🟢') ? <SuccessIcon /> :
                      <InsightIcon />
                    }
                  >
                    <Typography variant="body2">
                      {insight}
                    </Typography>
                  </Alert>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Data Summary */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📈 Tóm tắt dữ liệu ({timeRange} ngày gần nhất)
          </Typography>
          
          <Grid container spacing={3}>
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Dữ liệu tâm lý
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • {filteredPsychData.length} ngày có dữ liệu tâm lý
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Stress trung bình: {avgStress.toFixed(1)}/10
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Tự tin trung bình: {avgConfidence.toFixed(1)}/10
              </Typography>
            </Grid>
            
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="subtitle2" fontWeight={600} gutterBottom>
                Dữ liệu giao dịch
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • {filteredTradingData.length} ngày có giao dịch
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Tỷ lệ thắng trung bình: {avgWinRate.toFixed(1)}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                • Tổng P&L: {totalProfit.toLocaleString()}₫
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default AdvancedAnalytics;
