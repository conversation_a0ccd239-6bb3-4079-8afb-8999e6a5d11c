import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AddTransactionDialog from '../AddTransactionDialog';

// Mock the API functions
jest.mock('../../../api/financial', () => ({
  getDefaultCategories: jest.fn(() => [
    // Income categories
    { name: 'Lương', type: 'income', color: '#4caf50', icon: '💰', subcategories: ['Lương cơ bản', 'Thưởng', '<PERSON><PERSON> cấp'] },
    { name: 'Đầ<PERSON> tư', type: 'income', color: '#2196f3', icon: '📈', subcategories: ['<PERSON><PERSON> phiếu', 'Crypto', 'Bất động sản', 'Lãi ngân hàng', 'Binomo', 'Forex'] },
    
    // Expense categories
    { name: 'Ăn uống', type: 'expense', color: '#f44336', icon: '🍽️', subcategories: ['Ăn sáng', 'Ăn trưa', 'Ăn tối', '<PERSON><PERSON> uống'] },
    { name: '<PERSON><PERSON> lại', type: 'expense', color: '#9c27b0', icon: '🚗', subcategories: ['Xăng xe', 'Taxi/Grab', 'Xe bus', 'Bảo dưỡng xe'] },
    { name: 'Chỗ ở', type: 'expense', color: '#8d6e63', icon: '🏠', subcategories: ['Phòng trọ', 'Khách sạn', 'Homestay', 'Airbnb', 'Nhà nghỉ', 'Resort', 'Tiền thuê nhà'] },
  ]),
  getAllAssets: jest.fn(() => Promise.resolve([])),
  createTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  updateTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  formatCurrency: jest.fn((amount) => `${amount.toLocaleString()}₫`),
}));

describe('AddTransactionDialog - Accommodation Category', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    onTransactionAdded: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should show accommodation category in expense categories', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Expense is default type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    expect(typeSelect).toHaveValue('expense');

    // Open category dropdown
    const categorySelect = screen.getByLabelText('Danh mục');
    fireEvent.mouseDown(categorySelect);

    // Verify accommodation category is present
    expect(screen.getByText('🏠 Chỗ ở')).toBeInTheDocument();
  });

  test('should show all accommodation subcategories when selected', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select accommodation category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    // Check subcategories dropdown is enabled and contains all options
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      expect(subcategorySelect).not.toBeDisabled();
      
      fireEvent.mouseDown(subcategorySelect);
    });

    // Verify all accommodation subcategories
    expect(screen.getByText('Phòng trọ')).toBeInTheDocument();
    expect(screen.getByText('Khách sạn')).toBeInTheDocument();
    expect(screen.getByText('Homestay')).toBeInTheDocument();
    expect(screen.getByText('Airbnb')).toBeInTheDocument();
    expect(screen.getByText('Nhà nghỉ')).toBeInTheDocument();
    expect(screen.getByText('Resort')).toBeInTheDocument();
    expect(screen.getByText('Tiền thuê nhà')).toBeInTheDocument();
  });

  test('should be able to select hotel subcategory', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select accommodation category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    // Select hotel subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Khách sạn'));
    });

    // Verify hotel is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Khách sạn');
  });

  test('should be able to select homestay subcategory', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select accommodation category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    // Select homestay subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Homestay'));
    });

    // Verify homestay is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Homestay');
  });

  test('should be able to select Airbnb subcategory', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select accommodation category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    // Select Airbnb subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Airbnb'));
    });

    // Verify Airbnb is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Airbnb');
  });

  test('should create transaction with hotel accommodation', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with hotel accommodation
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Khách sạn'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '1500000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Khách sạn 5 sao Đà Nẵng 2 đêm' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Chỗ ở',
          subcategory: 'Khách sạn',
          amount: 1500000,
          description: 'Khách sạn 5 sao Đà Nẵng 2 đêm',
        })
      );
    });
  });

  test('should create transaction with rent payment', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with rent payment
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Tiền thuê nhà'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '8000000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Tiền thuê nhà tháng 12/2024' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Chỗ ở',
          subcategory: 'Tiền thuê nhà',
          amount: 8000000,
          description: 'Tiền thuê nhà tháng 12/2024',
        })
      );
    });
  });

  test('should create transaction with Airbnb booking', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with Airbnb booking
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Airbnb'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '2500000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Airbnb Hội An 3 đêm' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Chỗ ở',
          subcategory: 'Airbnb',
          amount: 2500000,
          description: 'Airbnb Hội An 3 đêm',
        })
      );
    });
  });

  test('should not show accommodation category for income type', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Switch to income type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    // Open category dropdown
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
    });

    // Verify accommodation category is not present for income
    expect(screen.queryByText('🏠 Chỗ ở')).not.toBeInTheDocument();
    
    // But should show income categories
    expect(screen.getByText('💰 Lương')).toBeInTheDocument();
    expect(screen.getByText('📈 Đầu tư')).toBeInTheDocument();
  });

  test('should handle accommodation category with recurring transactions', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with recurring rent payment
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Phòng trọ'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '3000000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Tiền phòng trọ hàng tháng' } });

    // Enable recurring transaction
    const recurringSwitch = screen.getByRole('checkbox', { name: 'Giao dịch định kỳ' });
    fireEvent.click(recurringSwitch);

    // Select monthly frequency
    await waitFor(() => {
      const frequencySelect = screen.getByLabelText('Tần suất');
      fireEvent.mouseDown(frequencySelect);
      fireEvent.click(screen.getByText('Hàng tháng'));
    });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data including recurring settings
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Chỗ ở',
          subcategory: 'Phòng trọ',
          amount: 3000000,
          description: 'Tiền phòng trọ hàng tháng',
          is_recurring: true,
          recurring_frequency: 'monthly',
        })
      );
    });
  });
});
