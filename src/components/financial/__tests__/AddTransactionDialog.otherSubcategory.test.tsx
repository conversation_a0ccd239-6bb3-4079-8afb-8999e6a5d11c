import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AddTransactionDialog from '../AddTransactionDialog';
import { getDefaultCategories } from '../../../api/financial';

// Mock the API functions
jest.mock('../../../api/financial', () => ({
  getDefaultCategories: jest.fn(),
  getAllAssets: jest.fn(() => Promise.resolve([])),
  createTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  updateTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  formatCurrency: jest.fn((amount) => `${amount.toLocaleString()}₫`),
}));

describe('AddTransactionDialog - "Khác" Subcategory', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    onTransactionAdded: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock categories with "Khác" in all subcategories
    (getDefaultCategories as jest.Mock).mockReturnValue([
      // Income categories
      { name: 'Lương', type: 'income', color: '#4caf50', icon: '💰', subcategories: ['Lương cơ bản', 'Thưởng', 'Phụ cấp', 'Khác'] },
      { name: 'Đầu tư', type: 'income', color: '#2196f3', icon: '📈', subcategories: ['Cổ phiếu', 'Crypto', 'Bất động sản', 'Lãi ngân hàng', 'Binomo', 'Forex', 'Khác'] },
      { name: 'Kinh doanh', type: 'income', color: '#ff9800', icon: '🏪', subcategories: ['Bán hàng', 'Dịch vụ', 'Freelance', 'Khác'] },
      { name: 'Thu nợ', type: 'income', color: '#8bc34a', icon: '💸', subcategories: ['Thu nợ cá nhân', 'Thu nợ kinh doanh', 'Khác'] },
      
      // Expense categories
      { name: 'Ăn uống', type: 'expense', color: '#f44336', icon: '🍽️', subcategories: ['Ăn sáng', 'Ăn trưa', 'Ăn tối', 'Đồ uống', 'Khác'] },
      { name: 'Đi lại', type: 'expense', color: '#9c27b0', icon: '🚗', subcategories: ['Xăng xe', 'Taxi/Grab', 'Xe bus', 'Bảo dưỡng xe', 'Khác'] },
      { name: 'Mua sắm', type: 'expense', color: '#e91e63', icon: '🛍️', subcategories: ['Quần áo', 'Điện tử', 'Gia dụng', 'Sách', 'Khác'] },
      { name: 'Giải trí', type: 'expense', color: '#673ab7', icon: '🎮', subcategories: ['Phim ảnh', 'Game', 'Du lịch', 'Thể thao', 'Khác'] },
      { name: 'Hóa đơn', type: 'expense', color: '#607d8b', icon: '📄', subcategories: ['Điện', 'Nước', 'Internet', 'Điện thoại', 'Khác'] },
      { name: 'Y tế', type: 'expense', color: '#009688', icon: '🏥', subcategories: ['Khám bệnh', 'Thuốc', 'Bảo hiểm y tế', 'Khác'] },
      { name: 'Giáo dục', type: 'expense', color: '#795548', icon: '📚', subcategories: ['Học phí', 'Sách vở', 'Khóa học online', 'Khác'] },
      { name: 'Chỗ ở', type: 'expense', color: '#8d6e63', icon: '🏠', subcategories: ['Phòng trọ', 'Khách sạn', 'Homestay', 'Airbnb', 'Nhà nghỉ', 'Resort', 'Tiền thuê nhà', 'Khác'] },
      { name: 'Công việc', type: 'expense', color: '#ff9800', icon: '💼', subcategories: ['Thiết kế', 'In ấn', 'Vận chuyển quốc tế', 'Dịch vụ chuyên nghiệp', 'Phí pháp lý', 'Marketing', 'Khác'] },
      { name: 'Đầu tư', type: 'expense', color: '#3f51b5', icon: '📊', subcategories: ['Mua cổ phiếu', 'Mua crypto', 'Đầu tư BĐS', 'Phí giao dịch', 'Binomo', 'Forex', 'Khác'] },
      { name: 'Công nghệ', type: 'expense', color: '#00bcd4', icon: '💻', subcategories: ['Phần mềm', 'Hosting/Domain', 'Thiết bị IT', 'Subscription', 'Khác'] },
      { name: 'Chuyển khoản', type: 'expense', color: '#ff5722', icon: '💳', subcategories: ['Chuyển tiền', 'Rút tiền', 'Phí ngân hàng', 'Khác'] },
    ]);
  });

  test('should show "Khác" option in all income categories', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Switch to income type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    // Test each income category
    const incomeCategories = ['💰 Lương', '📈 Đầu tư', '🏪 Kinh doanh', '💸 Thu nợ'];
    
    for (const category of incomeCategories) {
      // Select category
      await waitFor(() => {
        const categorySelect = screen.getByLabelText('Danh mục');
        fireEvent.mouseDown(categorySelect);
        fireEvent.click(screen.getByText(category));
      });

      // Check subcategories dropdown contains "Khác"
      await waitFor(() => {
        const subcategorySelect = screen.getByLabelText('Danh mục con');
        expect(subcategorySelect).not.toBeDisabled();
        
        fireEvent.mouseDown(subcategorySelect);
      });

      // Verify "Khác" is present
      expect(screen.getByText('Khác')).toBeInTheDocument();
      
      // Close dropdown
      fireEvent.keyDown(document.activeElement || document.body, { key: 'Escape' });
    }
  });

  test('should show "Khác" option in all expense categories', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Expense is default type
    const expenseCategories = [
      '🍽️ Ăn uống', '🚗 Đi lại', '🛍️ Mua sắm', '🎮 Giải trí', 
      '📄 Hóa đơn', '🏥 Y tế', '📚 Giáo dục', '🏠 Chỗ ở',
      '💼 Công việc', '📊 Đầu tư', '💻 Công nghệ', '💳 Chuyển khoản'
    ];
    
    for (const category of expenseCategories) {
      // Select category
      await waitFor(() => {
        const categorySelect = screen.getByLabelText('Danh mục');
        fireEvent.mouseDown(categorySelect);
        fireEvent.click(screen.getByText(category));
      });

      // Check subcategories dropdown contains "Khác"
      await waitFor(() => {
        const subcategorySelect = screen.getByLabelText('Danh mục con');
        expect(subcategorySelect).not.toBeDisabled();
        
        fireEvent.mouseDown(subcategorySelect);
      });

      // Verify "Khác" is present
      expect(screen.getByText('Khác')).toBeInTheDocument();
      
      // Close dropdown
      fireEvent.keyDown(document.activeElement || document.body, { key: 'Escape' });
    }
  });

  test('should be able to select "Khác" for salary income', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Switch to income and select salary
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('💰 Lương'));
    });

    // Select "Khác" subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Khác'));
    });

    // Verify "Khác" is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Khác');
  });

  test('should be able to select "Khác" for food expense', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select food category (expense is default)
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🍽️ Ăn uống'));
    });

    // Select "Khác" subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Khác'));
    });

    // Verify "Khác" is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Khác');
  });

  test('should create transaction with "Khác" subcategory', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with "Khác" subcategory
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🛍️ Mua sắm'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Khác'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '500000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Mua đồ không phân loại được' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Mua sắm',
          subcategory: 'Khác',
          amount: 500000,
          description: 'Mua đồ không phân loại được',
        })
      );
    });
  });

  test('should show "Khác" as last option in subcategory list', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select accommodation category which has many subcategories
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('🏠 Chỗ ở'));
    });

    // Open subcategories dropdown
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
    });

    // Get all menu items
    const menuItems = screen.getAllByRole('option');
    const subcategoryTexts = menuItems.map(item => item.textContent);

    // Verify "Khác" is the last option
    expect(subcategoryTexts[subcategoryTexts.length - 1]).toBe('Khác');
    
    // Verify other subcategories are present before "Khác"
    expect(subcategoryTexts).toContain('Phòng trọ');
    expect(subcategoryTexts).toContain('Khách sạn');
    expect(subcategoryTexts).toContain('Tiền thuê nhà');
  });

  test('should handle "Khác" with different categories consistently', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Test with multiple categories
    const testCases = [
      { category: '🎮 Giải trí', description: 'Chi phí giải trí khác' },
      { category: '💻 Công nghệ', description: 'Chi phí công nghệ khác' },
      { category: '💳 Chuyển khoản', description: 'Phí chuyển khoản khác' }
    ];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      
      // Select category
      await waitFor(() => {
        const categorySelect = screen.getByLabelText('Danh mục');
        fireEvent.mouseDown(categorySelect);
        fireEvent.click(screen.getByText(testCase.category));
      });

      // Select "Khác" subcategory
      await waitFor(() => {
        const subcategorySelect = screen.getByLabelText('Danh mục con');
        fireEvent.mouseDown(subcategorySelect);
        fireEvent.click(screen.getByText('Khác'));
      });

      // Fill other fields
      fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '100000' } });
      fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: testCase.description } });

      // Submit form
      fireEvent.click(screen.getByText('Thêm'));

      // Verify API call
      await waitFor(() => {
        expect(createTransaction).toHaveBeenNthCalledWith(i + 1,
          expect.objectContaining({
            subcategory: 'Khác',
            description: testCase.description,
          })
        );
      });

      // Reset form for next test (if not last iteration)
      if (i < testCases.length - 1) {
        // Reset category to trigger form reset
        await waitFor(() => {
          const categorySelect = screen.getByLabelText('Danh mục');
          fireEvent.mouseDown(categorySelect);
          fireEvent.click(screen.getByText('🍽️ Ăn uống')); // Reset to different category
        });
      }
    }
  });
});
