import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import AddTransactionDialog from '../AddTransactionDialog';

// Mock the API functions
jest.mock('../../../api/financial', () => ({
  getDefaultCategories: jest.fn(() => [
    // Income categories
    { name: 'Lương', type: 'income', color: '#4caf50', icon: '💰', subcategories: ['Lương cơ bản', 'Thưởng', '<PERSON><PERSON> cấp'] },
    { name: '<PERSON><PERSON><PERSON> tư', type: 'income', color: '#2196f3', icon: '📈', subcategories: ['<PERSON><PERSON> phiếu', 'Crypto', 'Bất động sản', '<PERSON><PERSON><PERSON> ngân hàng', 'Binomo', 'Forex'] },
    
    // Expense categories
    { name: 'Ăn uống', type: 'expense', color: '#f44336', icon: '🍽️', subcategories: ['Ăn sáng', 'Ăn trưa', 'Ăn tối', '<PERSON><PERSON> uống'] },
    { name: '<PERSON><PERSON><PERSON> tư', type: 'expense', color: '#3f51b5', icon: '📊', subcategories: ['Mua cổ phiếu', 'Mua crypto', 'Đầu tư BĐS', 'Phí giao dịch', 'Binomo', 'Forex'] },
  ]),
  getAllAssets: jest.fn(() => Promise.resolve([])),
  createTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  updateTransaction: jest.fn(() => Promise.resolve({ id: 1 })),
  formatCurrency: jest.fn((amount) => `${amount.toLocaleString()}₫`),
}));

describe('AddTransactionDialog - Investment Categories', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    onTransactionAdded: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should show Binomo and Forex in income investment subcategories', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select income type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    // Select investment category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📈 Đầu tư'));
    });

    // Check subcategories dropdown is enabled and contains new options
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      expect(subcategorySelect).not.toBeDisabled();
      
      fireEvent.mouseDown(subcategorySelect);
    });

    // Verify all subcategories including new ones
    expect(screen.getByText('Cổ phiếu')).toBeInTheDocument();
    expect(screen.getByText('Crypto')).toBeInTheDocument();
    expect(screen.getByText('Bất động sản')).toBeInTheDocument();
    expect(screen.getByText('Lãi ngân hàng')).toBeInTheDocument();
    expect(screen.getByText('Binomo')).toBeInTheDocument();
    expect(screen.getByText('Forex')).toBeInTheDocument();
  });

  test('should show Binomo and Forex in expense investment subcategories', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select expense type (default)
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    expect(typeSelect).toHaveValue('expense');

    // Select investment category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📊 Đầu tư'));
    });

    // Check subcategories dropdown is enabled and contains new options
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      expect(subcategorySelect).not.toBeDisabled();
      
      fireEvent.mouseDown(subcategorySelect);
    });

    // Verify all subcategories including new ones
    expect(screen.getByText('Mua cổ phiếu')).toBeInTheDocument();
    expect(screen.getByText('Mua crypto')).toBeInTheDocument();
    expect(screen.getByText('Đầu tư BĐS')).toBeInTheDocument();
    expect(screen.getByText('Phí giao dịch')).toBeInTheDocument();
    expect(screen.getByText('Binomo')).toBeInTheDocument();
    expect(screen.getByText('Forex')).toBeInTheDocument();
  });

  test('should be able to select Binomo as subcategory for income', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select income type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    // Select investment category
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📈 Đầu tư'));
    });

    // Select Binomo subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Binomo'));
    });

    // Verify Binomo is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Binomo');
  });

  test('should be able to select Forex as subcategory for expense', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Select investment category (expense is default)
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📊 Đầu tư'));
    });

    // Select Forex subcategory
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Forex'));
    });

    // Verify Forex is selected
    const subcategorySelect = screen.getByLabelText('Danh mục con');
    expect(subcategorySelect).toHaveValue('Forex');
  });

  test('should create transaction with Binomo subcategory', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with Binomo subcategory
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📈 Đầu tư'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Binomo'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '1000000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Lợi nhuận từ Binomo' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'income',
          category: 'Đầu tư',
          subcategory: 'Binomo',
          amount: 1000000,
          description: 'Lợi nhuận từ Binomo',
        })
      );
    });
  });

  test('should create transaction with Forex subcategory', async () => {
    const { createTransaction } = require('../../../api/financial');
    
    render(<AddTransactionDialog {...mockProps} />);

    // Fill in form with Forex subcategory (expense is default)
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📊 Đầu tư'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Forex'));
    });

    // Fill other required fields
    fireEvent.change(screen.getByLabelText('Số tiền (VNĐ)'), { target: { value: '500000' } });
    fireEvent.change(screen.getByLabelText('Mô tả giao dịch'), { target: { value: 'Đầu tư Forex' } });

    // Submit form
    fireEvent.click(screen.getByText('Thêm'));

    // Verify API was called with correct data
    await waitFor(() => {
      expect(createTransaction).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'expense',
          category: 'Đầu tư',
          subcategory: 'Forex',
          amount: 500000,
          description: 'Đầu tư Forex',
        })
      );
    });
  });

  test('should reset subcategory when switching between income and expense investment', async () => {
    render(<AddTransactionDialog {...mockProps} />);

    // Start with expense and select investment + Binomo
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📊 Đầu tư'));
    });

    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
      fireEvent.click(screen.getByText('Binomo'));
    });

    // Verify Binomo is selected
    expect(screen.getByLabelText('Danh mục con')).toHaveValue('Binomo');

    // Switch to income type
    const typeSelect = screen.getByLabelText('Loại giao dịch');
    fireEvent.mouseDown(typeSelect);
    fireEvent.click(screen.getByText('📈 Thu nhập'));

    // Subcategory should be reset
    await waitFor(() => {
      expect(screen.getByLabelText('Danh mục con')).toHaveValue('');
    });

    // Should be able to select investment category again
    await waitFor(() => {
      const categorySelect = screen.getByLabelText('Danh mục');
      fireEvent.mouseDown(categorySelect);
      fireEvent.click(screen.getByText('📈 Đầu tư'));
    });

    // Should show income investment subcategories including Binomo and Forex
    await waitFor(() => {
      const subcategorySelect = screen.getByLabelText('Danh mục con');
      fireEvent.mouseDown(subcategorySelect);
    });

    expect(screen.getByText('Binomo')).toBeInTheDocument();
    expect(screen.getByText('Forex')).toBeInTheDocument();
  });
});
