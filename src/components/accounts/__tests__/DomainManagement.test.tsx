import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DomainManagement from '../DomainManagement';

// Mock the API functions
jest.mock('../../api/accounts', () => ({
  getAllDomains: jest.fn(() => Promise.resolve([])),
  createDomain: jest.fn(),
  updateDomain: jest.fn(),
  deleteDomain: jest.fn(),
}));

// Mock the notification utilities
jest.mock('../../utils/notifications', () => ({
  NotificationManager: jest.fn().mockImplementation(() => ({
    start: jest.fn(),
    stop: jest.fn(),
    dismissNotification: jest.fn(),
    dismissAll: jest.fn(),
    updateSettings: jest.fn(),
  })),
  defaultNotificationSettings: {
    enabled: true,
    checkInterval: 60,
    dismissAfter: 24,
    thresholds: {
      critical: 3,
      warning: 7,
      info: 30,
    },
    methods: {
      browser: true,
      sound: false,
      email: false,
    },
  },
  checkExpiringDomains: jest.fn(() => []),
}));

// Mock NotificationCenter component
jest.mock('../ui/NotificationCenter', () => {
  return function MockNotificationCenter({ onDismiss, onDismissAll }: any) {
    return (
      <div data-testid="notification-center">
        <button onClick={() => onDismiss('test-notification-1')}>
          Dismiss Single
        </button>
        <button onClick={onDismissAll}>
          Dismiss All
        </button>
      </div>
    );
  };
});

describe('DomainManagement - Notification Close Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should dismiss single notification when close button is clicked', async () => {
    render(<DomainManagement />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('notification-center')).toBeInTheDocument();
    });

    // Click dismiss single notification
    const dismissButton = screen.getByText('Dismiss Single');
    fireEvent.click(dismissButton);

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/Đã ẩn thông báo/)).toBeInTheDocument();
    });
  });

  test('should dismiss all notifications when dismiss all button is clicked', async () => {
    render(<DomainManagement />);
    
    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByTestId('notification-center')).toBeInTheDocument();
    });

    // Click dismiss all notifications
    const dismissAllButton = screen.getByText('Dismiss All');
    fireEvent.click(dismissAllButton);

    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/Đã ẩn tất cả/)).toBeInTheDocument();
    });
  });

  test('should close expiry banner when close button is clicked', async () => {
    // Mock expiring domains to show banner
    const { checkExpiringDomains } = require('../../utils/notifications');
    checkExpiringDomains.mockReturnValue([
      {
        id: 'test-1',
        message: 'Domain example.com sắp hết hạn',
        severity: 'warning',
        accountName: 'example.com',
        daysRemaining: 5,
        timestamp: new Date(),
      },
    ]);

    render(<DomainManagement />);

    // Wait for banner to appear
    await waitFor(() => {
      expect(screen.getByText(/Domain Expiry Alerts/)).toBeInTheDocument();
    });

    // Find and click close button
    const closeButton = screen.getByLabelText('Đóng thông báo');
    fireEvent.click(closeButton);

    // Banner should be hidden
    await waitFor(() => {
      expect(screen.queryByText(/Domain Expiry Alerts/)).not.toBeInTheDocument();
    });

    // Should show confirmation message
    await waitFor(() => {
      expect(screen.getByText(/Đã ẩn thông báo domain sắp hết hạn/)).toBeInTheDocument();
    });
  });

  test('should close snackbar when close button is clicked', async () => {
    render(<DomainManagement />);
    
    // Trigger a snackbar by clicking dismiss all
    const dismissAllButton = screen.getByText('Dismiss All');
    fireEvent.click(dismissAllButton);

    // Wait for snackbar to appear
    await waitFor(() => {
      expect(screen.getByRole('alert')).toBeInTheDocument();
    });

    // Find and click close button on snackbar
    const snackbarCloseButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(snackbarCloseButton);

    // Snackbar should be hidden
    await waitFor(() => {
      expect(screen.queryByRole('alert')).not.toBeInTheDocument();
    });
  });
});
