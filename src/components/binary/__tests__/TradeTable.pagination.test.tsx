import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TradeTable from '../TradeTable';
import { BinaryOptionTrade } from '../../../types';

// Mock the API functions
jest.mock('../../../api/binaryTrades', () => ({
  deleteBinaryTrade: jest.fn(),
  formatCurrency: (amount: number) => `$${amount.toLocaleString()}`,
  formatDateTime: (date: string) => new Date(date).toLocaleString(),
}));

// Mock other components
jest.mock('../TradeDetailDialog', () => () => <div>TradeDetailDialog</div>);
jest.mock('../EditTradeDialog', () => () => <div>EditTradeDialog</div>);
jest.mock('../AdvancedFilters', () => () => <div>AdvancedFilters</div>);
jest.mock('../MarkdownRenderer', () => ({ content }: any) => <div>{content}</div>);

// Create mock trades data
const createMockTrades = (count: number): BinaryOptionTrade[] => {
  return Array.from({ length: count }, (_, index) => ({
    id: index + 1,
    currency_pair: `EUR/USD`,
    prediction: index % 2 === 0 ? 'UP' : 'DOWN',
    trade_amount: 100,
    profit: index % 3 === 0 ? 85 : -100,
    profit_percentage: index % 3 === 0 ? 85 : -100,
    trade_status: index % 3 === 0 ? 'win' : index % 3 === 1 ? 'loss' : 'draw',
    entry_datetime: `2024-01-${String(index + 1).padStart(2, '0')}T10:00:00Z`,
    notes: `Trade note ${index + 1}`,
    market_condition: ['trending'],
    signal: ['rsi_oversold'],
    result: ['profit'],
  }));
};

const mockProps = {
  onTradeUpdated: jest.fn(),
  onTradeDeleted: jest.fn(),
  onAdvancedFiltersChange: jest.fn(),
};

describe('TradeTable - Pagination Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should display pagination when there are more than 10 trades', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Should show pagination component
    expect(screen.getByText('Số dòng mỗi trang:')).toBeInTheDocument();
    
    // Should show page info
    expect(screen.getByText(/1–10 trong 25/)).toBeInTheDocument();
    
    // Should show page indicator in header
    expect(screen.getByText('Trang 1/3')).toBeInTheDocument();
  });

  test('should not display pagination when there are 10 or fewer trades', async () => {
    const trades = createMockTrades(5);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Should not show pagination component
    expect(screen.queryByText('Số dòng mỗi trang:')).not.toBeInTheDocument();
    
    // Should not show page indicator in header
    expect(screen.queryByText(/Trang/)).not.toBeInTheDocument();
  });

  test('should display only trades for current page', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Should show first 10 trades (default page size)
    const tableRows = screen.getAllByRole('row');
    // Header row + 10 data rows = 11 total rows
    expect(tableRows).toHaveLength(11);
  });

  test('should navigate to next page when next button is clicked', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Click next page button
    const nextButton = screen.getByRole('button', { name: /next page/i });
    fireEvent.click(nextButton);

    await waitFor(() => {
      // Should show page 2 info
      expect(screen.getByText(/11–20 trong 25/)).toBeInTheDocument();
      expect(screen.getByText('Trang 2/3')).toBeInTheDocument();
    });
  });

  test('should change rows per page when dropdown is changed', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Find and click the rows per page dropdown
    const rowsPerPageSelect = screen.getByDisplayValue('10');
    fireEvent.mouseDown(rowsPerPageSelect);
    
    // Select 25 rows per page
    const option25 = screen.getByRole('option', { name: '25' });
    fireEvent.click(option25);

    await waitFor(() => {
      // Should show all 25 trades on one page
      expect(screen.getByText(/1–25 trong 25/)).toBeInTheDocument();
      
      // Should not show page indicator since all fits on one page
      expect(screen.queryByText(/Trang/)).not.toBeInTheDocument();
    });
  });

  test('should reset to first page when filters change', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Go to page 2
    const nextButton = screen.getByRole('button', { name: /next page/i });
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('Trang 2/3')).toBeInTheDocument();
    });

    // Change filter (this should reset to page 1)
    const statusFilter = screen.getByLabelText('Trạng thái');
    fireEvent.mouseDown(statusFilter);
    const winOption = screen.getByRole('option', { name: 'Thắng' });
    fireEvent.click(winOption);

    await waitFor(() => {
      // Should be back to page 1
      expect(screen.queryByText('Trang 2/3')).not.toBeInTheDocument();
    });
  });

  test('should show correct trade count in header', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    expect(screen.getByText('25 giao dịch')).toBeInTheDocument();
  });

  test('should show current range indicator in table header', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Should show range indicator in table header
    expect(screen.getByText('1-10')).toBeInTheDocument();

    // Go to page 2
    const nextButton = screen.getByRole('button', { name: /next page/i });
    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('11-20')).toBeInTheDocument();
    });
  });

  test('should handle empty filtered results correctly', async () => {
    const trades = createMockTrades(25);
    
    render(
      <TradeTable
        trades={trades}
        allTrades={trades}
        {...mockProps}
      />
    );

    // Apply filter that returns no results
    const pairFilter = screen.getByDisplayValue('Tất cả');
    fireEvent.mouseDown(pairFilter);
    
    // This should filter out all trades since we only have EUR/USD
    const nonExistentOption = screen.getByRole('option', { name: 'Tất cả' });
    fireEvent.click(nonExistentOption);

    // Should show empty state
    expect(screen.getByText('📊 Không tìm thấy giao dịch nào')).toBeInTheDocument();
    
    // Should not show pagination
    expect(screen.queryByText('Số dòng mỗi trang:')).not.toBeInTheDocument();
  });
});
