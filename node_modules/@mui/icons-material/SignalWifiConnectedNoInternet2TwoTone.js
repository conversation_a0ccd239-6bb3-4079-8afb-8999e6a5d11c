"use strict";
"use client";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _createSvgIcon = _interopRequireDefault(require("./utils/createSvgIcon"));
var _jsxRuntime = require("react/jsx-runtime");
var _default = exports.default = (0, _createSvgIcon.default)([/*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  fillOpacity: ".3",
  d: "M18.31 12h2.67L24 8.98C20.93 5.9 16.69 4 12 4S3.07 5.9 0 8.98l4.23 4.24C6.22 11.23 8.97 10 12 10c2.35 0 4.52.74 6.31 2"
}, "0"), /*#__PURE__*/(0, _jsxRuntime.jsx)("path", {
  d: "M12 10c-3.03 0-5.78 1.23-7.77 3.22L12 21v-9h6.32c-1.8-1.26-3.97-2-6.32-2m9 5.41L19.59 14l-2.09 2.09L15.41 14 14 15.41l2.09 2.09L14 19.59 15.41 21l2.09-2.08L19.59 21 21 19.59l-2.08-2.09z"
}, "1")], 'SignalWifiConnectedNoInternet2TwoTone');