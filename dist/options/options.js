import{t as e,R as n}from"../assets/theme-Bc-mp0-v.js";import{r as s,j as a,B as t,T as i,Z as r,C as l,cQ as c,b as o,o as h,F as d,p as x,S as g,M as u,a2 as j,a3 as b,s as m,cR as v,b2 as p,L as y,d as f,cS as C,h as S,I as k,X as T,Y as A,i as z,cG as D,cH as W,bd as B,ab as M,a0 as w,a1 as I,t as q,u as N,bc as H,cJ as K,bq as O,J as R,cL as L,cT as P}from"../assets/mui-core-CZAN88TW.js";const U=({onSave:e})=>{const[n,y]=s.useState({openaiApiKey:"",jsonServerUrl:"http://localhost:3001",theme:"light",language:"vi",sidebarDefaultOpen:!0,defaultPage:"/dashboard",enableNotifications:!0,autoSave:!0,debugMode:!1}),[f,C]=s.useState(!1);s.useEffect((()=>{(async()=>{try{const e=await chrome.storage.sync.get("generalSettings");e.generalSettings&&y((n=>({...n,...e.generalSettings})))}catch(e){}})()}),[]);const S=(e,n)=>{y((s=>({...s,[e]:n})))};return a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt chung"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Cấu hình cơ bản cho hệ thống AI Coach"}),a.jsxs(r,{container:!0,spacing:3,children:[a.jsx(r,{size:12,children:a.jsxs(l,{children:[a.jsx(c,{title:"🔑 Cấu hình API"}),a.jsx(o,{children:a.jsxs(r,{container:!0,spacing:2,children:[a.jsx(r,{size:12,children:a.jsx(h,{fullWidth:!0,label:"OpenAI API Key",type:"password",value:n.openaiApiKey,onChange:e=>S("openaiApiKey",e.target.value),helperText:"API key để sử dụng tính năng AI",placeholder:"sk-..."})}),a.jsx(r,{size:12,children:a.jsx(h,{fullWidth:!0,label:"JSON Server URL",value:n.jsonServerUrl,onChange:e=>S("jsonServerUrl",e.target.value),helperText:"URL của server cơ sở dữ liệu",placeholder:"http://localhost:3001"})})]})})]})}),a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"🎨 Giao diện"}),a.jsx(o,{children:a.jsxs(r,{container:!0,spacing:2,children:[a.jsx(r,{size:12,children:a.jsxs(d,{fullWidth:!0,children:[a.jsx(x,{children:"Chủ đề"}),a.jsxs(g,{value:n.theme,label:"Chủ đề",onChange:e=>S("theme",e.target.value),children:[a.jsx(u,{value:"light",children:"Sáng"}),a.jsx(u,{value:"dark",children:"Tối"}),a.jsx(u,{value:"auto",children:"Tự động"})]})]})}),a.jsx(r,{size:12,children:a.jsxs(d,{fullWidth:!0,children:[a.jsx(x,{children:"Ngôn ngữ"}),a.jsxs(g,{value:n.language,label:"Ngôn ngữ",onChange:e=>S("language",e.target.value),children:[a.jsx(u,{value:"vi",children:"Tiếng Việt"}),a.jsx(u,{value:"en",children:"English"})]})]})}),a.jsx(r,{size:12,children:a.jsxs(d,{fullWidth:!0,children:[a.jsx(x,{children:"Trang mặc định"}),a.jsxs(g,{value:n.defaultPage,label:"Trang mặc định",onChange:e=>S("defaultPage",e.target.value),children:[a.jsx(u,{value:"/dashboard",children:"Dashboard"}),a.jsx(u,{value:"/todos",children:"Quản lý Todo"}),a.jsx(u,{value:"/financial",children:"Tài chính"}),a.jsx(u,{value:"/printing",children:"In ấn"}),a.jsx(u,{value:"/trading",children:"Giao dịch"})]})]})})]})})]})}),a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"⚙️ Hệ thống"}),a.jsx(o,{children:a.jsxs(t,{display:"flex",flexDirection:"column",gap:2,children:[a.jsx(j,{control:a.jsx(b,{checked:n.sidebarDefaultOpen,onChange:e=>S("sidebarDefaultOpen",e.target.checked)}),label:"Mở sidebar mặc định"}),a.jsx(j,{control:a.jsx(b,{checked:n.enableNotifications,onChange:e=>S("enableNotifications",e.target.checked)}),label:"Bật thông báo"}),a.jsx(j,{control:a.jsx(b,{checked:n.autoSave,onChange:e=>S("autoSave",e.target.checked)}),label:"Tự động lưu"}),a.jsx(j,{control:a.jsx(b,{checked:n.debugMode,onChange:e=>S("debugMode",e.target.checked)}),label:"Chế độ debug"})]})})]})}),a.jsx(r,{size:12,children:a.jsx(l,{children:a.jsx(o,{children:a.jsxs(t,{display:"flex",gap:2,justifyContent:"flex-end",children:[a.jsx(m,{variant:"outlined",startIcon:a.jsx(v,{}),onClick:()=>{y({openaiApiKey:"",jsonServerUrl:"http://localhost:3001",theme:"light",language:"vi",sidebarDefaultOpen:!0,defaultPage:"/dashboard",enableNotifications:!0,autoSave:!0,debugMode:!1}),e("Cài đặt đã được khôi phục về mặc định!","info")},children:"Khôi phục mặc định"}),a.jsx(m,{variant:"contained",startIcon:a.jsx(p,{}),onClick:async()=>{C(!0);try{await chrome.storage.sync.set({generalSettings:n}),e("Cài đặt chung đã được lưu thành công!","success")}catch(s){e("Lỗi khi lưu cài đặt chung!","error")}finally{C(!1)}},disabled:f,children:f?"Đang lưu...":"Lưu cài đặt"})]})})})})]})]})},G=({onSave:e})=>{const[n,v]=s.useState({enabledModules:[{id:"urgentTodos",name:"Todo khẩn cấp",enabled:!0,order:1},{id:"dailyHabits",name:"Thói quen hàng ngày",enabled:!0,order:2},{id:"financialOverview",name:"Tổng quan tài chính",enabled:!0,order:3},{id:"tradingStats",name:"Thống kê giao dịch",enabled:!0,order:4},{id:"printingOrders",name:"Đơn hàng in ấn",enabled:!0,order:5}],compactMode:!1,showCharts:!0,defaultTimeRange:"30d",dailyTargetAmount:35e4,monthlyTargetAmount:1e7,quickAddButtons:["transaction","trade","todo","order"],showDailyAffirmations:!0,affirmationText:"Hôm nay tôi sẽ đạt được mục tiêu của mình!"}),[D,W]=s.useState(!1);s.useEffect((()=>{(async()=>{try{const e=await chrome.storage.sync.get("dashboardSettings");e.dashboardSettings&&v((n=>({...n,...e.dashboardSettings})))}catch(e){}})()}),[]);const B=(e,n)=>{v((s=>({...s,[e]:n})))};return a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt Dashboard"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Tùy chỉnh giao diện và module hiển thị trên Dashboard"}),a.jsxs(r,{container:!0,spacing:3,children:[a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"📊 Module Dashboard"}),a.jsx(o,{children:a.jsx(y,{children:n.enabledModules.map((e=>a.jsxs(f,{divider:!0,sx:{display:"flex",alignItems:"center"},children:[a.jsx(C,{sx:{mr:1,color:"text.secondary"}}),a.jsx(S,{primary:e.name,secondary:`Thứ tự: ${e.order}`,sx:{flex:1}}),a.jsx(t,{sx:{ml:"auto"},children:a.jsx(k,{onClick:()=>{return n=e.id,void v((e=>({...e,enabledModules:e.enabledModules.map((e=>e.id===n?{...e,enabled:!e.enabled}:e))})));var n},color:e.enabled?"primary":"default",size:"small",children:e.enabled?a.jsx(T,{}):a.jsx(A,{})})})]},e.id)))})})]})}),a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"🎨 Hiển thị"}),a.jsx(o,{children:a.jsxs(r,{container:!0,spacing:2,children:[a.jsx(r,{size:12,children:a.jsx(j,{control:a.jsx(b,{checked:n.compactMode,onChange:e=>B("compactMode",e.target.checked)}),label:"Chế độ thu gọn"})}),a.jsx(r,{size:12,children:a.jsx(j,{control:a.jsx(b,{checked:n.showCharts,onChange:e=>B("showCharts",e.target.checked)}),label:"Hiển thị biểu đồ"})}),a.jsx(r,{size:12,children:a.jsx(j,{control:a.jsx(b,{checked:n.showDailyAffirmations,onChange:e=>B("showDailyAffirmations",e.target.checked)}),label:"Hiển thị khẩu hiệu hàng ngày"})}),a.jsx(r,{size:12,children:a.jsxs(d,{fullWidth:!0,children:[a.jsx(x,{children:"Khoảng thời gian mặc định"}),a.jsxs(g,{value:n.defaultTimeRange,label:"Khoảng thời gian mặc định",onChange:e=>B("defaultTimeRange",e.target.value),children:[a.jsx(u,{value:"7d",children:"7 ngày"}),a.jsx(u,{value:"30d",children:"30 ngày"}),a.jsx(u,{value:"90d",children:"90 ngày"})]})]})})]})})]})}),a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"🎯 Mục tiêu"}),a.jsx(o,{children:a.jsxs(r,{container:!0,spacing:2,children:[a.jsx(r,{size:12,children:a.jsx(h,{fullWidth:!0,label:"Mục tiêu hàng ngày",type:"number",value:n.dailyTargetAmount,onChange:e=>B("dailyTargetAmount",Number(e.target.value)),helperText:"Số tiền mục tiêu kiếm được mỗi ngày (VND)"})}),a.jsx(r,{size:12,children:a.jsx(h,{fullWidth:!0,label:"Mục tiêu hàng tháng",type:"number",value:n.monthlyTargetAmount,onChange:e=>B("monthlyTargetAmount",Number(e.target.value)),helperText:"Số tiền mục tiêu kiếm được mỗi tháng (VND)"})})]})})]})}),a.jsx(r,{size:{xs:12,md:6},children:a.jsxs(l,{children:[a.jsx(c,{title:"💪 Khẩu hiệu động viên"}),a.jsx(o,{children:a.jsx(h,{fullWidth:!0,multiline:!0,rows:4,label:"Khẩu hiệu hàng ngày",value:n.affirmationText,onChange:e=>B("affirmationText",e.target.value),helperText:"Tin nhắn động viên hiển thị trên Dashboard",placeholder:"Hôm nay tôi sẽ đạt được mục tiêu của mình!"})})]})}),a.jsx(r,{size:12,children:a.jsxs(l,{children:[a.jsx(c,{title:"⚡ Nút thao tác nhanh"}),a.jsxs(o,{children:[a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Các nút thao tác nhanh hiển thị trên Dashboard"}),a.jsx(t,{display:"flex",gap:1,flexWrap:"wrap",children:["transaction","trade","todo","order","quote"].map((e=>a.jsx(z,{label:e,variant:n.quickAddButtons.includes(e)?"filled":"outlined",onClick:()=>{const s=n.quickAddButtons.includes(e)?n.quickAddButtons.filter((n=>n!==e)):[...n.quickAddButtons,e];B("quickAddButtons",s)},color:"primary"},e)))})]})]})}),a.jsx(r,{size:12,children:a.jsx(l,{children:a.jsx(o,{children:a.jsx(t,{display:"flex",gap:2,justifyContent:"flex-end",children:a.jsx(m,{variant:"contained",startIcon:a.jsx(p,{}),onClick:async()=>{W(!0);try{await chrome.storage.sync.set({dashboardSettings:n}),e("Cài đặt Dashboard đã được lưu thành công!","success")}catch(s){e("Lỗi khi lưu cài đặt Dashboard!","error")}finally{W(!1)}},disabled:D,children:D?"Đang lưu...":"Lưu cài đặt"})})})})})]})]})},E=({onSave:e})=>a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt Tài chính"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Cấu hình các thông số tài chính và kế toán"}),a.jsx(l,{children:a.jsxs(o,{children:[a.jsx(i,{variant:"h6",color:"text.secondary",children:"🚧 Đang phát triển..."}),a.jsx(i,{variant:"body2",color:"text.secondary",children:"Tính năng này sẽ được bổ sung trong phiên bản tiếp theo."})]})})]}),J=({onSave:e})=>a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt Giao dịch"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Cấu hình các thông số giao dịch Binary Options"}),a.jsx(l,{children:a.jsxs(o,{children:[a.jsx(i,{variant:"h6",color:"text.secondary",children:"🚧 Đang phát triển..."}),a.jsx(i,{variant:"body2",color:"text.secondary",children:"Tính năng này sẽ được bổ sung trong phiên bản tiếp theo."})]})})]}),Q=({onSave:e})=>a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt In ấn"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Cấu hình hệ thống quản lý in ấn và vận chuyển"}),a.jsx(l,{children:a.jsxs(o,{children:[a.jsx(i,{variant:"h6",color:"text.secondary",children:"🚧 Đang phát triển..."}),a.jsx(i,{variant:"body2",color:"text.secondary",children:"Tính năng này sẽ được bổ sung trong phiên bản tiếp theo."})]})})]}),V=({onSave:e})=>a.jsxs(t,{children:[a.jsx(i,{variant:"h5",gutterBottom:!0,sx:{fontWeight:600},children:"Cài đặt Sao lưu"}),a.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:3},children:"Quản lý sao lưu và khôi phục dữ liệu"}),a.jsx(l,{children:a.jsxs(o,{children:[a.jsx(i,{variant:"h6",color:"text.secondary",children:"🚧 Đang phát triển..."}),a.jsx(i,{variant:"body2",color:"text.secondary",children:"Tính năng này sẽ được bổ sung trong phiên bản tiếp theo."})]})})]}),$=({children:e,value:n,index:s})=>a.jsx("div",{role:"tabpanel",hidden:n!==s,id:`options-tabpanel-${s}`,"aria-labelledby":`options-tab-${s}`,children:n===s&&a.jsx(t,{sx:{py:3},children:e})}),F=()=>{const[n,r]=s.useState(0),[l,c]=s.useState({open:!1,message:"",severity:"success"}),o=(e,n="success")=>{c({open:!0,message:e,severity:n})},h=[{label:"Cài đặt chung",icon:a.jsx(H,{}),component:a.jsx(U,{onSave:o})},{label:"Dashboard",icon:a.jsx(K,{}),component:a.jsx(G,{onSave:o})},{label:"Tài chính",icon:a.jsx(O,{}),component:a.jsx(E,{onSave:o})},{label:"Giao dịch",icon:a.jsx(R,{}),component:a.jsx(J,{onSave:o})},{label:"In ấn",icon:a.jsx(L,{}),component:a.jsx(Q,{onSave:o})},{label:"Sao lưu",icon:a.jsx(P,{}),component:a.jsx(V,{onSave:o})}];return a.jsxs(D,{theme:e,children:[a.jsx(W,{}),a.jsxs(t,{sx:{minHeight:"100vh",backgroundColor:"background.default"},children:[a.jsxs(B,{maxWidth:"lg",sx:{py:4},children:[a.jsxs(t,{sx:{mb:4},children:[a.jsx(i,{variant:"h3",component:"h1",gutterBottom:!0,sx:{fontWeight:700},children:"⚙️ Cài đặt AI Coach 2.0"}),a.jsx(i,{variant:"h6",color:"text.secondary",children:"Tùy chỉnh và cấu hình hệ thống quản lý của bạn."})]}),a.jsxs(M,{elevation:1,sx:{borderRadius:2,overflow:"hidden"},children:[a.jsx(t,{sx:{borderBottom:1,borderColor:"divider",backgroundColor:"background.paper"},children:a.jsx(w,{value:n,onChange:(e,n)=>{r(n)},variant:"scrollable",scrollButtons:"auto",sx:{px:2},children:h.map(((e,n)=>a.jsx(I,{icon:e.icon,label:e.label,iconPosition:"start",sx:{minHeight:64,textTransform:"none",fontSize:"0.875rem",fontWeight:500}},n)))})}),a.jsx(t,{sx:{backgroundColor:"background.default"},children:h.map(((e,s)=>a.jsx($,{value:n,index:s,children:a.jsx(B,{maxWidth:"md",children:e.component})},s)))})]})]}),a.jsx(q,{open:l.open,autoHideDuration:4e3,onClose:()=>c((e=>({...e,open:!1}))),anchorOrigin:{vertical:"bottom",horizontal:"right"},children:a.jsx(N,{onClose:()=>c((e=>({...e,open:!1}))),severity:l.severity,variant:"filled",sx:{width:"100%"},children:l.message})})]})]})};n.createRoot(document.getElementById("root")).render(a.jsx(F,{}));
