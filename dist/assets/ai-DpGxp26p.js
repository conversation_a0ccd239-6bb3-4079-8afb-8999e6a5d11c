const e="RFC3986",t={RFC1738:e=>String(e).replace(/%20/g,"+"),RFC3986:e=>String(e)},s=Array.isArray,n=(()=>{const e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e})(),r=1024;function o(e,t){if(s(e)){const s=[];for(let n=0;n<e.length;n+=1)s.push(t(e[n]));return s}return t(e)}const i=Object.prototype.hasOwnProperty,a={brackets:e=>String(e)+"[]",comma:"comma",indices:(e,t)=>String(e)+"["+t+"]",repeat:e=>String(e)},l=Array.isArray,c=Array.prototype.push,u=function(e,t){c.apply(e,l(t)?t:[t])},d=Date.prototype.toISOString,h={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:(e,t,s,o,i)=>{if(0===e.length)return e;let a=e;if("symbol"==typeof e?a=Symbol.prototype.toString.call(e):"string"!=typeof e&&(a=String(e)),"iso-8859-1"===s)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));let l="";for(let c=0;c<a.length;c+=r){const e=a.length>=r?a.slice(c,c+r):a,t=[];for(let s=0;s<e.length;++s){let r=e.charCodeAt(s);45===r||46===r||95===r||126===r||r>=48&&r<=57||r>=65&&r<=90||r>=97&&r<=122||"RFC1738"===i&&(40===r||41===r)?t[t.length]=e.charAt(s):r<128?t[t.length]=n[r]:r<2048?t[t.length]=n[192|r>>6]+n[128|63&r]:r<55296||r>=57344?t[t.length]=n[224|r>>12]+n[128|r>>6&63]+n[128|63&r]:(s+=1,r=65536+((1023&r)<<10|1023&e.charCodeAt(s)),t[t.length]=n[240|r>>18]+n[128|r>>12&63]+n[128|r>>6&63]+n[128|63&r])}l+=t.join("")}return l},encodeValuesOnly:!1,format:e,formatter:t[e],indices:!1,serializeDate:e=>d.call(e),skipNulls:!1,strictNullHandling:!1};const f={};function p(e,t,s,n,r,i,a,c,d,m,g,y,w,v,_,b,x,S){let A=e,P=S,$=0,R=!1;for(;void 0!==(P=P.get(f))&&!R;){const t=P.get(e);if($+=1,void 0!==t){if(t===$)throw new RangeError("Cyclic object value");R=!0}void 0===P.get(f)&&($=0)}if("function"==typeof m?A=m(t,A):A instanceof Date?A=null==w?void 0:w(A):"comma"===s&&l(A)&&(A=o(A,(function(e){return e instanceof Date?null==w?void 0:w(e):e}))),null===A){if(i)return d&&!b?d(t,h.encoder,x,"key",v):t;A=""}if("string"==typeof(E=A)||"number"==typeof E||"boolean"==typeof E||"symbol"==typeof E||"bigint"==typeof E||function(e){return!(!e||"object"!=typeof e||!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e)))}(A)){if(d){const e=b?t:d(t,h.encoder,x,"key",v);return[(null==_?void 0:_(e))+"="+(null==_?void 0:_(d(A,h.encoder,x,"value",v)))]}return[(null==_?void 0:_(t))+"="+(null==_?void 0:_(String(A)))]}var E;const I=[];if(void 0===A)return I;let C;if("comma"===s&&l(A))b&&d&&(A=o(A,d)),C=[{value:A.length>0?A.join(",")||null:void 0}];else if(l(m))C=m;else{const e=Object.keys(A);C=g?e.sort(g):e}const O=c?String(t).replace(/\./g,"%2E"):String(t),k=n&&l(A)&&1===A.length?O+"[]":O;if(r&&l(A)&&0===A.length)return k+"[]";for(let o=0;o<C.length;++o){const t=C[o],h="object"==typeof t&&void 0!==t.value?t.value:A[t];if(a&&null===h)continue;const P=y&&c?t.replace(/\./g,"%2E"):t,R=l(A)?"function"==typeof s?s(k,P):k:k+(y?"."+P:"["+P+"]");S.set(e,$);const E=new WeakMap;E.set(f,S),u(I,p(h,R,s,n,r,i,a,c,"comma"===s&&b&&l(A)?null:d,m,g,y,w,v,_,b,x,E))}return I}function m(s,n={}){let r=s;const o=function(s=h){if(void 0!==s.allowEmptyArrays&&"boolean"!=typeof s.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==s.encodeDotInKeys&&"boolean"!=typeof s.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==s.encoder&&void 0!==s.encoder&&"function"!=typeof s.encoder)throw new TypeError("Encoder has to be a function.");const n=s.charset||h.charset;if(void 0!==s.charset&&"utf-8"!==s.charset&&"iso-8859-1"!==s.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let r=e;if(void 0!==s.format){if(!i.call(t,s.format))throw new TypeError("Unknown format option provided.");r=s.format}const o=t[r];let c,u=h.filter;if(("function"==typeof s.filter||l(s.filter))&&(u=s.filter),c=s.arrayFormat&&s.arrayFormat in a?s.arrayFormat:"indices"in s?s.indices?"indices":"repeat":h.arrayFormat,"commaRoundTrip"in s&&"boolean"!=typeof s.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const d=void 0===s.allowDots?1==!!s.encodeDotInKeys||h.allowDots:!!s.allowDots;return{addQueryPrefix:"boolean"==typeof s.addQueryPrefix?s.addQueryPrefix:h.addQueryPrefix,allowDots:d,allowEmptyArrays:"boolean"==typeof s.allowEmptyArrays?!!s.allowEmptyArrays:h.allowEmptyArrays,arrayFormat:c,charset:n,charsetSentinel:"boolean"==typeof s.charsetSentinel?s.charsetSentinel:h.charsetSentinel,commaRoundTrip:!!s.commaRoundTrip,delimiter:void 0===s.delimiter?h.delimiter:s.delimiter,encode:"boolean"==typeof s.encode?s.encode:h.encode,encodeDotInKeys:"boolean"==typeof s.encodeDotInKeys?s.encodeDotInKeys:h.encodeDotInKeys,encoder:"function"==typeof s.encoder?s.encoder:h.encoder,encodeValuesOnly:"boolean"==typeof s.encodeValuesOnly?s.encodeValuesOnly:h.encodeValuesOnly,filter:u,format:r,formatter:o,serializeDate:"function"==typeof s.serializeDate?s.serializeDate:h.serializeDate,skipNulls:"boolean"==typeof s.skipNulls?s.skipNulls:h.skipNulls,sort:"function"==typeof s.sort?s.sort:null,strictNullHandling:"boolean"==typeof s.strictNullHandling?s.strictNullHandling:h.strictNullHandling}}(n);let c,d;"function"==typeof o.filter?(d=o.filter,r=d("",r)):l(o.filter)&&(d=o.filter,c=d);const f=[];if("object"!=typeof r||null===r)return"";const m=a[o.arrayFormat],g="comma"===m&&o.commaRoundTrip;c||(c=Object.keys(r)),o.sort&&c.sort(o.sort);const y=new WeakMap;for(let e=0;e<c.length;++e){const t=c[e];o.skipNulls&&null===r[t]||u(f,p(r[t],t,m,g,o.allowEmptyArrays,o.strictNullHandling,o.skipNulls,o.encodeDotInKeys,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,y))}const w=f.join(o.delimiter);let v=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?v+="utf8=%26%2310003%3B&":v+="utf8=%E2%9C%93&"),w.length>0?v+w:""}const g="4.96.0";let y,w,v,_,b,x,S,A,P,$=!1;class R{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}const E=()=>{y||function(e,t={auto:!1}){if($)throw new Error(`you must \`import 'openai/shims/${e.kind}'\` before importing anything else from openai`);if(y)throw new Error(`can't \`import 'openai/shims/${e.kind}'\` after \`import 'openai/shims/${y}'\``);$=t.auto,y=e.kind,w=e.fetch,v=e.FormData,_=e.File,b=e.ReadableStream,x=e.getMultipartRequestOptions,S=e.getDefaultAgent,A=e.fileFromPath,P=e.isFsReadStream}(function({manuallyImported:e}={}){const t=e?"You may need to use polyfills":"Add one of these imports before your first `import … from 'openai'`:\n- `import 'openai/shims/node'` (if you're running on Node)\n- `import 'openai/shims/web'` (otherwise)\n";let s,n,r,o;try{s=fetch,n=Request,r=Response,o=Headers}catch(i){throw new Error(`this environment is missing the following Web Fetch API type: ${i.message}. ${t}`)}return{kind:"web",fetch:s,Request:n,Response:r,Headers:o,FormData:"undefined"!=typeof FormData?FormData:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${t}`)}},Blob:"undefined"!=typeof Blob?Blob:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${t}`)}},File:"undefined"!=typeof File?File:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${t}`)}},ReadableStream:"undefined"!=typeof ReadableStream?ReadableStream:class{constructor(){throw new Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${t}`)}},getMultipartRequestOptions:async(e,t)=>({...t,body:new R(e)}),getDefaultAgent:e=>{},fileFromPath:()=>{throw new Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:e=>!1}}(),{auto:!0})};E();class I extends Error{}class C extends I{constructor(e,t,s,n){super(`${C.makeMessage(e,t,s)}`),this.status=e,this.headers=n,this.request_id=null==n?void 0:n["x-request-id"],this.error=t;const r=t;this.code=null==r?void 0:r.code,this.param=null==r?void 0:r.param,this.type=null==r?void 0:r.type}static makeMessage(e,t,s){const n=(null==t?void 0:t.message)?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):s;return e&&n?`${e} ${n}`:e?`${e} status code (no body)`:n||"(no status code or body)"}static generate(e,t,s,n){if(!e||!n)return new k({message:s,cause:Ce(t)});const r=null==t?void 0:t.error;return 400===e?new j(e,r,s,n):401===e?new M(e,r,s,n):403===e?new N(e,r,s,n):404===e?new B(e,r,s,n):409===e?new D(e,r,s,n):422===e?new q(e,r,s,n):429===e?new L(e,r,s,n):e>=500?new F(e,r,s,n):new C(e,r,s,n)}}class O extends C{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class k extends C{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class T extends k{constructor({message:e}={}){super({message:e??"Request timed out."})}}class j extends C{}class M extends C{}class N extends C{}class B extends C{}class D extends C{}class q extends C{}class L extends C{}class F extends C{}class W extends I{constructor(){super("Could not parse response content as the length limit was reached")}}class U extends I{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var X,J=function(e,t,s,n,r){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s},H=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)};class V{constructor(){X.set(this,void 0),this.buffer=new Uint8Array,J(this,X,null,"f")}decode(e){if(null==e)return[];const t=e instanceof ArrayBuffer?new Uint8Array(e):"string"==typeof e?(new TextEncoder).encode(e):e;let s=new Uint8Array(this.buffer.length+t.length);s.set(this.buffer),s.set(t,this.buffer.length),this.buffer=s;const n=[];let r;for(;null!=(r=K(this.buffer,H(this,X,"f")));){if(r.carriage&&null==H(this,X,"f")){J(this,X,r.index,"f");continue}if(null!=H(this,X,"f")&&(r.index!==H(this,X,"f")+1||r.carriage)){n.push(this.decodeText(this.buffer.slice(0,H(this,X,"f")-1))),this.buffer=this.buffer.slice(H(this,X,"f")),J(this,X,null,"f");continue}const e=null!==H(this,X,"f")?r.preceding-1:r.preceding,t=this.decodeText(this.buffer.slice(0,e));n.push(t),this.buffer=this.buffer.slice(r.index),J(this,X,null,"f")}return n}decodeText(e){if(null==e)return"";if("string"==typeof e)return e;if("undefined"!=typeof Buffer){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new I(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if("undefined"!=typeof TextDecoder){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new I(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new I("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode("\n"):[]}}function K(e,t){for(let s=t??0;s<e.length;s++){if(10===e[s])return{preceding:s,index:s+1,carriage:!1};if(13===e[s])return{preceding:s,index:s+1,carriage:!0}}return null}function z(e){for(let t=0;t<e.length-1;t++){if(10===e[t]&&10===e[t+1])return t+2;if(13===e[t]&&13===e[t+1])return t+2;if(13===e[t]&&10===e[t+1]&&t+3<e.length&&13===e[t+2]&&10===e[t+3])return t+4}return-1}function Q(e){if(e[Symbol.asyncIterator])return e;const t=e.getReader();return{async next(){try{const e=await t.read();return(null==e?void 0:e.done)&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){const e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}X=new WeakMap,V.NEWLINE_CHARS=new Set(["\n","\r"]),V.NEWLINE_REGEXP=/\r\n|[\n\r]/g;class G{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let s=!1;return new G((async function*(){if(s)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(const s of async function*(e,t){if(!e.body)throw t.abort(),new I("Attempted to iterate over a response with no body");const s=new Y,n=new V,r=Q(e.body);for await(const o of async function*(e){let t=new Uint8Array;for await(const s of e){if(null==s)continue;const e=s instanceof ArrayBuffer?new Uint8Array(s):"string"==typeof s?(new TextEncoder).encode(s):s;let n,r=new Uint8Array(t.length+e.length);for(r.set(t),r.set(e,t.length),t=r;-1!==(n=z(t));)yield t.slice(0,n),t=t.slice(n)}t.length>0&&(yield t)}(r))for(const e of n.decode(o)){const t=s.decode(e);t&&(yield t)}for(const o of n.flush()){const e=s.decode(o);e&&(yield e)}}(e,t))if(!n)if(s.data.startsWith("[DONE]"))n=!0;else if(null===s.event||s.event.startsWith("response.")||s.event.startsWith("transcript.")){let t;try{t=JSON.parse(s.data)}catch(r){throw r}if(t&&t.error)throw new C(void 0,t.error,void 0,ye(e.headers));yield t}else{let e;try{e=JSON.parse(s.data)}catch(r){throw r}if("error"==s.event)throw new C(void 0,e.error,e.message,void 0);yield{event:s.event,data:e}}n=!0}catch(r){if(r instanceof Error&&"AbortError"===r.name)return;throw r}finally{n||t.abort()}}),t)}static fromReadableStream(e,t){let s=!1;return new G((async function*(){if(s)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let n=!1;try{for await(const t of async function*(){const t=new V,s=Q(e);for await(const e of s)for(const s of t.decode(e))yield s;for(const e of t.flush())yield e}())n||t&&(yield JSON.parse(t));n=!0}catch(r){if(r instanceof Error&&"AbortError"===r.name)return;throw r}finally{n||t.abort()}}),t)}[Symbol.asyncIterator](){return this.iterator()}tee(){const e=[],t=[],s=this.iterator(),n=n=>({next:()=>{if(0===n.length){const n=s.next();e.push(n),t.push(n)}return n.shift()}});return[new G((()=>n(e)),this.controller),new G((()=>n(t)),this.controller)]}toReadableStream(){const e=this;let t;const s=new TextEncoder;return new b({async start(){t=e[Symbol.asyncIterator]()},async pull(e){try{const{value:n,done:r}=await t.next();if(r)return e.close();const o=s.encode(JSON.stringify(n)+"\n");e.enqueue(o)}catch(n){e.error(n)}},async cancel(){var e;await(null==(e=t.return)?void 0:e.call(t))}})}}class Y{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,s,n]=function(e,t){const s=e.indexOf(t);if(-1!==s)return[e.substring(0,s),t,e.substring(s+t.length)];return[e,"",""]}(e,":");return n.startsWith(" ")&&(n=n.substring(1)),"event"===t?this.event=n:"data"===t&&this.data.push(n),null}}const Z=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob,ee=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&te(e),te=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer;async function se(e,t,s){var n;if(e=await e,ee(e))return e;if(Z(e)){const n=await e.blob();t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()??"unknown_file");const r=te(n)?[await n.arrayBuffer()]:[n];return new _(r,t,s)}const r=await async function(e){var t;let s=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)s.push(e);else if(te(e))s.push(await e.arrayBuffer());else{if(!re(e))throw new Error(`Unexpected data type: ${typeof e}; constructor: ${null==(t=null==e?void 0:e.constructor)?void 0:t.name}; props: ${function(e){const t=Object.getOwnPropertyNames(e);return`[${t.map((e=>`"${e}"`)).join(", ")}]`}(e)}`);for await(const t of e)s.push(t)}return s}(e);if(t||(t=function(e){var t;return ne(e.name)||ne(e.filename)||(null==(t=ne(e.path))?void 0:t.split(/[\\/]/).pop())}(e)??"unknown_file"),!(null==s?void 0:s.type)){const e=null==(n=r[0])?void 0:n.type;"string"==typeof e&&(s={...s,type:e})}return new _(r,t,s)}const ne=e=>"string"==typeof e?e:"undefined"!=typeof Buffer&&e instanceof Buffer?String(e):void 0,re=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],oe=e=>e&&"object"==typeof e&&e.body&&"MultipartBody"===e[Symbol.toStringTag],ie=async e=>{const t=await ae(e.body);return x(t,e)},ae=async e=>{const t=new v;return await Promise.all(Object.entries(e||{}).map((([e,s])=>le(t,e,s)))),t},le=async(e,t,s)=>{if(void 0!==s){if(null==s)throw new TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof s||"number"==typeof s||"boolean"==typeof s)e.append(t,String(s));else if((e=>ee(e)||Z(e)||P(e))(s)){const n=await se(s);e.append(t,n)}else if(Array.isArray(s))await Promise.all(s.map((s=>le(e,t+"[]",s))));else{if("object"!=typeof s)throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${s} instead`);await Promise.all(Object.entries(s).map((([s,n])=>le(e,`${t}[${s}]`,n))))}}};var ce,ue={};async function de(e){var t;const{response:s}=e;if(e.options.stream)return Ne("response",s.status,s.url,s.headers,s.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(s,e.controller):G.fromSSEResponse(s,e.controller);if(204===s.status)return null;if(e.options.__binaryResponse)return s;const n=s.headers.get("content-type"),r=null==(t=null==n?void 0:n.split(";")[0])?void 0:t.trim();if((null==r?void 0:r.includes("application/json"))||(null==r?void 0:r.endsWith("+json"))){const e=await s.json();return Ne("response",s.status,s.url,s.headers,e),he(e,s)}const o=await s.text();return Ne("response",s.status,s.url,s.headers,o),o}function he(e,t){return!e||"object"!=typeof e||Array.isArray(e)?e:Object.defineProperty(e,"_request_id",{value:t.headers.get("x-request-id"),enumerable:!1})}E();class fe extends Promise{constructor(e,t=de){super((e=>{e(null)})),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new fe(this.responsePromise,(async t=>he(e(await this.parseResponse(t),t),t.response)))}asResponse(){return this.responsePromise.then((e=>e.response))}async withResponse(){const[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class pe{constructor({baseURL:e,maxRetries:t=2,timeout:s=6e5,httpAgent:n,fetch:r}){this.baseURL=e,this.maxRetries=Ie("maxRetries",t),this.timeout=Ie("timeout",s),this.httpAgent=n,this.fetch=r??w}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...Ae(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${Be()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,s){return this.request(Promise.resolve(s).then((async s=>{const n=s&&te(null==s?void 0:s.body)?new DataView(await s.body.arrayBuffer()):(null==s?void 0:s.body)instanceof DataView?s.body:(null==s?void 0:s.body)instanceof ArrayBuffer?new DataView(s.body):s&&ArrayBuffer.isView(null==s?void 0:s.body)?new DataView(s.body.buffer):null==s?void 0:s.body;return{method:e,path:t,...s,body:n}})))}getAPIList(e,t,s){return this.requestAPIList(t,{method:"get",path:e,...s})}calculateContentLength(e){if("string"==typeof e){if("undefined"!=typeof Buffer)return Buffer.byteLength(e,"utf8").toString();if("undefined"!=typeof TextEncoder){return(new TextEncoder).encode(e).length.toString()}}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){var s;const n={...e},{method:r,path:o,query:i,headers:a={}}=n,l=ArrayBuffer.isView(n.body)||n.__binaryRequest&&"string"==typeof n.body?n.body:oe(n.body)?n.body.body:n.body?JSON.stringify(n.body,null,2):null,c=this.calculateContentLength(l),u=this.buildURL(o,i);"timeout"in n&&Ie("timeout",n.timeout),n.timeout=n.timeout??this.timeout;const d=n.httpAgent??this.httpAgent??S(u),h=n.timeout+1e3;"number"==typeof(null==(s=null==d?void 0:d.options)?void 0:s.timeout)&&h>(d.options.timeout??0)&&(d.options.timeout=h),this.idempotencyHeader&&"get"!==r&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);return{req:{method:r,...l&&{body:l},headers:this.buildHeaders({options:n,headers:a,contentLength:c,retryCount:t}),...d&&{agent:d},signal:n.signal??null},url:u,timeout:n.timeout}}buildHeaders({options:e,headers:t,contentLength:s,retryCount:n}){const r={};s&&(r["content-length"]=s);const o=this.defaultHeaders(e);return je(r,o),je(r,t),oe(e.body)&&"node"!==y&&delete r["content-type"],void 0===De(o,"x-stainless-retry-count")&&void 0===De(t,"x-stainless-retry-count")&&(r["x-stainless-retry-count"]=String(n)),void 0===De(o,"x-stainless-timeout")&&void 0===De(t,"x-stainless-timeout")&&e.timeout&&(r["x-stainless-timeout"]=String(Math.trunc(e.timeout/1e3))),this.validateHeaders(r,t),r}async prepareOptions(e){}async prepareRequest(e,{url:t,options:s}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map((e=>[...e]))):{...e}:{}}makeStatusError(e,t,s,n){return C.generate(e,t,s,n)}request(e,t=null){return new fe(this.makeRequest(e,t))}async makeRequest(e,t){var s,n;const r=await e,o=r.maxRetries??this.maxRetries;null==t&&(t=o),await this.prepareOptions(r);const{req:i,url:a,timeout:l}=this.buildRequest(r,{retryCount:o-t});if(await this.prepareRequest(i,{url:a,options:r}),Ne("request",a,r,i.headers),null==(s=r.signal)?void 0:s.aborted)throw new O;const c=new AbortController,u=await this.fetchWithTimeout(a,i,l,c).catch(Ce);if(u instanceof Error){if(null==(n=r.signal)?void 0:n.aborted)throw new O;if(t)return this.retryRequest(r,t);if("AbortError"===u.name)throw new T;throw new k({cause:u})}const d=ye(u.headers);if(!u.ok){if(t&&this.shouldRetry(u)){return Ne(`response (error; ${`retrying, ${t} attempts remaining`})`,u.status,a,d),this.retryRequest(r,t,d)}const e=await u.text().catch((e=>Ce(e).message)),s=Pe(e),n=s?void 0:e;Ne(`response (error; ${t?"(error; no more retries left)":"(error; not retryable)"})`,u.status,a,d,n);throw this.makeStatusError(u.status,s,n,d)}return{response:u,options:r,controller:c}}requestAPIList(e,t){const s=this.makeRequest(t,null);return new ge(this,s,e)}buildURL(e,t){const s=Re(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),n=this.defaultQuery();return ke(n)||(t={...n,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(s.search=this.stringifyQuery(t)),s.toString()}stringifyQuery(e){return Object.entries(e).filter((([e,t])=>void 0!==t)).map((([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new I(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)})).join("&")}async fetchWithTimeout(e,t,s,n){const{signal:r,...o}=t||{};r&&r.addEventListener("abort",(()=>n.abort()));const i=setTimeout((()=>n.abort()),s),a={signal:n.signal,...o};return a.method&&(a.method=a.method.toUpperCase()),this.fetch.call(void 0,e,a).finally((()=>{clearTimeout(i)}))}shouldRetry(e){const t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||(409===e.status||(429===e.status||e.status>=500)))}async retryRequest(e,t,s){let n;const r=null==s?void 0:s["retry-after-ms"];if(r){const e=parseFloat(r);Number.isNaN(e)||(n=e)}const o=null==s?void 0:s["retry-after"];if(o&&!n){const e=parseFloat(o);n=Number.isNaN(e)?Date.parse(o)-Date.now():1e3*e}if(!(n&&0<=n&&n<6e4)){const s=e.maxRetries??this.maxRetries;n=this.calculateDefaultRetryTimeoutMillis(t,s)}return await Ee(n),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){const s=t-e;return Math.min(.5*Math.pow(2,s),8)*(1-.25*Math.random())*1e3}getUserAgent(){return`${this.constructor.name}/JS ${g}`}}class me{constructor(e,t,s,n){ce.set(this,void 0),function(e,t,s){if("function"==typeof t||!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");t.set(e,s)}(this,ce,e),this.options=n,this.response=t,this.body=s}hasNextPage(){return!!this.getPaginatedItems().length&&null!=this.nextPageInfo()}async getNextPage(){const e=this.nextPageInfo();if(!e)throw new I("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");const t={...this.options};if("params"in e&&"object"==typeof t.query)t.query={...t.query,...e.params};else if("url"in e){const s=[...Object.entries(t.query||{}),...e.url.searchParams.entries()];for(const[t,n]of s)e.url.searchParams.set(t,n);t.query=void 0,t.path=e.url.toString()}return await function(e,t,s,n){if("function"==typeof t||!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)}(this,ce,"f").requestAPIList(this.constructor,t)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(ce=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const t of e.getPaginatedItems())yield t}}class ge extends fe{constructor(e,t,s){super(t,(async t=>new s(e,t.response,await de(t),t.options)))}async*[Symbol.asyncIterator](){const e=await(this);for await(const t of e)yield t}}const ye=e=>new Proxy(Object.fromEntries(e.entries()),{get(e,t){const s=t.toString();return e[s.toLowerCase()]||e[s]}}),we={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},ve=e=>"object"==typeof e&&null!==e&&!ke(e)&&Object.keys(e).every((e=>Te(we,e))),_e=()=>{var e;if("undefined"!=typeof Deno&&null!=Deno.build)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":g,"X-Stainless-OS":xe(Deno.build.os),"X-Stainless-Arch":be(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":"string"==typeof Deno.version?Deno.version:(null==(e=Deno.version)?void 0:e.deno)??"unknown"};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":g,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if("[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0))return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":g,"X-Stainless-OS":xe(process.platform),"X-Stainless-Arch":be(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};const t=function(){if("undefined"==typeof navigator||!navigator)return null;const e=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:t,pattern:s}of e){const e=s.exec(navigator.userAgent);if(e){return{browser:t,version:`${e[1]||0}.${e[2]||0}.${e[3]||0}`}}}return null}();return t?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":g,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${t.browser}`,"X-Stainless-Runtime-Version":t.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":g,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};const be=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",xe=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown";let Se;const Ae=()=>Se??(Se=_e()),Pe=e=>{try{return JSON.parse(e)}catch(t){return}},$e=/^[a-z][a-z0-9+.-]*:/i,Re=e=>$e.test(e),Ee=e=>new Promise((t=>setTimeout(t,e))),Ie=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new I(`${e} must be an integer`);if(t<0)throw new I(`${e} must be a positive integer`);return t},Ce=e=>{if(e instanceof Error)return e;if("object"==typeof e&&null!==e)try{return new Error(JSON.stringify(e))}catch{}return new Error(e)},Oe=e=>{var t,s,n,r;return"undefined"!=typeof process?(null==(t=null==ue?void 0:ue[e])?void 0:t.trim())??void 0:"undefined"!=typeof Deno?null==(r=null==(n=null==(s=Deno.env)?void 0:s.get)?void 0:n.call(s,e))?void 0:r.trim():void 0};function ke(e){if(!e)return!0;for(const t in e)return!1;return!0}function Te(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function je(e,t){for(const s in t){if(!Te(t,s))continue;const n=s.toLowerCase();if(!n)continue;const r=t[s];null===r?delete e[n]:void 0!==r&&(e[n]=r)}}const Me=new Set(["authorization","api-key"]);function Ne(e,...t){if("undefined"!=typeof process&&"true"===(null==ue?void 0:ue.DEBUG)){t.map((e=>{if(!e)return e;if(e.headers){const t={...e,headers:{...e.headers}};for(const s in e.headers)Me.has(s.toLowerCase())&&(t.headers[s]="REDACTED");return t}let t=null;for(const s in e)Me.has(s.toLowerCase())&&(t??(t={...e}),t[s]="REDACTED");return t??e}))}}const Be=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(e=>{const t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})),De=(e,t)=>{var s;const n=t.toLowerCase();if((e=>"function"==typeof(null==e?void 0:e.get))(e)){const r=(null==(s=t[0])?void 0:s.toUpperCase())+t.substring(1).replace(/([^\w])(\w)/g,((e,t,s)=>t+s.toUpperCase()));for(const s of[t,n,t.toUpperCase(),r]){const t=e.get(s);if(t)return t}}for(const[r,o]of Object.entries(e))if(r.toLowerCase()===n)return Array.isArray(o)?(o.length,o[0]):o};function qe(e){return null!=e&&"object"==typeof e&&!Array.isArray(e)}class Le extends me{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageParams(){return null}nextPageInfo(){return null}}class Fe extends me{constructor(e,t,s,n){super(e,t,s,n),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return!1!==this.has_more&&super.hasNextPage()}nextPageParams(){const e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;const t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){var e;const t=this.getPaginatedItems();if(!t.length)return null;const s=null==(e=t[t.length-1])?void 0:e.id;return s?{params:{after:s}}:null}}class We{constructor(e){this._client=e}}let Ue=class extends We{list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/chat/completions/${e}/messages`,He,{query:t,...s})}},Xe=class extends We{constructor(){super(...arguments),this.messages=new Ue(this._client)}create(e,t){return this._client.post("/chat/completions",{body:e,...t,stream:e.stream??!1})}retrieve(e,t){return this._client.get(`/chat/completions/${e}`,t)}update(e,t,s){return this._client.post(`/chat/completions/${e}`,{body:t,...s})}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/chat/completions",Je,{query:e,...t})}del(e,t){return this._client.delete(`/chat/completions/${e}`,t)}};class Je extends Fe{}class He extends Fe{}Xe.ChatCompletionsPage=Je,Xe.Messages=Ue;let Ve=class extends We{constructor(){super(...arguments),this.completions=new Xe(this._client)}};Ve.Completions=Xe,Ve.ChatCompletionsPage=Je;class Ke extends We{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:{Accept:"application/octet-stream",...null==t?void 0:t.headers},__binaryResponse:!0})}}class ze extends We{create(e,t){return this._client.post("/audio/transcriptions",ie({body:e,...t,stream:e.stream??!1,__metadata:{model:e.model}}))}}class Qe extends We{create(e,t){return this._client.post("/audio/translations",ie({body:e,...t,__metadata:{model:e.model}}))}}class Ge extends We{constructor(){super(...arguments),this.transcriptions=new ze(this._client),this.translations=new Qe(this._client),this.speech=new Ke(this._client)}}Ge.Transcriptions=ze,Ge.Translations=Qe,Ge.Speech=Ke;class Ye extends We{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/batches",Ze,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}}class Ze extends Fe{}Ye.BatchesPage=Ze;var et,tt,st,nt,rt,ot,it,at,lt,ct,ut,dt,ht,ft=function(e,t,s,n,r){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s},pt=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)};class mt{constructor(){et.add(this),this.controller=new AbortController,tt.set(this,void 0),st.set(this,(()=>{})),nt.set(this,(()=>{})),rt.set(this,void 0),ot.set(this,(()=>{})),it.set(this,(()=>{})),at.set(this,{}),lt.set(this,!1),ct.set(this,!1),ut.set(this,!1),dt.set(this,!1),ft(this,tt,new Promise(((e,t)=>{ft(this,st,e,"f"),ft(this,nt,t,"f")})),"f"),ft(this,rt,new Promise(((e,t)=>{ft(this,ot,e,"f"),ft(this,it,t,"f")})),"f"),pt(this,tt,"f").catch((()=>{})),pt(this,rt,"f").catch((()=>{}))}_run(e){setTimeout((()=>{e().then((()=>{this._emitFinal(),this._emit("end")}),pt(this,et,"m",ht).bind(this))}),0)}_connected(){this.ended||(pt(this,st,"f").call(this),this._emit("connect"))}get ended(){return pt(this,lt,"f")}get errored(){return pt(this,ct,"f")}get aborted(){return pt(this,ut,"f")}abort(){this.controller.abort()}on(e,t){return(pt(this,at,"f")[e]||(pt(this,at,"f")[e]=[])).push({listener:t}),this}off(e,t){const s=pt(this,at,"f")[e];if(!s)return this;const n=s.findIndex((e=>e.listener===t));return n>=0&&s.splice(n,1),this}once(e,t){return(pt(this,at,"f")[e]||(pt(this,at,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise(((t,s)=>{ft(this,dt,!0,"f"),"error"!==e&&this.once("error",s),this.once(e,t)}))}async done(){ft(this,dt,!0,"f"),await pt(this,rt,"f")}_emit(e,...t){if(pt(this,lt,"f"))return;"end"===e&&(ft(this,lt,!0,"f"),pt(this,ot,"f").call(this));const s=pt(this,at,"f")[e];if(s&&(pt(this,at,"f")[e]=s.filter((e=>!e.once)),s.forEach((({listener:e})=>e(...t)))),"abort"===e){const e=t[0];return pt(this,dt,"f")||(null==s?void 0:s.length)||Promise.reject(e),pt(this,nt,"f").call(this,e),pt(this,it,"f").call(this,e),void this._emit("end")}if("error"===e){const e=t[0];pt(this,dt,"f")||(null==s?void 0:s.length)||Promise.reject(e),pt(this,nt,"f").call(this,e),pt(this,it,"f").call(this,e),this._emit("end")}}_emitFinal(){}}tt=new WeakMap,st=new WeakMap,nt=new WeakMap,rt=new WeakMap,ot=new WeakMap,it=new WeakMap,at=new WeakMap,lt=new WeakMap,ct=new WeakMap,ut=new WeakMap,dt=new WeakMap,et=new WeakSet,ht=function(e){if(ft(this,ct,!0,"f"),e instanceof Error&&"AbortError"===e.name&&(e=new O),e instanceof O)return ft(this,ut,!0,"f"),this._emit("abort",e);if(e instanceof I)return this._emit("error",e);if(e instanceof Error){const t=new I(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new I(String(e)))};var gt,yt,wt,vt,_t,bt,xt,St,At,Pt,$t,Rt,Et,It,Ct,Ot,kt,Tt,jt,Mt,Nt,Bt,Dt=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)},qt=function(e,t,s,n,r){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s};class Lt extends mt{constructor(){super(...arguments),gt.add(this),yt.set(this,[]),wt.set(this,{}),vt.set(this,{}),_t.set(this,void 0),bt.set(this,void 0),xt.set(this,void 0),St.set(this,void 0),At.set(this,void 0),Pt.set(this,void 0),$t.set(this,void 0),Rt.set(this,void 0),Et.set(this,void 0)}[(yt=new WeakMap,wt=new WeakMap,vt=new WeakMap,_t=new WeakMap,bt=new WeakMap,xt=new WeakMap,St=new WeakMap,At=new WeakMap,Pt=new WeakMap,$t=new WeakMap,Rt=new WeakMap,Et=new WeakMap,gt=new WeakSet,Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("event",(s=>{const n=t.shift();n?n.resolve(s):e.push(s)})),this.on("end",(()=>{s=!0;for(const e of t)e.resolve(void 0);t.length=0})),this.on("abort",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),this.on("error",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),{next:async()=>{if(!e.length)return s?{value:void 0,done:!0}:new Promise(((e,s)=>t.push({resolve:e,reject:s}))).then((e=>e?{value:e,done:!1}:{value:void 0,done:!0}));return{value:e.shift(),done:!1}},return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const t=new Lt;return t._run((()=>t._fromReadableStream(e))),t}async _fromReadableStream(e,t){var s;const n=null==t?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",(()=>this.controller.abort()))),this._connected();const r=G.fromReadableStream(e,this.controller);for await(const o of r)Dt(this,gt,"m",It).call(this,o);if(null==(s=r.controller.signal)?void 0:s.aborted)throw new O;return this._addRun(Dt(this,gt,"m",Ct).call(this))}toReadableStream(){return new G(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,s,n,r){const o=new Lt;return o._run((()=>o._runToolAssistantStream(e,t,s,n,{...r,headers:{...null==r?void 0:r.headers,"X-Stainless-Helper-Method":"stream"}}))),o}async _createToolAssistantStream(e,t,s,n,r){var o;const i=null==r?void 0:r.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",(()=>this.controller.abort())));const a={...n,stream:!0},l=await e.submitToolOutputs(t,s,a,{...r,signal:this.controller.signal});this._connected();for await(const c of l)Dt(this,gt,"m",It).call(this,c);if(null==(o=l.controller.signal)?void 0:o.aborted)throw new O;return this._addRun(Dt(this,gt,"m",Ct).call(this))}static createThreadAssistantStream(e,t,s){const n=new Lt;return n._run((()=>n._threadAssistantStream(e,t,{...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}}))),n}static createAssistantStream(e,t,s,n){const r=new Lt;return r._run((()=>r._runAssistantStream(e,t,s,{...n,headers:{...null==n?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}}))),r}currentEvent(){return Dt(this,$t,"f")}currentRun(){return Dt(this,Rt,"f")}currentMessageSnapshot(){return Dt(this,_t,"f")}currentRunStepSnapshot(){return Dt(this,Et,"f")}async finalRunSteps(){return await this.done(),Object.values(Dt(this,wt,"f"))}async finalMessages(){return await this.done(),Object.values(Dt(this,vt,"f"))}async finalRun(){if(await this.done(),!Dt(this,bt,"f"))throw Error("Final run was not received.");return Dt(this,bt,"f")}async _createThreadAssistantStream(e,t,s){var n;const r=null==s?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",(()=>this.controller.abort())));const o={...t,stream:!0},i=await e.createAndRun(o,{...s,signal:this.controller.signal});this._connected();for await(const a of i)Dt(this,gt,"m",It).call(this,a);if(null==(n=i.controller.signal)?void 0:n.aborted)throw new O;return this._addRun(Dt(this,gt,"m",Ct).call(this))}async _createAssistantStream(e,t,s,n){var r;const o=null==n?void 0:n.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",(()=>this.controller.abort())));const i={...s,stream:!0},a=await e.create(t,i,{...n,signal:this.controller.signal});this._connected();for await(const l of a)Dt(this,gt,"m",It).call(this,l);if(null==(r=a.controller.signal)?void 0:r.aborted)throw new O;return this._addRun(Dt(this,gt,"m",Ct).call(this))}static accumulateDelta(e,t){for(const[s,n]of Object.entries(t)){if(!e.hasOwnProperty(s)){e[s]=n;continue}let t=e[s];if(null!=t)if("index"!==s&&"type"!==s){if("string"==typeof t&&"string"==typeof n)t+=n;else if("number"==typeof t&&"number"==typeof n)t+=n;else{if(!qe(t)||!qe(n)){if(Array.isArray(t)&&Array.isArray(n)){if(t.every((e=>"string"==typeof e||"number"==typeof e))){t.push(...n);continue}for(const e of n){if(!qe(e))throw new Error(`Expected array delta entry to be an object but got: ${e}`);const s=e.index;if(null==s)throw new Error("Expected array delta entry to have an `index` property");if("number"!=typeof s)throw new Error(`Expected array delta entry \`index\` property to be a number but got ${s}`);const n=t[s];null==n?t.push(e):t[s]=this.accumulateDelta(n,e)}continue}throw Error(`Unhandled record type: ${s}, deltaValue: ${n}, accValue: ${t}`)}t=this.accumulateDelta(t,n)}e[s]=t}else e[s]=n;else e[s]=n}return e}_addRun(e){return e}async _threadAssistantStream(e,t,s){return await this._createThreadAssistantStream(t,e,s)}async _runAssistantStream(e,t,s,n){return await this._createAssistantStream(t,e,s,n)}async _runToolAssistantStream(e,t,s,n,r){return await this._createToolAssistantStream(s,e,t,n,r)}}It=function(e){if(!this.ended)switch(qt(this,$t,e,"f"),Dt(this,gt,"m",Tt).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":Dt(this,gt,"m",Bt).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":Dt(this,gt,"m",kt).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":Dt(this,gt,"m",Ot).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},Ct=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");if(!Dt(this,bt,"f"))throw Error("Final run has not been received");return Dt(this,bt,"f")},Ot=function(e){const[t,s]=Dt(this,gt,"m",Mt).call(this,e,Dt(this,_t,"f"));qt(this,_t,t,"f"),Dt(this,vt,"f")[t.id]=t;for(const n of s){const e=t.content[n.index];"text"==(null==e?void 0:e.type)&&this._emit("textCreated",e.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(const s of e.data.delta.content){if("text"==s.type&&s.text){let e=s.text,n=t.content[s.index];if(!n||"text"!=n.type)throw Error("The snapshot associated with this text delta is not text or missing");this._emit("textDelta",e,n.text)}if(s.index!=Dt(this,xt,"f")){if(Dt(this,St,"f"))switch(Dt(this,St,"f").type){case"text":this._emit("textDone",Dt(this,St,"f").text,Dt(this,_t,"f"));break;case"image_file":this._emit("imageFileDone",Dt(this,St,"f").image_file,Dt(this,_t,"f"))}qt(this,xt,s.index,"f")}qt(this,St,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(void 0!==Dt(this,xt,"f")){const t=e.data.content[Dt(this,xt,"f")];if(t)switch(t.type){case"image_file":this._emit("imageFileDone",t.image_file,Dt(this,_t,"f"));break;case"text":this._emit("textDone",t.text,Dt(this,_t,"f"))}}Dt(this,_t,"f")&&this._emit("messageDone",e.data),qt(this,_t,void 0,"f")}},kt=function(e){const t=Dt(this,gt,"m",jt).call(this,e);switch(qt(this,Et,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const s=e.data.delta;if(s.step_details&&"tool_calls"==s.step_details.type&&s.step_details.tool_calls&&"tool_calls"==t.step_details.type)for(const e of s.step_details.tool_calls)e.index==Dt(this,At,"f")?this._emit("toolCallDelta",e,t.step_details.tool_calls[e.index]):(Dt(this,Pt,"f")&&this._emit("toolCallDone",Dt(this,Pt,"f")),qt(this,At,e.index,"f"),qt(this,Pt,t.step_details.tool_calls[e.index],"f"),Dt(this,Pt,"f")&&this._emit("toolCallCreated",Dt(this,Pt,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":qt(this,Et,void 0,"f");"tool_calls"==e.data.step_details.type&&Dt(this,Pt,"f")&&(this._emit("toolCallDone",Dt(this,Pt,"f")),qt(this,Pt,void 0,"f")),this._emit("runStepDone",e.data,t)}},Tt=function(e){Dt(this,yt,"f").push(e),this._emit("event",e)},jt=function(e){switch(e.event){case"thread.run.step.created":return Dt(this,wt,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=Dt(this,wt,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){const n=Lt.accumulateDelta(t,s.delta);Dt(this,wt,"f")[e.data.id]=n}return Dt(this,wt,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":Dt(this,wt,"f")[e.data.id]=e.data}if(Dt(this,wt,"f")[e.data.id])return Dt(this,wt,"f")[e.data.id];throw new Error("No snapshot available")},Mt=function(e,t){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let n=e.data;if(n.delta.content)for(const e of n.delta.content)if(e.index in t.content){let s=t.content[e.index];t.content[e.index]=Dt(this,gt,"m",Nt).call(this,e,s)}else t.content[e.index]=e,s.push(e);return[t,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},Nt=function(e,t){return Lt.accumulateDelta(t,e)},Bt=function(e){switch(qt(this,Rt,e.data,"f"),e.event){case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":qt(this,bt,e.data,"f"),Dt(this,Pt,"f")&&(this._emit("toolCallDone",Dt(this,Pt,"f")),qt(this,Pt,void 0,"f"))}};class Ft extends We{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}update(e,t,s){return this._client.post(`/assistants/${e}`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/assistants",Wt,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}}class Wt extends Fe{}function Ut(e){return"function"==typeof e.parse}Ft.AssistantsPage=Wt;const Xt=e=>"assistant"===(null==e?void 0:e.role),Jt=e=>"function"===(null==e?void 0:e.role),Ht=e=>"tool"===(null==e?void 0:e.role);function Vt(e){return"auto-parseable-response-format"===(null==e?void 0:e.$brand)}function Kt(e){return"auto-parseable-tool"===(null==e?void 0:e.$brand)}function zt(e,t){const s=e.choices.map((e=>{var s;if("length"===e.finish_reason)throw new W;if("content_filter"===e.finish_reason)throw new U;return{...e,message:{...e.message,...e.message.tool_calls?{tool_calls:(null==(s=e.message.tool_calls)?void 0:s.map((e=>function(e,t){var s;const n=null==(s=e.tools)?void 0:s.find((e=>{var s;return(null==(s=e.function)?void 0:s.name)===t.function.name}));return{...t,function:{...t.function,parsed_arguments:Kt(n)?n.$parseRaw(t.function.arguments):(null==n?void 0:n.function.strict)?JSON.parse(t.function.arguments):null}}}(t,e))))??void 0}:void 0,parsed:e.message.content&&!e.message.refusal?Qt(t,e.message.content):null}}}));return{...e,choices:s}}function Qt(e,t){var s,n;if("json_schema"!==(null==(s=e.response_format)?void 0:s.type))return null;if("json_schema"===(null==(n=e.response_format)?void 0:n.type)){if("$parseRaw"in e.response_format){return e.response_format.$parseRaw(t)}return JSON.parse(t)}return null}function Gt(e,t){var s;if(!e)return!1;const n=null==(s=e.tools)?void 0:s.find((e=>{var s;return(null==(s=e.function)?void 0:s.name)===t.function.name}));return Kt(n)||(null==n?void 0:n.function.strict)||!1}function Yt(e){var t;return!!Vt(e.response_format)||((null==(t=e.tools)?void 0:t.some((e=>Kt(e)||"function"===e.type&&!0===e.function.strict)))??!1)}var Zt,es,ts,ss,ns,rs,os,is,as=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)};const ls=10;class cs extends mt{constructor(){super(...arguments),Zt.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var t;this._chatCompletions.push(e),this._emit("chatCompletion",e);const s=null==(t=e.choices[0])?void 0:t.message;return s&&this._addMessage(s),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t)if(this._emit("message",e),(Jt(e)||Ht(e))&&e.content)this._emit("functionCallResult",e.content);else if(Xt(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(Xt(e)&&e.tool_calls)for(const s of e.tool_calls)"function"===s.type&&this._emit("functionCall",s.function)}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new I("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),as(this,Zt,"m",es).call(this)}async finalMessage(){return await this.done(),as(this,Zt,"m",ts).call(this)}async finalFunctionCall(){return await this.done(),as(this,Zt,"m",ss).call(this)}async finalFunctionCallResult(){return await this.done(),as(this,Zt,"m",ns).call(this)}async totalUsage(){return await this.done(),as(this,Zt,"m",rs).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const t=as(this,Zt,"m",ts).call(this);t&&this._emit("finalMessage",t);const s=as(this,Zt,"m",es).call(this);s&&this._emit("finalContent",s);const n=as(this,Zt,"m",ss).call(this);n&&this._emit("finalFunctionCall",n);const r=as(this,Zt,"m",ns).call(this);null!=r&&this._emit("finalFunctionCallResult",r),this._chatCompletions.some((e=>e.usage))&&this._emit("totalUsage",as(this,Zt,"m",rs).call(this))}async _createChatCompletion(e,t,s){const n=null==s?void 0:s.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",(()=>this.controller.abort()))),as(this,Zt,"m",os).call(this,t);const r=await e.chat.completions.create({...t,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(zt(r,t))}async _runChatCompletion(e,t,s){for(const n of t.messages)this._addMessage(n,!1);return await this._createChatCompletion(e,t,s)}async _runFunctions(e,t,s){var n;const r="function",{function_call:o="auto",stream:i,...a}=t,l="string"!=typeof o&&(null==o?void 0:o.name),{maxChatCompletions:c=ls}=s||{},u={};for(const f of t.functions)u[f.name||f.function.name]=f;const d=t.functions.map((e=>({name:e.name||e.function.name,parameters:e.parameters,description:e.description})));for(const f of t.messages)this._addMessage(f,!1);for(let f=0;f<c;++f){const t=null==(n=(await this._createChatCompletion(e,{...a,function_call:o,functions:d,messages:[...this.messages]},s)).choices[0])?void 0:n.message;if(!t)throw new I("missing message in ChatCompletion response");if(!t.function_call)return;const{name:i,arguments:c}=t.function_call,f=u[i];if(!f){const e=`Invalid function_call: ${JSON.stringify(i)}. Available options are: ${d.map((e=>JSON.stringify(e.name))).join(", ")}. Please try again`;this._addMessage({role:r,name:i,content:e});continue}if(l&&l!==i){const e=`Invalid function_call: ${JSON.stringify(i)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:r,name:i,content:e});continue}let p;try{p=Ut(f)?await f.parse(c):c}catch(h){this._addMessage({role:r,name:i,content:h instanceof Error?h.message:String(h)});continue}const m=await f.function(p,this),g=as(this,Zt,"m",is).call(this,m);if(this._addMessage({role:r,name:i,content:g}),l)return}}async _runTools(e,t,s){var n,r,o;const i="tool",{tool_choice:a="auto",stream:l,...c}=t,u="string"!=typeof a&&(null==(n=null==a?void 0:a.function)?void 0:n.name),{maxChatCompletions:d=ls}=s||{},h=t.tools.map((e=>{if(Kt(e)){if(!e.$callback)throw new I("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:e.$callback,name:e.function.name,description:e.function.description||"",parameters:e.function.parameters,parse:e.$parseRaw,strict:!0}}}return e})),f={};for(const g of h)"function"===g.type&&(f[g.function.name||g.function.function.name]=g.function);const p="tools"in t?h.map((e=>"function"===e.type?{type:"function",function:{name:e.function.name||e.function.function.name,parameters:e.function.parameters,description:e.function.description,strict:e.function.strict}}:e)):void 0;for(const g of t.messages)this._addMessage(g,!1);for(let g=0;g<d;++g){const t=null==(r=(await this._createChatCompletion(e,{...c,tool_choice:a,tools:p,messages:[...this.messages]},s)).choices[0])?void 0:r.message;if(!t)throw new I("missing message in ChatCompletion response");if(!(null==(o=t.tool_calls)?void 0:o.length))return;for(const e of t.tool_calls){if("function"!==e.type)continue;const t=e.id,{name:s,arguments:n}=e.function,r=f[s];if(!r){const e=`Invalid tool_call: ${JSON.stringify(s)}. Available options are: ${Object.keys(f).map((e=>JSON.stringify(e))).join(", ")}. Please try again`;this._addMessage({role:i,tool_call_id:t,content:e});continue}if(u&&u!==s){const e=`Invalid tool_call: ${JSON.stringify(s)}. ${JSON.stringify(u)} requested. Please try again`;this._addMessage({role:i,tool_call_id:t,content:e});continue}let o;try{o=Ut(r)?await r.parse(n):n}catch(m){const e=m instanceof Error?m.message:String(m);this._addMessage({role:i,tool_call_id:t,content:e});continue}const a=await r.function(o,this),l=as(this,Zt,"m",is).call(this,a);if(this._addMessage({role:i,tool_call_id:t,content:l}),u)return}}}}Zt=new WeakSet,es=function(){return as(this,Zt,"m",ts).call(this).content??null},ts=function(){let e=this.messages.length;for(;e-- >0;){const t=this.messages[e];if(Xt(t)){const{function_call:e,...s}=t,n={...s,content:t.content??null,refusal:t.refusal??null};return e&&(n.function_call=e),n}}throw new I("stream ended without producing a ChatCompletionMessage with role=assistant")},ss=function(){var e,t;for(let s=this.messages.length-1;s>=0;s--){const n=this.messages[s];if(Xt(n)&&(null==n?void 0:n.function_call))return n.function_call;if(Xt(n)&&(null==(e=null==n?void 0:n.tool_calls)?void 0:e.length))return null==(t=n.tool_calls.at(-1))?void 0:t.function}},ns=function(){for(let e=this.messages.length-1;e>=0;e--){const t=this.messages[e];if(Jt(t)&&null!=t.content)return t.content;if(Ht(t)&&null!=t.content&&"string"==typeof t.content&&this.messages.some((e=>{var s;return"assistant"===e.role&&(null==(s=e.tool_calls)?void 0:s.some((e=>"function"===e.type&&e.id===t.tool_call_id)))})))return t.content}},rs=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},os=function(e){if(null!=e.n&&e.n>1)throw new I("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},is=function(e){return"string"==typeof e?e:void 0===e?"undefined":JSON.stringify(e)};class us extends cs{static runFunctions(e,t,s){const n=new us,r={...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run((()=>n._runFunctions(e,t,r))),n}static runTools(e,t,s){const n=new us,r={...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run((()=>n._runTools(e,t,r))),n}_addMessage(e,t=!0){super._addMessage(e,t),Xt(e)&&e.content&&this._emit("content",e.content)}}const ds=1,hs=2,fs=4,ps=8,ms=16,gs=32,ys=64,ws=128,vs=256,_s=511;class bs extends Error{}class xs extends Error{}const Ss=(e,t)=>{const s=e.length;let n=0;const r=e=>{throw new bs(`${e} at position ${n}`)},o=e=>{throw new xs(`${e} at position ${n}`)},i=()=>(d(),n>=s&&r("Unexpected end of input"),'"'===e[n]?a():"{"===e[n]?l():"["===e[n]?c():"null"===e.substring(n,n+4)||ms&t&&s-n<4&&"null".startsWith(e.substring(n))?(n+=4,null):"true"===e.substring(n,n+4)||gs&t&&s-n<4&&"true".startsWith(e.substring(n))?(n+=4,!0):"false"===e.substring(n,n+5)||gs&t&&s-n<5&&"false".startsWith(e.substring(n))?(n+=5,!1):"Infinity"===e.substring(n,n+8)||ws&t&&s-n<8&&"Infinity".startsWith(e.substring(n))?(n+=8,1/0):"-Infinity"===e.substring(n,n+9)||vs&t&&1<s-n&&s-n<9&&"-Infinity".startsWith(e.substring(n))?(n+=9,-1/0):"NaN"===e.substring(n,n+3)||ys&t&&s-n<3&&"NaN".startsWith(e.substring(n))?(n+=3,NaN):u()),a=()=>{const i=n;let a=!1;for(n++;n<s&&('"'!==e[n]||a&&"\\"===e[n-1]);)a="\\"===e[n]&&!a,n++;if('"'==e.charAt(n))try{return JSON.parse(e.substring(i,++n-Number(a)))}catch(l){o(String(l))}else if(ds&t)try{return JSON.parse(e.substring(i,n-Number(a))+'"')}catch(l){return JSON.parse(e.substring(i,e.lastIndexOf("\\"))+'"')}r("Unterminated string literal")},l=()=>{n++,d();const o={};try{for(;"}"!==e[n];){if(d(),n>=s&&ps&t)return o;const r=a();d(),n++;try{const e=i();Object.defineProperty(o,r,{value:e,writable:!0,enumerable:!0,configurable:!0})}catch(l){if(ps&t)return o;throw l}d(),","===e[n]&&n++}}catch(l){if(ps&t)return o;r("Expected '}' at end of object")}return n++,o},c=()=>{n++;const s=[];try{for(;"]"!==e[n];)s.push(i()),d(),","===e[n]&&n++}catch(o){if(fs&t)return s;r("Expected ']' at end of array")}return n++,s},u=()=>{if(0===n){"-"===e&&hs&t&&r("Not sure what '-' is");try{return JSON.parse(e)}catch(a){if(hs&t)try{return"."===e[e.length-1]?JSON.parse(e.substring(0,e.lastIndexOf("."))):JSON.parse(e.substring(0,e.lastIndexOf("e")))}catch(l){}o(String(a))}}const i=n;for("-"===e[n]&&n++;e[n]&&!",]}".includes(e[n]);)n++;n!=s||hs&t||r("Unterminated number literal");try{return JSON.parse(e.substring(i,n))}catch(a){"-"===e.substring(i,n)&&hs&t&&r("Not sure what '-' is");try{return JSON.parse(e.substring(i,e.lastIndexOf("e")))}catch(l){o(String(l))}}},d=()=>{for(;n<s&&" \n\r\t".includes(e[n]);)n++};return i()},As=e=>function(e,t=_s){if("string"!=typeof e)throw new TypeError("expecting str, got "+typeof e);if(!e.trim())throw new Error(`${e} is empty`);return Ss(e.trim(),t)}(e,_s^hs);var Ps,$s,Rs,Es,Is,Cs,Os,ks,Ts,js,Ms,Ns,Bs=function(e,t,s,n,r){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s},Ds=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)};class qs extends cs{constructor(e){super(),Ps.add(this),$s.set(this,void 0),Rs.set(this,void 0),Es.set(this,void 0),Bs(this,$s,e,"f"),Bs(this,Rs,[],"f")}get currentChatCompletionSnapshot(){return Ds(this,Es,"f")}static fromReadableStream(e){const t=new qs(null);return t._run((()=>t._fromReadableStream(e))),t}static createChatCompletion(e,t,s){const n=new qs(t);return n._run((()=>n._runChatCompletion(e,{...t,stream:!0},{...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}}))),n}async _createChatCompletion(e,t,s){var n;super._createChatCompletion;const r=null==s?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",(()=>this.controller.abort()))),Ds(this,Ps,"m",Is).call(this);const o=await e.chat.completions.create({...t,stream:!0},{...s,signal:this.controller.signal});this._connected();for await(const i of o)Ds(this,Ps,"m",Os).call(this,i);if(null==(n=o.controller.signal)?void 0:n.aborted)throw new O;return this._addChatCompletion(Ds(this,Ps,"m",js).call(this))}async _fromReadableStream(e,t){var s;const n=null==t?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",(()=>this.controller.abort()))),Ds(this,Ps,"m",Is).call(this),this._connected();const r=G.fromReadableStream(e,this.controller);let o;for await(const i of r)o&&o!==i.id&&this._addChatCompletion(Ds(this,Ps,"m",js).call(this)),Ds(this,Ps,"m",Os).call(this,i),o=i.id;if(null==(s=r.controller.signal)?void 0:s.aborted)throw new O;return this._addChatCompletion(Ds(this,Ps,"m",js).call(this))}[($s=new WeakMap,Rs=new WeakMap,Es=new WeakMap,Ps=new WeakSet,Is=function(){this.ended||Bs(this,Es,void 0,"f")},Cs=function(e){let t=Ds(this,Rs,"f")[e.index];return t||(t={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},Ds(this,Rs,"f")[e.index]=t,t)},Os=function(e){var t,s,n,r,o,i,a,l,c,u,d,h,f,p,m;if(this.ended)return;const g=Ds(this,Ps,"m",Ns).call(this,e);this._emit("chunk",e,g);for(const y of e.choices){const e=g.choices[y.index];null!=y.delta.content&&"assistant"===(null==(t=e.message)?void 0:t.role)&&(null==(s=e.message)?void 0:s.content)&&(this._emit("content",y.delta.content,e.message.content),this._emit("content.delta",{delta:y.delta.content,snapshot:e.message.content,parsed:e.message.parsed})),null!=y.delta.refusal&&"assistant"===(null==(n=e.message)?void 0:n.role)&&(null==(r=e.message)?void 0:r.refusal)&&this._emit("refusal.delta",{delta:y.delta.refusal,snapshot:e.message.refusal}),null!=(null==(o=y.logprobs)?void 0:o.content)&&"assistant"===(null==(i=e.message)?void 0:i.role)&&this._emit("logprobs.content.delta",{content:null==(a=y.logprobs)?void 0:a.content,snapshot:(null==(l=e.logprobs)?void 0:l.content)??[]}),null!=(null==(c=y.logprobs)?void 0:c.refusal)&&"assistant"===(null==(u=e.message)?void 0:u.role)&&this._emit("logprobs.refusal.delta",{refusal:null==(d=y.logprobs)?void 0:d.refusal,snapshot:(null==(h=e.logprobs)?void 0:h.refusal)??[]});const w=Ds(this,Ps,"m",Cs).call(this,e);e.finish_reason&&(Ds(this,Ps,"m",Ts).call(this,e),null!=w.current_tool_call_index&&Ds(this,Ps,"m",ks).call(this,e,w.current_tool_call_index));for(const t of y.delta.tool_calls??[])w.current_tool_call_index!==t.index&&(Ds(this,Ps,"m",Ts).call(this,e),null!=w.current_tool_call_index&&Ds(this,Ps,"m",ks).call(this,e,w.current_tool_call_index)),w.current_tool_call_index=t.index;for(const t of y.delta.tool_calls??[]){const s=null==(f=e.message.tool_calls)?void 0:f[t.index];(null==s?void 0:s.type)&&("function"===(null==s?void 0:s.type)?this._emit("tool_calls.function.arguments.delta",{name:null==(p=s.function)?void 0:p.name,index:t.index,arguments:s.function.arguments,parsed_arguments:s.function.parsed_arguments,arguments_delta:(null==(m=t.function)?void 0:m.arguments)??""}):null==s||s.type)}}},ks=function(e,t){var s,n,r;if(Ds(this,Ps,"m",Cs).call(this,e).done_tool_calls.has(t))return;const o=null==(s=e.message.tool_calls)?void 0:s[t];if(!o)throw new Error("no tool call snapshot");if(!o.type)throw new Error("tool call snapshot missing `type`");if("function"===o.type){const e=null==(r=null==(n=Ds(this,$s,"f"))?void 0:n.tools)?void 0:r.find((e=>"function"===e.type&&e.function.name===o.function.name));this._emit("tool_calls.function.arguments.done",{name:o.function.name,index:t,arguments:o.function.arguments,parsed_arguments:Kt(e)?e.$parseRaw(o.function.arguments):(null==e?void 0:e.function.strict)?JSON.parse(o.function.arguments):null})}else o.type},Ts=function(e){var t,s;const n=Ds(this,Ps,"m",Cs).call(this,e);if(e.message.content&&!n.content_done){n.content_done=!0;const t=Ds(this,Ps,"m",Ms).call(this);this._emit("content.done",{content:e.message.content,parsed:t?t.$parseRaw(e.message.content):null})}e.message.refusal&&!n.refusal_done&&(n.refusal_done=!0,this._emit("refusal.done",{refusal:e.message.refusal})),(null==(t=e.logprobs)?void 0:t.content)&&!n.logprobs_content_done&&(n.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:e.logprobs.content})),(null==(s=e.logprobs)?void 0:s.refusal)&&!n.logprobs_refusal_done&&(n.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:e.logprobs.refusal}))},js=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");const e=Ds(this,Es,"f");if(!e)throw new I("request ended without sending any chunks");return Bs(this,Es,void 0,"f"),Bs(this,Rs,[],"f"),function(e,t){const{id:s,choices:n,created:r,model:o,system_fingerprint:i,...a}=e,l={...a,id:s,choices:n.map((({message:t,finish_reason:s,index:n,logprobs:r,...o})=>{if(!s)throw new I(`missing finish_reason for choice ${n}`);const{content:i=null,function_call:a,tool_calls:l,...c}=t,u=t.role;if(!u)throw new I(`missing role for choice ${n}`);if(a){const{arguments:e,name:l}=a;if(null==e)throw new I(`missing function_call.arguments for choice ${n}`);if(!l)throw new I(`missing function_call.name for choice ${n}`);return{...o,message:{content:i,function_call:{arguments:e,name:l},role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}}return l?{...o,index:n,finish_reason:s,logprobs:r,message:{...c,role:u,content:i,refusal:t.refusal??null,tool_calls:l.map(((t,s)=>{const{function:r,type:o,id:i,...a}=t,{arguments:l,name:c,...u}=r||{};if(null==i)throw new I(`missing choices[${n}].tool_calls[${s}].id\n${Ls(e)}`);if(null==o)throw new I(`missing choices[${n}].tool_calls[${s}].type\n${Ls(e)}`);if(null==c)throw new I(`missing choices[${n}].tool_calls[${s}].function.name\n${Ls(e)}`);if(null==l)throw new I(`missing choices[${n}].tool_calls[${s}].function.arguments\n${Ls(e)}`);return{...a,id:i,type:o,function:{...u,name:c,arguments:l}}}))}}:{...o,message:{...c,content:i,role:u,refusal:t.refusal??null},finish_reason:s,index:n,logprobs:r}})),created:r,model:o,object:"chat.completion",...i?{system_fingerprint:i}:{}};return function(e,t){return t&&Yt(t)?zt(e,t):{...e,choices:e.choices.map((e=>({...e,message:{...e.message,parsed:null,...e.message.tool_calls?{tool_calls:e.message.tool_calls}:void 0}})))}}(l,t)}(e,Ds(this,$s,"f"))},Ms=function(){var e;const t=null==(e=Ds(this,$s,"f"))?void 0:e.response_format;return Vt(t)?t:null},Ns=function(e){var t,s,n,r;let o=Ds(this,Es,"f");const{choices:i,...a}=e;o?Object.assign(o,a):o=Bs(this,Es,{...a,choices:[]},"f");for(const{delta:l,finish_reason:c,index:u,logprobs:d=null,...h}of e.choices){let e=o.choices[u];if(e||(e=o.choices[u]={finish_reason:c,index:u,message:{},logprobs:d,...h}),d)if(e.logprobs){const{content:n,refusal:r,...o}=d;Object.assign(e.logprobs,o),n&&((t=e.logprobs).content??(t.content=[]),e.logprobs.content.push(...n)),r&&((s=e.logprobs).refusal??(s.refusal=[]),e.logprobs.refusal.push(...r))}else e.logprobs=Object.assign({},d);if(c&&(e.finish_reason=c,Ds(this,$s,"f")&&Yt(Ds(this,$s,"f")))){if("length"===c)throw new W;if("content_filter"===c)throw new U}if(Object.assign(e,h),!l)continue;const{content:i,refusal:a,function_call:f,role:p,tool_calls:m,...g}=l;if(Object.assign(e.message,g),a&&(e.message.refusal=(e.message.refusal||"")+a),p&&(e.message.role=p),f&&(e.message.function_call?(f.name&&(e.message.function_call.name=f.name),f.arguments&&((n=e.message.function_call).arguments??(n.arguments=""),e.message.function_call.arguments+=f.arguments)):e.message.function_call=f),i&&(e.message.content=(e.message.content||"")+i,!e.message.refusal&&Ds(this,Ps,"m",Ms).call(this)&&(e.message.parsed=As(e.message.content))),m){e.message.tool_calls||(e.message.tool_calls=[]);for(const{index:t,id:s,type:n,function:o,...i}of m){const a=(r=e.message.tool_calls)[t]??(r[t]={});Object.assign(a,i),s&&(a.id=s),n&&(a.type=n),o&&(a.function??(a.function={name:o.name??"",arguments:""})),(null==o?void 0:o.name)&&(a.function.name=o.name),(null==o?void 0:o.arguments)&&(a.function.arguments+=o.arguments,Gt(Ds(this,$s,"f"),a)&&(a.function.parsed_arguments=As(a.function.arguments)))}}}return o},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("chunk",(s=>{const n=t.shift();n?n.resolve(s):e.push(s)})),this.on("end",(()=>{s=!0;for(const e of t)e.resolve(void 0);t.length=0})),this.on("abort",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),this.on("error",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),{next:async()=>{if(!e.length)return s?{value:void 0,done:!0}:new Promise(((e,s)=>t.push({resolve:e,reject:s}))).then((e=>e?{value:e,done:!1}:{value:void 0,done:!0}));return{value:e.shift(),done:!1}},return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new G(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Ls(e){return JSON.stringify(e)}class Fs extends qs{static fromReadableStream(e){const t=new Fs(null);return t._run((()=>t._fromReadableStream(e))),t}static runFunctions(e,t,s){const n=new Fs(null),r={...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"runFunctions"}};return n._run((()=>n._runFunctions(e,t,r))),n}static runTools(e,t,s){const n=new Fs(t),r={...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return n._run((()=>n._runTools(e,t,r))),n}}let Ws=class extends We{parse(e,t){return function(e){for(const t of e??[]){if("function"!==t.type)throw new I(`Currently only \`function\` tool types support auto-parsing; Received \`${t.type}\``);if(!0!==t.function.strict)throw new I(`The \`${t.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}(e.tools),this._client.chat.completions.create(e,{...t,headers:{...null==t?void 0:t.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap((t=>zt(t,e)))}runFunctions(e,t){return e.stream?Fs.runFunctions(this._client,e,t):us.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?Fs.runTools(this._client,e,t):us.runTools(this._client,e,t)}stream(e,t){return qs.createChatCompletion(this._client,e,t)}};class Us extends We{constructor(){super(...arguments),this.completions=new Ws(this._client)}}(Us||(Us={})).Completions=Ws;class Xs extends We{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}}class Js extends We{create(e,t){return this._client.post("/realtime/transcription_sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}}class Hs extends We{constructor(){super(...arguments),this.sessions=new Xs(this._client),this.transcriptionSessions=new Js(this._client)}}Hs.Sessions=Xs,Hs.TranscriptionSessions=Js;class Vs extends We{create(e,t,s){return this._client.post(`/threads/${e}/messages`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}retrieve(e,t,s){return this._client.get(`/threads/${e}/messages/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}update(e,t,s,n){return this._client.post(`/threads/${e}/messages/${t}`,{body:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers}})}list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,Ks,{query:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}del(e,t,s){return this._client.delete(`/threads/${e}/messages/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}}class Ks extends Fe{}Vs.MessagesPage=Ks;class zs extends We{retrieve(e,t,s,n={},r){return ve(n)?this.retrieve(e,t,s,{},n):this._client.get(`/threads/${e}/runs/${t}/steps/${s}`,{query:n,...r,headers:{"OpenAI-Beta":"assistants=v2",...null==r?void 0:r.headers}})}list(e,t,s={},n){return ve(s)?this.list(e,t,{},s):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,Qs,{query:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers}})}}class Qs extends Fe{}zs.RunStepsPage=Qs;let Gs=class extends We{constructor(){super(...arguments),this.steps=new zs(this._client)}create(e,t,s){const{include:n,...r}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:n},body:r,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers},stream:t.stream??!1})}retrieve(e,t,s){return this._client.get(`/threads/${e}/runs/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}update(e,t,s,n){return this._client.post(`/threads/${e}/runs/${t}`,{body:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers}})}list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,Ys,{query:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}cancel(e,t,s){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}async createAndPoll(e,t,s){const n=await this.create(e,t,s);return await this.poll(e,n.id,s)}createAndStream(e,t,s){return Lt.createAssistantStream(e,this._client.beta.threads.runs,t,s)}async poll(e,t,s){const n={...null==s?void 0:s.headers,"X-Stainless-Poll-Helper":"true"};for((null==s?void 0:s.pollIntervalMs)&&(n["X-Stainless-Custom-Poll-Interval"]=s.pollIntervalMs.toString());;){const{data:r,response:o}=await this.retrieve(e,t,{...s,headers:{...null==s?void 0:s.headers,...n}}).withResponse();switch(r.status){case"queued":case"in_progress":case"cancelling":let e=5e3;if(null==s?void 0:s.pollIntervalMs)e=s.pollIntervalMs;else{const t=o.headers.get("openai-poll-after-ms");if(t){const s=parseInt(t);isNaN(s)||(e=s)}}await Ee(e);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return r}}}stream(e,t,s){return Lt.createAssistantStream(e,this._client.beta.threads.runs,t,s)}submitToolOutputs(e,t,s,n){return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers},stream:s.stream??!1})}async submitToolOutputsAndPoll(e,t,s,n){const r=await this.submitToolOutputs(e,t,s,n);return await this.poll(e,r.id,n)}submitToolOutputsStream(e,t,s,n){return Lt.createToolAssistantStream(e,t,this._client.beta.threads.runs,s,n)}};class Ys extends Fe{}Gs.RunsPage=Ys,Gs.Steps=zs,Gs.RunStepsPage=Qs;class Zs extends We{constructor(){super(...arguments),this.runs=new Gs(this._client),this.messages=new Vs(this._client)}create(e={},t){return ve(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}update(e,t,s){return this._client.post(`/threads/${e}`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}createAndRun(e,t){return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers},stream:e.stream??!1})}async createAndRunPoll(e,t){const s=await this.createAndRun(e,t);return await this.runs.poll(s.thread_id,s.id,t)}createAndRunStream(e,t){return Lt.createThreadAssistantStream(e,this._client.beta.threads,t)}}Zs.Runs=Gs,Zs.RunsPage=Ys,Zs.Messages=Vs,Zs.MessagesPage=Ks;class en extends We{constructor(){super(...arguments),this.realtime=new Hs(this._client),this.chat=new Us(this._client),this.assistants=new Ft(this._client),this.threads=new Zs(this._client)}}en.Realtime=Hs,en.Assistants=Ft,en.AssistantsPage=Wt,en.Threads=Zs;class tn extends We{create(e,t){return this._client.post("/completions",{body:e,...t,stream:e.stream??!1})}}class sn extends We{create(e,t){const s=!!e.encoding_format;let n=s?e.encoding_format:"base64";s&&Ne(0,"User defined encoding_format:",e.encoding_format);const r=this._client.post("/embeddings",{body:{...e,encoding_format:n},...t});return s?r:(Ne(0,"Decoding base64 embeddings to float32 array"),r._thenUnwrap((e=>(e&&e.data&&e.data.forEach((e=>{const t=e.embedding;e.embedding=(e=>{if("undefined"!=typeof Buffer){const t=Buffer.from(e,"base64");return Array.from(new Float32Array(t.buffer,t.byteOffset,t.length/Float32Array.BYTES_PER_ELEMENT))}{const t=atob(e),s=t.length,n=new Uint8Array(s);for(let e=0;e<s;e++)n[e]=t.charCodeAt(e);return Array.from(new Float32Array(n.buffer))}})(t)})),e))))}}class nn extends We{retrieve(e,t,s,n){return this._client.get(`/evals/${e}/runs/${t}/output_items/${s}`,n)}list(e,t,s={},n){return ve(s)?this.list(e,t,{},s):this._client.getAPIList(`/evals/${e}/runs/${t}/output_items`,rn,{query:s,...n})}}class rn extends Fe{}nn.OutputItemListResponsesPage=rn;class on extends We{constructor(){super(...arguments),this.outputItems=new nn(this._client)}create(e,t,s){return this._client.post(`/evals/${e}/runs`,{body:t,...s})}retrieve(e,t,s){return this._client.get(`/evals/${e}/runs/${t}`,s)}list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/evals/${e}/runs`,an,{query:t,...s})}del(e,t,s){return this._client.delete(`/evals/${e}/runs/${t}`,s)}cancel(e,t,s){return this._client.post(`/evals/${e}/runs/${t}`,s)}}class an extends Fe{}on.RunListResponsesPage=an,on.OutputItems=nn,on.OutputItemListResponsesPage=rn;class ln extends We{constructor(){super(...arguments),this.runs=new on(this._client)}create(e,t){return this._client.post("/evals",{body:e,...t})}retrieve(e,t){return this._client.get(`/evals/${e}`,t)}update(e,t,s){return this._client.post(`/evals/${e}`,{body:t,...s})}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/evals",cn,{query:e,...t})}del(e,t){return this._client.delete(`/evals/${e}`,t)}}class cn extends Fe{}ln.EvalListResponsesPage=cn,ln.Runs=on,ln.RunListResponsesPage=an;let un=class extends We{create(e,t){return this._client.post("/files",ie({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/files",dn,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/binary",...null==t?void 0:t.headers},__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,t)}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:s=18e5}={}){const n=new Set(["processed","error","deleted"]),r=Date.now();let o=await this.retrieve(e);for(;!o.status||!n.has(o.status);)if(await Ee(t),o=await this.retrieve(e),Date.now()-r>s)throw new T({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return o}};class dn extends Fe{}un.FileObjectsPage=dn;class hn extends We{create(e,t,s){return this._client.getAPIList(`/fine_tuning/checkpoints/${e}/permissions`,fn,{body:t,method:"post",...s})}retrieve(e,t={},s){return ve(t)?this.retrieve(e,{},t):this._client.get(`/fine_tuning/checkpoints/${e}/permissions`,{query:t,...s})}del(e,t,s){return this._client.delete(`/fine_tuning/checkpoints/${e}/permissions/${t}`,s)}}class fn extends Le{}hn.PermissionCreateResponsesPage=fn;let pn=class extends We{constructor(){super(...arguments),this.permissions=new hn(this._client)}};pn.Permissions=hn,pn.PermissionCreateResponsesPage=fn;class mn extends We{list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,gn,{query:t,...s})}}class gn extends Fe{}mn.FineTuningJobCheckpointsPage=gn;class yn extends We{constructor(){super(...arguments),this.checkpoints=new mn(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",wn,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},s){return ve(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,vn,{query:t,...s})}}class wn extends Fe{}class vn extends Fe{}yn.FineTuningJobsPage=wn,yn.FineTuningJobEventsPage=vn,yn.Checkpoints=mn,yn.FineTuningJobCheckpointsPage=gn;class _n extends We{constructor(){super(...arguments),this.jobs=new yn(this._client),this.checkpoints=new pn(this._client)}}_n.Jobs=yn,_n.FineTuningJobsPage=wn,_n.FineTuningJobEventsPage=vn,_n.Checkpoints=pn;class bn extends We{createVariation(e,t){return this._client.post("/images/variations",ie({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",ie({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class xn extends We{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",Sn,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}}class Sn extends Le{}xn.ModelsPage=Sn;class An extends We{create(e,t){return this._client.post("/moderations",{body:e,...t})}}function Pn(e,t){return t&&function(e){var t;if(Vt(null==(t=e.text)?void 0:t.format))return!0;return!1}(t)?$n(e,t):{...e,output_parsed:null,output:e.output.map((e=>"function_call"===e.type?{...e,parsed_arguments:null}:"message"===e.type?{...e,content:e.content.map((e=>({...e,parsed:null})))}:e))}}function $n(e,t){const s=e.output.map((e=>{if("function_call"===e.type)return{...e,parsed_arguments:En(t,e)};if("message"===e.type){const s=e.content.map((e=>"output_text"===e.type?{...e,parsed:Rn(t,e.text)}:e));return{...e,content:s}}return e})),n=Object.assign({},e,{output:s});return Object.getOwnPropertyDescriptor(e,"output_text")||In(n),Object.defineProperty(n,"output_parsed",{enumerable:!0,get(){for(const e of n.output)if("message"===e.type)for(const t of e.content)if("output_text"===t.type&&null!==t.parsed)return t.parsed;return null}}),n}function Rn(e,t){var s,n,r,o;if("json_schema"!==(null==(n=null==(s=e.text)?void 0:s.format)?void 0:n.type))return null;if("$parseRaw"in(null==(r=e.text)?void 0:r.format)){return(null==(o=e.text)?void 0:o.format).$parseRaw(t)}return JSON.parse(t)}function En(e,t){const s=(n=e.tools??[],r=t.name,n.find((e=>"function"===e.type&&e.name===r)));var n,r,o;return{...t,...t,parsed_arguments:(o=s,"auto-parseable-tool"===(null==o?void 0:o.$brand)?s.$parseRaw(t.arguments):(null==s?void 0:s.strict)?JSON.parse(t.arguments):null)}}function In(e){const t=[];for(const s of e.output)if("message"===s.type)for(const e of s.content)"output_text"===e.type&&t.push(e.text);e.output_text=t.join("")}class Cn extends We{list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/responses/${e}/input_items`,Un,{query:t,...s})}}var On,kn,Tn,jn,Mn,Nn,Bn,Dn,qn=function(e,t,s,n,r){if("m"===n)throw new TypeError("Private method is not writable");if("a"===n&&!r)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!r:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(e,s):r?r.value=s:t.set(e,s),s},Ln=function(e,t,s,n){if("a"===s&&!n)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!n:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===s?n:"a"===s?n.call(e):n?n.value:t.get(e)};class Fn extends mt{constructor(e){super(),On.add(this),kn.set(this,void 0),Tn.set(this,void 0),jn.set(this,void 0),qn(this,kn,e,"f")}static createResponse(e,t,s){const n=new Fn(t);return n._run((()=>n._createResponse(e,t,{...s,headers:{...null==s?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}}))),n}async _createResponse(e,t,s){var n;const r=null==s?void 0:s.signal;r&&(r.aborted&&this.controller.abort(),r.addEventListener("abort",(()=>this.controller.abort()))),Ln(this,On,"m",Mn).call(this);const o=await e.responses.create({...t,stream:!0},{...s,signal:this.controller.signal});this._connected();for await(const i of o)Ln(this,On,"m",Nn).call(this,i);if(null==(n=o.controller.signal)?void 0:n.aborted)throw new O;return Ln(this,On,"m",Bn).call(this)}[(kn=new WeakMap,Tn=new WeakMap,jn=new WeakMap,On=new WeakSet,Mn=function(){this.ended||qn(this,Tn,void 0,"f")},Nn=function(e){if(this.ended)return;const t=Ln(this,On,"m",Dn).call(this,e);switch(this._emit("event",e),e.type){case"response.output_text.delta":{const s=t.output[e.output_index];if(!s)throw new I(`missing output at index ${e.output_index}`);if("message"===s.type){const t=s.content[e.content_index];if(!t)throw new I(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new I(`expected content to be 'output_text', got ${t.type}`);this._emit("response.output_text.delta",{...e,snapshot:t.text})}break}case"response.function_call_arguments.delta":{const s=t.output[e.output_index];if(!s)throw new I(`missing output at index ${e.output_index}`);"function_call"===s.type&&this._emit("response.function_call_arguments.delta",{...e,snapshot:s.arguments});break}default:this._emit(e.type,e)}},Bn=function(){if(this.ended)throw new I("stream has ended, this shouldn't happen");const e=Ln(this,Tn,"f");if(!e)throw new I("request ended without sending any events");qn(this,Tn,void 0,"f");const t=function(e,t){return Pn(e,t)}(e,Ln(this,kn,"f"));return qn(this,jn,t,"f"),t},Dn=function(e){let t=Ln(this,Tn,"f");if(!t){if("response.created"!==e.type)throw new I(`When snapshot hasn't been set yet, expected 'response.created' event, got ${e.type}`);return t=qn(this,Tn,e.response,"f"),t}switch(e.type){case"response.output_item.added":t.output.push(e.item);break;case"response.content_part.added":{const s=t.output[e.output_index];if(!s)throw new I(`missing output at index ${e.output_index}`);"message"===s.type&&s.content.push(e.part);break}case"response.output_text.delta":{const s=t.output[e.output_index];if(!s)throw new I(`missing output at index ${e.output_index}`);if("message"===s.type){const t=s.content[e.content_index];if(!t)throw new I(`missing content at index ${e.content_index}`);if("output_text"!==t.type)throw new I(`expected content to be 'output_text', got ${t.type}`);t.text+=e.delta}break}case"response.function_call_arguments.delta":{const s=t.output[e.output_index];if(!s)throw new I(`missing output at index ${e.output_index}`);"function_call"===s.type&&(s.arguments+=e.delta);break}case"response.completed":qn(this,Tn,e.response,"f")}return t},Symbol.asyncIterator)](){const e=[],t=[];let s=!1;return this.on("event",(s=>{const n=t.shift();n?n.resolve(s):e.push(s)})),this.on("end",(()=>{s=!0;for(const e of t)e.resolve(void 0);t.length=0})),this.on("abort",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),this.on("error",(e=>{s=!0;for(const s of t)s.reject(e);t.length=0})),{next:async()=>{if(!e.length)return s?{value:void 0,done:!0}:new Promise(((e,s)=>t.push({resolve:e,reject:s}))).then((e=>e?{value:e,done:!1}:{value:void 0,done:!0}));return{value:e.shift(),done:!1}},return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=Ln(this,jn,"f");if(!e)throw new I("stream ended without producing a ChatCompletion");return e}}class Wn extends We{constructor(){super(...arguments),this.inputItems=new Cn(this._client)}create(e,t){return this._client.post("/responses",{body:e,...t,stream:e.stream??!1})._thenUnwrap((e=>("object"in e&&"response"===e.object&&In(e),e)))}retrieve(e,t={},s){return ve(t)?this.retrieve(e,{},t):this._client.get(`/responses/${e}`,{query:t,...s})}del(e,t){return this._client.delete(`/responses/${e}`,{...t,headers:{Accept:"*/*",...null==t?void 0:t.headers}})}parse(e,t){return this._client.responses.create(e,t)._thenUnwrap((t=>$n(t,e)))}stream(e,t){return Fn.createResponse(this._client,e,t)}}class Un extends Fe{}Wn.InputItems=Cn;class Xn extends We{create(e,t,s){return this._client.post(`/uploads/${e}/parts`,ie({body:t,...s}))}}class Jn extends We{constructor(){super(...arguments),this.parts=new Xn(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,s){return this._client.post(`/uploads/${e}/complete`,{body:t,...s})}}Jn.Parts=Xn;class Hn extends We{create(e,t,s){return this._client.post(`/vector_stores/${e}/files`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}retrieve(e,t,s){return this._client.get(`/vector_stores/${e}/files/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}update(e,t,s,n){return this._client.post(`/vector_stores/${e}/files/${t}`,{body:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers}})}list(e,t={},s){return ve(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,Vn,{query:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}del(e,t,s){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}async createAndPoll(e,t,s){const n=await this.create(e,t,s);return await this.poll(e,n.id,s)}async poll(e,t,s){const n={...null==s?void 0:s.headers,"X-Stainless-Poll-Helper":"true"};for((null==s?void 0:s.pollIntervalMs)&&(n["X-Stainless-Custom-Poll-Interval"]=s.pollIntervalMs.toString());;){const r=await this.retrieve(e,t,{...s,headers:n}).withResponse(),o=r.data;switch(o.status){case"in_progress":let e=5e3;if(null==s?void 0:s.pollIntervalMs)e=s.pollIntervalMs;else{const t=r.response.headers.get("openai-poll-after-ms");if(t){const s=parseInt(t);isNaN(s)||(e=s)}}await Ee(e);break;case"failed":case"completed":return o}}}async upload(e,t,s){const n=await this._client.files.create({file:t,purpose:"assistants"},s);return this.create(e,{file_id:n.id},s)}async uploadAndPoll(e,t,s){const n=await this.upload(e,t,s);return await this.poll(e,n.id,s)}content(e,t,s){return this._client.getAPIList(`/vector_stores/${e}/files/${t}/content`,Kn,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}}class Vn extends Fe{}class Kn extends Le{}Hn.VectorStoreFilesPage=Vn,Hn.FileContentResponsesPage=Kn;class zn extends We{create(e,t,s){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}retrieve(e,t,s){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}cancel(e,t,s){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}async createAndPoll(e,t,s){const n=await this.create(e,t);return await this.poll(e,n.id,s)}listFiles(e,t,s={},n){return ve(s)?this.listFiles(e,t,{},s):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,Vn,{query:s,...n,headers:{"OpenAI-Beta":"assistants=v2",...null==n?void 0:n.headers}})}async poll(e,t,s){const n={...null==s?void 0:s.headers,"X-Stainless-Poll-Helper":"true"};for((null==s?void 0:s.pollIntervalMs)&&(n["X-Stainless-Custom-Poll-Interval"]=s.pollIntervalMs.toString());;){const{data:r,response:o}=await this.retrieve(e,t,{...s,headers:n}).withResponse();switch(r.status){case"in_progress":let e=5e3;if(null==s?void 0:s.pollIntervalMs)e=s.pollIntervalMs;else{const t=o.headers.get("openai-poll-after-ms");if(t){const s=parseInt(t);isNaN(s)||(e=s)}}await Ee(e);break;case"failed":case"cancelled":case"completed":return r}}}async uploadAndPoll(e,{files:t,fileIds:s=[]},n){if(null==t||0==t.length)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const r=(null==n?void 0:n.maxConcurrency)??5,o=Math.min(r,t.length),i=this._client,a=t.values(),l=[...s];const c=Array(o).fill(a).map((async function(e){for(let t of e){const e=await i.files.create({file:t,purpose:"assistants"},n);l.push(e.id)}}));return await(async e=>{const t=await Promise.allSettled(e),s=t.filter((e=>"rejected"===e.status));if(s.length){for(const e of s);throw new Error(`${s.length} promise(s) failed - see the above errors`)}const n=[];for(const r of t)"fulfilled"===r.status&&n.push(r.value);return n})(c),await this.createAndPoll(e,{file_ids:l})}}class Qn extends We{constructor(){super(...arguments),this.files=new Hn(this._client),this.fileBatches=new zn(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}update(e,t,s){return this._client.post(`/vector_stores/${e}`,{body:t,...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}list(e={},t){return ve(e)?this.list({},e):this._client.getAPIList("/vector_stores",Gn,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...null==t?void 0:t.headers}})}search(e,t,s){return this._client.getAPIList(`/vector_stores/${e}/search`,Yn,{body:t,method:"post",...s,headers:{"OpenAI-Beta":"assistants=v2",...null==s?void 0:s.headers}})}}class Gn extends Fe{}class Yn extends Le{}var Zn;Qn.VectorStoresPage=Gn,Qn.VectorStoreSearchResponsesPage=Yn,Qn.Files=Hn,Qn.VectorStoreFilesPage=Vn,Qn.FileContentResponsesPage=Kn,Qn.FileBatches=zn;class er extends pe{constructor({baseURL:e=Oe("OPENAI_BASE_URL"),apiKey:t=Oe("OPENAI_API_KEY"),organization:s=Oe("OPENAI_ORG_ID")??null,project:n=Oe("OPENAI_PROJECT_ID")??null,...r}={}){if(void 0===t)throw new I("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const o={apiKey:t,organization:s,project:n,...r,baseURL:e||"https://api.openai.com/v1"};if(!o.dangerouslyAllowBrowser&&"undefined"!=typeof window&&void 0!==window.document&&"undefined"!=typeof navigator)throw new I("It looks like you're running in a browser-like environment.\n\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew OpenAI({ apiKey, dangerouslyAllowBrowser: true });\n\nhttps://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety\n");super({baseURL:o.baseURL,timeout:o.timeout??6e5,httpAgent:o.httpAgent,maxRetries:o.maxRetries,fetch:o.fetch}),this.completions=new tn(this),this.chat=new Ve(this),this.embeddings=new sn(this),this.files=new un(this),this.images=new bn(this),this.audio=new Ge(this),this.moderations=new An(this),this.models=new xn(this),this.fineTuning=new _n(this),this.vectorStores=new Qn(this),this.beta=new en(this),this.batches=new Ye(this),this.uploads=new Jn(this),this.responses=new Wn(this),this.evals=new ln(this),this._options=o,this.apiKey=t,this.organization=s,this.project=n}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return m(e,{arrayFormat:"brackets"})}}Zn=er,er.OpenAI=Zn,er.DEFAULT_TIMEOUT=6e5,er.OpenAIError=I,er.APIError=C,er.APIConnectionError=k,er.APIConnectionTimeoutError=T,er.APIUserAbortError=O,er.NotFoundError=B,er.ConflictError=D,er.RateLimitError=L,er.BadRequestError=j,er.AuthenticationError=M,er.InternalServerError=F,er.PermissionDeniedError=N,er.UnprocessableEntityError=q,er.toFile=se,er.fileFromPath=A,er.Completions=tn,er.Chat=Ve,er.ChatCompletionsPage=Je,er.Embeddings=sn,er.Files=un,er.FileObjectsPage=dn,er.Images=bn,er.Audio=Ge,er.Moderations=An,er.Models=xn,er.ModelsPage=Sn,er.FineTuning=_n,er.VectorStores=Qn,er.VectorStoresPage=Gn,er.VectorStoreSearchResponsesPage=Yn,er.Beta=en,er.Batches=Ye,er.BatchesPage=Ze,er.Uploads=Jn,er.Responses=Wn,er.Evals=ln,er.EvalListResponsesPage=cn;export{er as O};
