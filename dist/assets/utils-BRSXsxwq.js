const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-CVIejru_.js","assets/mui-core-CZAN88TW.js"])))=>i.map(i=>d[i]);
function t(t,e){return function(){return t.apply(e,arguments)}}const{toString:e}=Object.prototype,{getPrototypeOf:n}=Object,{iterator:r,toStringTag:i}=Symbol,o=(t=>n=>{const r=e.call(n);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),a=t=>(t=t.toLowerCase(),e=>o(e)===t),s=t=>e=>typeof e===t,{isArray:l}=Array,u=s("undefined");const c=a("ArrayBuffer");const h=s("string"),f=s("function"),d=s("number"),p=t=>null!==t&&"object"==typeof t,g=t=>{if("object"!==o(t))return!1;const e=n(t);return!(null!==e&&e!==Object.prototype&&null!==Object.getPrototypeOf(e)||i in t||r in t)},m=a("Date"),v=a("File"),b=a("Blob"),y=a("FileList"),w=a("URLSearchParams"),[x,N,L,A]=["ReadableStream","Request","Response","Headers"].map(a);function S(t,e,{allOwnKeys:n=!1}={}){if(null==t)return;let r,i;if("object"!=typeof t&&(t=[t]),l(t))for(r=0,i=t.length;r<i;r++)e.call(null,t[r],r,t);else{const i=n?Object.getOwnPropertyNames(t):Object.keys(t),o=i.length;let a;for(r=0;r<o;r++)a=i[r],e.call(null,t[a],a,t)}}function _(t,e){e=e.toLowerCase();const n=Object.keys(t);let r,i=n.length;for(;i-- >0;)if(r=n[i],e===r.toLowerCase())return r;return null}const P="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,k=t=>!u(t)&&t!==P;const F=(t=>e=>t&&e instanceof t)("undefined"!=typeof Uint8Array&&n(Uint8Array)),C=a("HTMLFormElement"),j=(({hasOwnProperty:t})=>(e,n)=>t.call(e,n))(Object.prototype),I=a("RegExp"),O=(t,e)=>{const n=Object.getOwnPropertyDescriptors(t),r={};S(n,((n,i)=>{let o;!1!==(o=e(n,i,t))&&(r[i]=o||n)})),Object.defineProperties(t,r)};const E=a("AsyncFunction"),R=(B="function"==typeof setImmediate,D=f(P.postMessage),B?setImmediate:D?(T=`axios@${Math.random()}`,M=[],P.addEventListener("message",(({source:t,data:e})=>{t===P&&e===T&&M.length&&M.shift()()}),!1),t=>{M.push(t),P.postMessage(T,"*")}):t=>setTimeout(t));var B,D,T,M;const q="undefined"!=typeof queueMicrotask?queueMicrotask.bind(P):"undefined"!=typeof process&&process.nextTick||R,U={isArray:l,isArrayBuffer:c,isBuffer:function(t){return null!==t&&!u(t)&&null!==t.constructor&&!u(t.constructor)&&f(t.constructor.isBuffer)&&t.constructor.isBuffer(t)},isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||f(t.append)&&("formdata"===(e=o(t))||"object"===e&&f(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){let e;return e="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&c(t.buffer),e},isString:h,isNumber:d,isBoolean:t=>!0===t||!1===t,isObject:p,isPlainObject:g,isReadableStream:x,isRequest:N,isResponse:L,isHeaders:A,isUndefined:u,isDate:m,isFile:v,isBlob:b,isRegExp:I,isFunction:f,isStream:t=>p(t)&&f(t.pipe),isURLSearchParams:w,isTypedArray:F,isFileList:y,forEach:S,merge:function t(){const{caseless:e}=k(this)&&this||{},n={},r=(r,i)=>{const o=e&&_(n,i)||i;g(n[o])&&g(r)?n[o]=t(n[o],r):g(r)?n[o]=t({},r):l(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&S(arguments[i],r);return n},extend:(e,n,r,{allOwnKeys:i}={})=>(S(n,((n,i)=>{r&&f(n)?e[i]=t(n,r):e[i]=n}),{allOwnKeys:i}),e),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,n,r)=>{t.prototype=Object.create(e.prototype,r),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),n&&Object.assign(t.prototype,n)},toFlatObject:(t,e,r,i)=>{let o,a,s;const l={};if(e=e||{},null==t)return e;do{for(o=Object.getOwnPropertyNames(t),a=o.length;a-- >0;)s=o[a],i&&!i(s,t,e)||l[s]||(e[s]=t[s],l[s]=!0);t=!1!==r&&n(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},kindOf:o,kindOfTest:a,endsWith:(t,e,n)=>{t=String(t),(void 0===n||n>t.length)&&(n=t.length),n-=e.length;const r=t.indexOf(e,n);return-1!==r&&r===n},toArray:t=>{if(!t)return null;if(l(t))return t;let e=t.length;if(!d(e))return null;const n=new Array(e);for(;e-- >0;)n[e]=t[e];return n},forEachEntry:(t,e)=>{const n=(t&&t[r]).call(t);let i;for(;(i=n.next())&&!i.done;){const n=i.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let n;const r=[];for(;null!==(n=t.exec(e));)r.push(n);return r},isHTMLForm:C,hasOwnProperty:j,hasOwnProp:j,reduceDescriptors:O,freezeMethods:t=>{O(t,((e,n)=>{if(f(t)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=t[n];f(r)&&(e.enumerable=!1,"writable"in e?e.writable=!1:e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(t,e)=>{const n={},r=t=>{t.forEach((t=>{n[t]=!0}))};return l(t)?r(t):r(String(t).split(e)),n},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(t,e,n){return e.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:_,global:P,isContextDefined:k,isSpecCompliantForm:function(t){return!!(t&&f(t.append)&&"FormData"===t[i]&&t[r])},toJSONObject:t=>{const e=new Array(10),n=(t,r)=>{if(p(t)){if(e.indexOf(t)>=0)return;if(!("toJSON"in t)){e[r]=t;const i=l(t)?[]:{};return S(t,((t,e)=>{const o=n(t,r+1);!u(o)&&(i[e]=o)})),e[r]=void 0,i}}return t};return n(t,0)},isAsyncFn:E,isThenable:t=>t&&(p(t)||f(t))&&f(t.then)&&f(t.catch),setImmediate:R,asap:q,isIterable:t=>null!=t&&f(t[r])};function z(t,e,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=t,this.name="AxiosError",e&&(this.code=e),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}U.inherits(z,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:U.toJSONObject(this.config),code:this.code,status:this.status}}});const W=z.prototype,H={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((t=>{H[t]={value:t}})),Object.defineProperties(z,H),Object.defineProperty(W,"isAxiosError",{value:!0}),z.from=(t,e,n,r,i,o)=>{const a=Object.create(W);return U.toFlatObject(t,a,(function(t){return t!==Error.prototype}),(t=>"isAxiosError"!==t)),z.call(a,t.message,e,n,r,i),a.cause=t,a.name=t.name,o&&Object.assign(a,o),a};function V(t){return U.isPlainObject(t)||U.isArray(t)}function G(t){return U.endsWith(t,"[]")?t.slice(0,-2):t}function J(t,e,n){return t?t.concat(e).map((function(t,e){return t=G(t),!n&&e?"["+t+"]":t})).join(n?".":""):e}const Y=U.toFlatObject(U,{},null,(function(t){return/^is[A-Z]/.test(t)}));function K(t,e,n){if(!U.isObject(t))throw new TypeError("target must be an object");e=e||new FormData;const r=(n=U.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(t,e){return!U.isUndefined(e[t])}))).metaTokens,i=n.visitor||u,o=n.dots,a=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&U.isSpecCompliantForm(e);if(!U.isFunction(i))throw new TypeError("visitor must be a function");function l(t){if(null===t)return"";if(U.isDate(t))return t.toISOString();if(!s&&U.isBlob(t))throw new z("Blob is not supported. Use a Buffer instead.");return U.isArrayBuffer(t)||U.isTypedArray(t)?s&&"function"==typeof Blob?new Blob([t]):Buffer.from(t):t}function u(t,n,i){let s=t;if(t&&!i&&"object"==typeof t)if(U.endsWith(n,"{}"))n=r?n:n.slice(0,-2),t=JSON.stringify(t);else if(U.isArray(t)&&function(t){return U.isArray(t)&&!t.some(V)}(t)||(U.isFileList(t)||U.endsWith(n,"[]"))&&(s=U.toArray(t)))return n=G(n),s.forEach((function(t,r){!U.isUndefined(t)&&null!==t&&e.append(!0===a?J([n],r,o):null===a?n:n+"[]",l(t))})),!1;return!!V(t)||(e.append(J(i,n,o),l(t)),!1)}const c=[],h=Object.assign(Y,{defaultVisitor:u,convertValue:l,isVisitable:V});if(!U.isObject(t))throw new TypeError("data must be an object");return function t(n,r){if(!U.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),U.forEach(n,(function(n,o){!0===(!(U.isUndefined(n)||null===n)&&i.call(e,n,U.isString(o)?o.trim():o,r,h))&&t(n,r?r.concat(o):[o])})),c.pop()}}(t),e}function X(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,(function(t){return e[t]}))}function $(t,e){this._pairs=[],t&&K(t,this,e)}const Z=$.prototype;function Q(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function tt(t,e,n){if(!e)return t;const r=n&&n.encode||Q;U.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(o=i?i(e,n):U.isURLSearchParams(e)?e.toString():new $(e,n).toString(r),o){const e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+o}return t}Z.append=function(t,e){this._pairs.push([t,e])},Z.toString=function(t){const e=t?function(e){return t.call(this,e,X)}:X;return this._pairs.map((function(t){return e(t[0])+"="+e(t[1])}),"").join("&")};class et{constructor(){this.handlers=[]}use(t,e,n){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){U.forEach(this.handlers,(function(e){null!==e&&t(e)}))}}const nt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},rt={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:$,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},it="undefined"!=typeof window&&"undefined"!=typeof document,ot="object"==typeof navigator&&navigator||void 0,at=it&&(!ot||["ReactNative","NativeScript","NS"].indexOf(ot.product)<0),st="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,lt=it&&window.location.href||"http://localhost",ut={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:it,hasStandardBrowserEnv:at,hasStandardBrowserWebWorkerEnv:st,navigator:ot,origin:lt},Symbol.toStringTag,{value:"Module"})),...rt};function ct(t){function e(t,n,r,i){let o=t[i++];if("__proto__"===o)return!0;const a=Number.isFinite(+o),s=i>=t.length;if(o=!o&&U.isArray(r)?r.length:o,s)return U.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!a;r[o]&&U.isObject(r[o])||(r[o]=[]);return e(t,n,r[o],i)&&U.isArray(r[o])&&(r[o]=function(t){const e={},n=Object.keys(t);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],e[o]=t[o];return e}(r[o])),!a}if(U.isFormData(t)&&U.isFunction(t.entries)){const n={};return U.forEachEntry(t,((t,r)=>{e(function(t){return U.matchAll(/\w+|\[(\w*)]/g,t).map((t=>"[]"===t[0]?"":t[1]||t[0]))}(t),r,n,0)})),n}return null}const ht={transitional:nt,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){const n=e.getContentType()||"",r=n.indexOf("application/json")>-1,i=U.isObject(t);i&&U.isHTMLForm(t)&&(t=new FormData(t));if(U.isFormData(t))return r?JSON.stringify(ct(t)):t;if(U.isArrayBuffer(t)||U.isBuffer(t)||U.isStream(t)||U.isFile(t)||U.isBlob(t)||U.isReadableStream(t))return t;if(U.isArrayBufferView(t))return t.buffer;if(U.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(t,e){return K(t,new ut.classes.URLSearchParams,Object.assign({visitor:function(t,e,n,r){return ut.isNode&&U.isBuffer(t)?(this.append(e,t.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},e))}(t,this.formSerializer).toString();if((o=U.isFileList(t))||n.indexOf("multipart/form-data")>-1){const e=this.env&&this.env.FormData;return K(o?{"files[]":t}:t,e&&new e,this.formSerializer)}}return i||r?(e.setContentType("application/json",!1),function(t,e,n){if(U.isString(t))try{return(e||JSON.parse)(t),U.trim(t)}catch(Tr){if("SyntaxError"!==Tr.name)throw Tr}return(n||JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){const e=this.transitional||ht.transitional,n=e&&e.forcedJSONParsing,r="json"===this.responseType;if(U.isResponse(t)||U.isReadableStream(t))return t;if(t&&U.isString(t)&&(n&&!this.responseType||r)){const n=!(e&&e.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(Tr){if(n){if("SyntaxError"===Tr.name)throw z.from(Tr,z.ERR_BAD_RESPONSE,this,null,this.response);throw Tr}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ut.classes.FormData,Blob:ut.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};U.forEach(["delete","get","head","post","put","patch"],(t=>{ht.headers[t]={}}));const ft=U.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),dt=Symbol("internals");function pt(t){return t&&String(t).trim().toLowerCase()}function gt(t){return!1===t||null==t?t:U.isArray(t)?t.map(gt):String(t)}function mt(t,e,n,r,i){return U.isFunction(r)?r.call(this,e,n):(i&&(e=n),U.isString(e)?U.isString(r)?-1!==e.indexOf(r):U.isRegExp(r)?r.test(e):void 0:void 0)}let vt=class{constructor(t){t&&this.set(t)}set(t,e,n){const r=this;function i(t,e,n){const i=pt(e);if(!i)throw new Error("header name must be a non-empty string");const o=U.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||e]=gt(t))}const o=(t,e)=>U.forEach(t,((t,n)=>i(t,n,e)));if(U.isPlainObject(t)||t instanceof this.constructor)o(t,e);else if(U.isString(t)&&(t=t.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim()))o((t=>{const e={};let n,r,i;return t&&t.split("\n").forEach((function(t){i=t.indexOf(":"),n=t.substring(0,i).trim().toLowerCase(),r=t.substring(i+1).trim(),!n||e[n]&&ft[n]||("set-cookie"===n?e[n]?e[n].push(r):e[n]=[r]:e[n]=e[n]?e[n]+", "+r:r)})),e})(t),e);else if(U.isObject(t)&&U.isIterable(t)){let n,r,i={};for(const e of t){if(!U.isArray(e))throw TypeError("Object iterator must return a key-value pair");i[r=e[0]]=(n=i[r])?U.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}o(i,e)}else null!=t&&i(e,t,n);return this}get(t,e){if(t=pt(t)){const n=U.findKey(this,t);if(n){const t=this[n];if(!e)return t;if(!0===e)return function(t){const e=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(t);)e[r[1]]=r[2];return e}(t);if(U.isFunction(e))return e.call(this,t,n);if(U.isRegExp(e))return e.exec(t);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=pt(t)){const n=U.findKey(this,t);return!(!n||void 0===this[n]||e&&!mt(0,this[n],n,e))}return!1}delete(t,e){const n=this;let r=!1;function i(t){if(t=pt(t)){const i=U.findKey(n,t);!i||e&&!mt(0,n[i],i,e)||(delete n[i],r=!0)}}return U.isArray(t)?t.forEach(i):i(t),r}clear(t){const e=Object.keys(this);let n=e.length,r=!1;for(;n--;){const i=e[n];t&&!mt(0,this[i],i,t,!0)||(delete this[i],r=!0)}return r}normalize(t){const e=this,n={};return U.forEach(this,((r,i)=>{const o=U.findKey(n,i);if(o)return e[o]=gt(r),void delete e[i];const a=t?function(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((t,e,n)=>e.toUpperCase()+n))}(i):String(i).trim();a!==i&&delete e[i],e[a]=gt(r),n[a]=!0})),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const e=Object.create(null);return U.forEach(this,((n,r)=>{null!=n&&!1!==n&&(e[r]=t&&U.isArray(n)?n.join(", "):n)})),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([t,e])=>t+": "+e)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){const n=new this(t);return e.forEach((t=>n.set(t))),n}static accessor(t){const e=(this[dt]=this[dt]={accessors:{}}).accessors,n=this.prototype;function r(t){const r=pt(t);e[r]||(!function(t,e){const n=U.toCamelCase(" "+e);["get","set","has"].forEach((r=>{Object.defineProperty(t,r+n,{value:function(t,n,i){return this[r].call(this,e,t,n,i)},configurable:!0})}))}(n,t),e[r]=!0)}return U.isArray(t)?t.forEach(r):r(t),this}};function bt(t,e){const n=this||ht,r=e||n,i=vt.from(r.headers);let o=r.data;return U.forEach(t,(function(t){o=t.call(n,o,i.normalize(),e?e.status:void 0)})),i.normalize(),o}function yt(t){return!(!t||!t.__CANCEL__)}function wt(t,e,n){z.call(this,null==t?"canceled":t,z.ERR_CANCELED,e,n),this.name="CanceledError"}function xt(t,e,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?e(new z("Request failed with status code "+n.status,[z.ERR_BAD_REQUEST,z.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):t(n)}vt.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),U.reduceDescriptors(vt.prototype,(({value:t},e)=>{let n=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[n]=t}}})),U.freezeMethods(vt),U.inherits(wt,z,{__CANCEL__:!0});const Nt=(t,e,n=3)=>{let r=0;const i=function(t,e){t=t||10;const n=new Array(t),r=new Array(t);let i,o=0,a=0;return e=void 0!==e?e:1e3,function(s){const l=Date.now(),u=r[a];i||(i=l),n[o]=s,r[o]=l;let c=a,h=0;for(;c!==o;)h+=n[c++],c%=t;if(o=(o+1)%t,o===a&&(a=(a+1)%t),l-i<e)return;const f=u&&l-u;return f?Math.round(1e3*h/f):void 0}}(50,250);return function(t,e){let n,r,i=0,o=1e3/e;const a=(e,o=Date.now())=>{i=o,n=null,r&&(clearTimeout(r),r=null),t.apply(null,e)};return[(...t)=>{const e=Date.now(),s=e-i;s>=o?a(t,e):(n=t,r||(r=setTimeout((()=>{r=null,a(n)}),o-s)))},()=>n&&a(n)]}((n=>{const o=n.loaded,a=n.lengthComputable?n.total:void 0,s=o-r,l=i(s);r=o;t({loaded:o,total:a,progress:a?o/a:void 0,bytes:s,rate:l||void 0,estimated:l&&a&&o<=a?(a-o)/l:void 0,event:n,lengthComputable:null!=a,[e?"download":"upload"]:!0})}),n)},Lt=(t,e)=>{const n=null!=t;return[r=>e[0]({lengthComputable:n,total:t,loaded:r}),e[1]]},At=t=>(...e)=>U.asap((()=>t(...e))),St=ut.hasStandardBrowserEnv?((t,e)=>n=>(n=new URL(n,ut.origin),t.protocol===n.protocol&&t.host===n.host&&(e||t.port===n.port)))(new URL(ut.origin),ut.navigator&&/(msie|trident)/i.test(ut.navigator.userAgent)):()=>!0,_t=ut.hasStandardBrowserEnv?{write(t,e,n,r,i,o){const a=[t+"="+encodeURIComponent(e)];U.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),U.isString(r)&&a.push("path="+r),U.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Pt(t,e,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(r||0==n)?function(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}(t,e):e}const kt=t=>t instanceof vt?{...t}:t;function Ft(t,e){e=e||{};const n={};function r(t,e,n,r){return U.isPlainObject(t)&&U.isPlainObject(e)?U.merge.call({caseless:r},t,e):U.isPlainObject(e)?U.merge({},e):U.isArray(e)?e.slice():e}function i(t,e,n,i){return U.isUndefined(e)?U.isUndefined(t)?void 0:r(void 0,t,0,i):r(t,e,0,i)}function o(t,e){if(!U.isUndefined(e))return r(void 0,e)}function a(t,e){return U.isUndefined(e)?U.isUndefined(t)?void 0:r(void 0,t):r(void 0,e)}function s(n,i,o){return o in e?r(n,i):o in t?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(t,e,n)=>i(kt(t),kt(e),0,!0)};return U.forEach(Object.keys(Object.assign({},t,e)),(function(r){const o=l[r]||i,a=o(t[r],e[r],r);U.isUndefined(a)&&o!==s||(n[r]=a)})),n}const Ct=t=>{const e=Ft({},t);let n,{data:r,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:a,headers:s,auth:l}=e;if(e.headers=s=vt.from(s),e.url=tt(Pt(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),U.isFormData(r))if(ut.hasStandardBrowserEnv||ut.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[t,...e]=n?n.split(";").map((t=>t.trim())).filter(Boolean):[];s.setContentType([t||"multipart/form-data",...e].join("; "))}if(ut.hasStandardBrowserEnv&&(i&&U.isFunction(i)&&(i=i(e)),i||!1!==i&&St(e.url))){const t=o&&a&&_t.read(a);t&&s.set(o,t)}return e},jt="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise((function(e,n){const r=Ct(t);let i=r.data;const o=vt.from(r.headers).normalize();let a,s,l,u,c,{responseType:h,onUploadProgress:f,onDownloadProgress:d}=r;function p(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(a),r.signal&&r.signal.removeEventListener("abort",a)}let g=new XMLHttpRequest;function m(){if(!g)return;const r=vt.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());xt((function(t){e(t),p()}),(function(t){n(t),p()}),{data:h&&"text"!==h&&"json"!==h?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:r,config:t,request:g}),g=null}g.open(r.method.toUpperCase(),r.url,!0),g.timeout=r.timeout,"onloadend"in g?g.onloadend=m:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(m)},g.onabort=function(){g&&(n(new z("Request aborted",z.ECONNABORTED,t,g)),g=null)},g.onerror=function(){n(new z("Network Error",z.ERR_NETWORK,t,g)),g=null},g.ontimeout=function(){let e=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||nt;r.timeoutErrorMessage&&(e=r.timeoutErrorMessage),n(new z(e,i.clarifyTimeoutError?z.ETIMEDOUT:z.ECONNABORTED,t,g)),g=null},void 0===i&&o.setContentType(null),"setRequestHeader"in g&&U.forEach(o.toJSON(),(function(t,e){g.setRequestHeader(e,t)})),U.isUndefined(r.withCredentials)||(g.withCredentials=!!r.withCredentials),h&&"json"!==h&&(g.responseType=r.responseType),d&&([l,c]=Nt(d,!0),g.addEventListener("progress",l)),f&&g.upload&&([s,u]=Nt(f),g.upload.addEventListener("progress",s),g.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(a=e=>{g&&(n(!e||e.type?new wt(null,t,g):e),g.abort(),g=null)},r.cancelToken&&r.cancelToken.subscribe(a),r.signal&&(r.signal.aborted?a():r.signal.addEventListener("abort",a)));const v=function(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(r.url);v&&-1===ut.protocols.indexOf(v)?n(new z("Unsupported protocol "+v+":",z.ERR_BAD_REQUEST,t)):g.send(i||null)}))},It=(t,e)=>{const{length:n}=t=t?t.filter(Boolean):[];if(e||n){let n,r=new AbortController;const i=function(t){if(!n){n=!0,a();const e=t instanceof Error?t:this.reason;r.abort(e instanceof z?e:new wt(e instanceof Error?e.message:e))}};let o=e&&setTimeout((()=>{o=null,i(new z(`timeout ${e} of ms exceeded`,z.ETIMEDOUT))}),e);const a=()=>{t&&(o&&clearTimeout(o),o=null,t.forEach((t=>{t.unsubscribe?t.unsubscribe(i):t.removeEventListener("abort",i)})),t=null)};t.forEach((t=>t.addEventListener("abort",i)));const{signal:s}=r;return s.unsubscribe=()=>U.asap(a),s}},Ot=function*(t,e){let n=t.byteLength;if(n<e)return void(yield t);let r,i=0;for(;i<n;)r=i+e,yield t.slice(i,r),i=r},Et=async function*(t){if(t[Symbol.asyncIterator])return void(yield*t);const e=t.getReader();try{for(;;){const{done:t,value:n}=await e.read();if(t)break;yield n}}finally{await e.cancel()}},Rt=(t,e,n,r)=>{const i=async function*(t,e){for await(const n of Et(t))yield*Ot(n,e)}(t,e);let o,a=0,s=t=>{o||(o=!0,r&&r(t))};return new ReadableStream({async pull(t){try{const{done:e,value:r}=await i.next();if(e)return s(),void t.close();let o=r.byteLength;if(n){let t=a+=o;n(t)}t.enqueue(new Uint8Array(r))}catch(e){throw s(e),e}},cancel:t=>(s(t),i.return())},{highWaterMark:2})},Bt="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Dt=Bt&&"function"==typeof ReadableStream,Tt=Bt&&("function"==typeof TextEncoder?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),Mt=(t,...e)=>{try{return!!t(...e)}catch(Tr){return!1}},qt=Dt&&Mt((()=>{let t=!1;const e=new Request(ut.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e})),Ut=Dt&&Mt((()=>U.isReadableStream(new Response("").body))),zt={stream:Ut&&(t=>t.body)};var Wt;Bt&&(Wt=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((t=>{!zt[t]&&(zt[t]=U.isFunction(Wt[t])?e=>e[t]():(e,n)=>{throw new z(`Response type '${t}' is not supported`,z.ERR_NOT_SUPPORT,n)})})));const Ht=async(t,e)=>{const n=U.toFiniteNumber(t.getContentLength());return null==n?(async t=>{if(null==t)return 0;if(U.isBlob(t))return t.size;if(U.isSpecCompliantForm(t)){const e=new Request(ut.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return U.isArrayBufferView(t)||U.isArrayBuffer(t)?t.byteLength:(U.isURLSearchParams(t)&&(t+=""),U.isString(t)?(await Tt(t)).byteLength:void 0)})(e):n},Vt={http:null,xhr:jt,fetch:Bt&&(async t=>{let{url:e,method:n,data:r,signal:i,cancelToken:o,timeout:a,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:h="same-origin",fetchOptions:f}=Ct(t);u=u?(u+"").toLowerCase():"text";let d,p=It([i,o&&o.toAbortSignal()],a);const g=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let m;try{if(l&&qt&&"get"!==n&&"head"!==n&&0!==(m=await Ht(c,r))){let t,n=new Request(e,{method:"POST",body:r,duplex:"half"});if(U.isFormData(r)&&(t=n.headers.get("content-type"))&&c.setContentType(t),n.body){const[t,e]=Lt(m,Nt(At(l)));r=Rt(n.body,65536,t,e)}}U.isString(h)||(h=h?"include":"omit");const i="credentials"in Request.prototype;d=new Request(e,{...f,signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:i?h:void 0});let o=await fetch(d);const a=Ut&&("stream"===u||"response"===u);if(Ut&&(s||a&&g)){const t={};["status","statusText","headers"].forEach((e=>{t[e]=o[e]}));const e=U.toFiniteNumber(o.headers.get("content-length")),[n,r]=s&&Lt(e,Nt(At(s),!0))||[];o=new Response(Rt(o.body,65536,n,(()=>{r&&r(),g&&g()})),t)}u=u||"text";let v=await zt[U.findKey(zt,u)||"text"](o,t);return!a&&g&&g(),await new Promise(((e,n)=>{xt(e,n,{data:v,headers:vt.from(o.headers),status:o.status,statusText:o.statusText,config:t,request:d})}))}catch(v){if(g&&g(),v&&"TypeError"===v.name&&/Load failed|fetch/i.test(v.message))throw Object.assign(new z("Network Error",z.ERR_NETWORK,t,d),{cause:v.cause||v});throw z.from(v,v&&v.code,t,d)}})};U.forEach(Vt,((t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(Tr){}Object.defineProperty(t,"adapterName",{value:e})}}));const Gt=t=>`- ${t}`,Jt=t=>U.isFunction(t)||null===t||!1===t,Yt=t=>{t=U.isArray(t)?t:[t];const{length:e}=t;let n,r;const i={};for(let o=0;o<e;o++){let e;if(n=t[o],r=n,!Jt(n)&&(r=Vt[(e=String(n)).toLowerCase()],void 0===r))throw new z(`Unknown adapter '${e}'`);if(r)break;i[e||"#"+o]=r}if(!r){const t=Object.entries(i).map((([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build")));throw new z("There is no suitable adapter to dispatch the request "+(e?t.length>1?"since :\n"+t.map(Gt).join("\n"):" "+Gt(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function Kt(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new wt(null,t)}function Xt(t){Kt(t),t.headers=vt.from(t.headers),t.data=bt.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);return Yt(t.adapter||ht.adapter)(t).then((function(e){return Kt(t),e.data=bt.call(t,t.transformResponse,e),e.headers=vt.from(e.headers),e}),(function(e){return yt(e)||(Kt(t),e&&e.response&&(e.response.data=bt.call(t,t.transformResponse,e.response),e.response.headers=vt.from(e.response.headers))),Promise.reject(e)}))}const $t="1.9.0",Zt={};["object","boolean","number","function","string","symbol"].forEach(((t,e)=>{Zt[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}}));const Qt={};Zt.transitional=function(t,e,n){return(r,i,o)=>{if(!1===t)throw new z(function(t,e){return"[Axios v1.9.0] Transitional option '"+t+"'"+e+(n?". "+n:"")}(i," has been removed"+(e?" in "+e:"")),z.ERR_DEPRECATED);return e&&!Qt[i]&&(Qt[i]=!0),!t||t(r,i,o)}},Zt.spelling=function(t){return(t,e)=>!0};const te={assertOptions:function(t,e,n){if("object"!=typeof t)throw new z("options must be an object",z.ERR_BAD_OPTION_VALUE);const r=Object.keys(t);let i=r.length;for(;i-- >0;){const o=r[i],a=e[o];if(a){const e=t[o],n=void 0===e||a(e,o,t);if(!0!==n)throw new z("option "+o+" must be "+n,z.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new z("Unknown option "+o,z.ERR_BAD_OPTION)}},validators:Zt},ee=te.validators;let ne=class{constructor(t){this.defaults=t||{},this.interceptors={request:new et,response:new et}}async request(t,e){try{return await this._request(t,e)}catch(n){if(n instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const e=t.stack?t.stack.replace(/^.+\n/,""):"";try{n.stack?e&&!String(n.stack).endsWith(e.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+e):n.stack=e}catch(Tr){}}throw n}}_request(t,e){"string"==typeof t?(e=e||{}).url=t:e=t||{},e=Ft(this.defaults,e);const{transitional:n,paramsSerializer:r,headers:i}=e;void 0!==n&&te.assertOptions(n,{silentJSONParsing:ee.transitional(ee.boolean),forcedJSONParsing:ee.transitional(ee.boolean),clarifyTimeoutError:ee.transitional(ee.boolean)},!1),null!=r&&(U.isFunction(r)?e.paramsSerializer={serialize:r}:te.assertOptions(r,{encode:ee.function,serialize:ee.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),te.assertOptions(e,{baseUrl:ee.spelling("baseURL"),withXsrfToken:ee.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let o=i&&U.merge(i.common,i[e.method]);i&&U.forEach(["delete","get","head","post","put","patch","common"],(t=>{delete i[t]})),e.headers=vt.concat(o,i);const a=[];let s=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(s=s&&t.synchronous,a.unshift(t.fulfilled,t.rejected))}));const l=[];let u;this.interceptors.response.forEach((function(t){l.push(t.fulfilled,t.rejected)}));let c,h=0;if(!s){const t=[Xt.bind(this),void 0];for(t.unshift.apply(t,a),t.push.apply(t,l),c=t.length,u=Promise.resolve(e);h<c;)u=u.then(t[h++],t[h++]);return u}c=a.length;let f=e;for(h=0;h<c;){const t=a[h++],e=a[h++];try{f=t(f)}catch(d){e.call(this,d);break}}try{u=Xt.call(this,f)}catch(d){return Promise.reject(d)}for(h=0,c=l.length;h<c;)u=u.then(l[h++],l[h++]);return u}getUri(t){return tt(Pt((t=Ft(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}};U.forEach(["delete","get","head","options"],(function(t){ne.prototype[t]=function(e,n){return this.request(Ft(n||{},{method:t,url:e,data:(n||{}).data}))}})),U.forEach(["post","put","patch"],(function(t){function e(e){return function(n,r,i){return this.request(Ft(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}ne.prototype[t]=e(),ne.prototype[t+"Form"]=e(!0)}));const re={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(re).forEach((([t,e])=>{re[e]=t}));const ie=function e(n){const r=new ne(n),i=t(ne.prototype.request,r);return U.extend(i,ne.prototype,r,{allOwnKeys:!0}),U.extend(i,r,null,{allOwnKeys:!0}),i.create=function(t){return e(Ft(n,t))},i}(ht);ie.Axios=ne,ie.CanceledError=wt,ie.CancelToken=class t{constructor(t){if("function"!=typeof t)throw new TypeError("executor must be a function.");let e;this.promise=new Promise((function(t){e=t}));const n=this;this.promise.then((t=>{if(!n._listeners)return;let e=n._listeners.length;for(;e-- >0;)n._listeners[e](t);n._listeners=null})),this.promise.then=t=>{let e;const r=new Promise((t=>{n.subscribe(t),e=t})).then(t);return r.cancel=function(){n.unsubscribe(e)},r},t((function(t,r,i){n.reason||(n.reason=new wt(t,r,i),e(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){this.reason?t(this.reason):this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){const t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let e;return{token:new t((function(t){e=t})),cancel:e}}},ie.isCancel=yt,ie.VERSION=$t,ie.toFormData=K,ie.AxiosError=z,ie.Cancel=ie.CanceledError,ie.all=function(t){return Promise.all(t)},ie.spread=function(t){return function(e){return t.apply(null,e)}},ie.isAxiosError=function(t){return U.isObject(t)&&!0===t.isAxiosError},ie.mergeConfig=Ft,ie.AxiosHeaders=vt,ie.formToJSON=t=>ct(U.isHTMLForm(t)?new FormData(t):t),ie.getAdapter=Yt,ie.HttpStatusCode=re,ie.default=ie;const{Axios:oe,AxiosError:ae,CanceledError:se,isCancel:le,CancelToken:ue,VERSION:ce,all:he,Cancel:fe,isAxiosError:de,spread:pe,toFormData:ge,AxiosHeaders:me,HttpStatusCode:ve,formToJSON:be,getAdapter:ye,mergeConfig:we}=ie,xe={},Ne=function(t,e,n){let r=Promise.resolve();if(e&&e.length>0){let t=function(t){return Promise.all(t.map((t=>Promise.resolve(t).then((t=>({status:"fulfilled",value:t})),(t=>({status:"rejected",reason:t}))))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));r=t(e.map((t=>{if((t=function(t){return"/"+t}(t))in xe)return;xe[t]=!0;const e=t.endsWith(".css"),n=e?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${t}"]${n}`))return;const r=document.createElement("link");return r.rel=e?"stylesheet":"modulepreload",e||(r.as="script"),r.crossOrigin="",r.href=t,i&&r.setAttribute("nonce",i),document.head.appendChild(r),e?new Promise(((e,n)=>{r.addEventListener("load",e),r.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0})))}function i(t){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=t,window.dispatchEvent(e),!e.defaultPrevented)throw t}return r.then((e=>{for(const t of e||[])"rejected"===t.status&&i(t.reason);return t().catch(i)}))};function Le(t){return(Le="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Ae=Uint8Array,Se=Uint16Array,_e=Int32Array,Pe=new Ae([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ke=new Ae([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Fe=new Ae([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Ce=function(t,e){for(var n=new Se(31),r=0;r<31;++r)n[r]=e+=1<<t[r-1];var i=new _e(n[30]);for(r=1;r<30;++r)for(var o=n[r];o<n[r+1];++o)i[o]=o-n[r]<<5|r;return{b:n,r:i}},je=Ce(Pe,2),Ie=je.b,Oe=je.r;Ie[28]=258,Oe[258]=28;for(var Ee=Ce(ke,0),Re=Ee.b,Be=Ee.r,De=new Se(32768),Te=0;Te<32768;++Te){var Me=(43690&Te)>>1|(21845&Te)<<1;Me=(61680&(Me=(52428&Me)>>2|(13107&Me)<<2))>>4|(3855&Me)<<4,De[Te]=((65280&Me)>>8|(255&Me)<<8)>>1}var qe=function(t,e,n){for(var r=t.length,i=0,o=new Se(e);i<r;++i)t[i]&&++o[t[i]-1];var a,s=new Se(e);for(i=1;i<e;++i)s[i]=s[i-1]+o[i-1]<<1;if(n){a=new Se(1<<e);var l=15-e;for(i=0;i<r;++i)if(t[i])for(var u=i<<4|t[i],c=e-t[i],h=s[t[i]-1]++<<c,f=h|(1<<c)-1;h<=f;++h)a[De[h]>>l]=u}else for(a=new Se(r),i=0;i<r;++i)t[i]&&(a[i]=De[s[t[i]-1]++]>>15-t[i]);return a},Ue=new Ae(288);for(Te=0;Te<144;++Te)Ue[Te]=8;for(Te=144;Te<256;++Te)Ue[Te]=9;for(Te=256;Te<280;++Te)Ue[Te]=7;for(Te=280;Te<288;++Te)Ue[Te]=8;var ze=new Ae(32);for(Te=0;Te<32;++Te)ze[Te]=5;var We=qe(Ue,9,0),He=qe(Ue,9,1),Ve=qe(ze,5,0),Ge=qe(ze,5,1),Je=function(t){for(var e=t[0],n=1;n<t.length;++n)t[n]>e&&(e=t[n]);return e},Ye=function(t,e,n){var r=e/8|0;return(t[r]|t[r+1]<<8)>>(7&e)&n},Ke=function(t,e){var n=e/8|0;return(t[n]|t[n+1]<<8|t[n+2]<<16)>>(7&e)},Xe=function(t){return(t+7)/8|0},$e=function(t,e,n){return(null==n||n>t.length)&&(n=t.length),new Ae(t.subarray(e,n))},Ze=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Qe=function(t,e,n){var r=new Error(e||Ze[t]);if(r.code=t,Error.captureStackTrace&&Error.captureStackTrace(r,Qe),!n)throw r;return r},tn=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8},en=function(t,e,n){n<<=7&e;var r=e/8|0;t[r]|=n,t[r+1]|=n>>8,t[r+2]|=n>>16},nn=function(t,e){for(var n=[],r=0;r<t.length;++r)t[r]&&n.push({s:r,f:t[r]});var i=n.length,o=n.slice();if(!i)return{t:cn,l:0};if(1==i){var a=new Ae(n[0].s+1);return a[n[0].s]=1,{t:a,l:1}}n.sort((function(t,e){return t.f-e.f})),n.push({s:-1,f:25001});var s=n[0],l=n[1],u=0,c=1,h=2;for(n[0]={s:-1,f:s.f+l.f,l:s,r:l};c!=i-1;)s=n[n[u].f<n[h].f?u++:h++],l=n[u!=c&&n[u].f<n[h].f?u++:h++],n[c++]={s:-1,f:s.f+l.f,l:s,r:l};var f=o[0].s;for(r=1;r<i;++r)o[r].s>f&&(f=o[r].s);var d=new Se(f+1),p=rn(n[c-1],d,0);if(p>e){r=0;var g=0,m=p-e,v=1<<m;for(o.sort((function(t,e){return d[e.s]-d[t.s]||t.f-e.f}));r<i;++r){var b=o[r].s;if(!(d[b]>e))break;g+=v-(1<<p-d[b]),d[b]=e}for(g>>=m;g>0;){var y=o[r].s;d[y]<e?g-=1<<e-d[y]++-1:++r}for(;r>=0&&g;--r){var w=o[r].s;d[w]==e&&(--d[w],++g)}p=e}return{t:new Ae(d),l:p}},rn=function(t,e,n){return-1==t.s?Math.max(rn(t.l,e,n+1),rn(t.r,e,n+1)):e[t.s]=n},on=function(t){for(var e=t.length;e&&!t[--e];);for(var n=new Se(++e),r=0,i=t[0],o=1,a=function(t){n[r++]=t},s=1;s<=e;++s)if(t[s]==i&&s!=e)++o;else{if(!i&&o>2){for(;o>138;o-=138)a(32754);o>2&&(a(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(a(i),--o;o>6;o-=6)a(8304);o>2&&(a(o-3<<5|8208),o=0)}for(;o--;)a(i);o=1,i=t[s]}return{c:n.subarray(0,r),n:e}},an=function(t,e){for(var n=0,r=0;r<e.length;++r)n+=t[r]*e[r];return n},sn=function(t,e,n){var r=n.length,i=Xe(e+2);t[i]=255&r,t[i+1]=r>>8,t[i+2]=255^t[i],t[i+3]=255^t[i+1];for(var o=0;o<r;++o)t[i+o+4]=n[o];return 8*(i+4+r)},ln=function(t,e,n,r,i,o,a,s,l,u,c){tn(e,c++,n),++i[256];for(var h=nn(i,15),f=h.t,d=h.l,p=nn(o,15),g=p.t,m=p.l,v=on(f),b=v.c,y=v.n,w=on(g),x=w.c,N=w.n,L=new Se(19),A=0;A<b.length;++A)++L[31&b[A]];for(A=0;A<x.length;++A)++L[31&x[A]];for(var S=nn(L,7),_=S.t,P=S.l,k=19;k>4&&!_[Fe[k-1]];--k);var F,C,j,I,O=u+5<<3,E=an(i,Ue)+an(o,ze)+a,R=an(i,f)+an(o,g)+a+14+3*k+an(L,_)+2*L[16]+3*L[17]+7*L[18];if(l>=0&&O<=E&&O<=R)return sn(e,c,t.subarray(l,l+u));if(tn(e,c,1+(R<E)),c+=2,R<E){F=qe(f,d,0),C=f,j=qe(g,m,0),I=g;var B=qe(_,P,0);tn(e,c,y-257),tn(e,c+5,N-1),tn(e,c+10,k-4),c+=14;for(A=0;A<k;++A)tn(e,c+3*A,_[Fe[A]]);c+=3*k;for(var D=[b,x],T=0;T<2;++T){var M=D[T];for(A=0;A<M.length;++A){var q=31&M[A];tn(e,c,B[q]),c+=_[q],q>15&&(tn(e,c,M[A]>>5&127),c+=M[A]>>12)}}}else F=We,C=Ue,j=Ve,I=ze;for(A=0;A<s;++A){var U=r[A];if(U>255){en(e,c,F[(q=U>>18&31)+257]),c+=C[q+257],q>7&&(tn(e,c,U>>23&31),c+=Pe[q]);var z=31&U;en(e,c,j[z]),c+=I[z],z>3&&(en(e,c,U>>5&8191),c+=ke[z])}else en(e,c,F[U]),c+=C[U]}return en(e,c,F[256]),c+C[256]},un=new _e([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),cn=new Ae(0),hn=function(){var t=1,e=0;return{p:function(n){for(var r=t,i=e,o=0|n.length,a=0;a!=o;){for(var s=Math.min(a+2655,o);a<s;++a)i+=r+=n[a];r=(65535&r)+15*(r>>16),i=(65535&i)+15*(i>>16)}t=r,e=i},d:function(){return(255&(t%=65521))<<24|(65280&t)<<8|(255&(e%=65521))<<8|e>>8}}},fn=function(t,e,n,r,i){if(!i&&(i={l:1},e.dictionary)){var o=e.dictionary.subarray(-32768),a=new Ae(o.length+t.length);a.set(o),a.set(t,o.length),t=a,i.w=o.length}return function(t,e,n,r,i,o){var a=o.z||t.length,s=new Ae(r+a+5*(1+Math.ceil(a/7e3))+i),l=s.subarray(r,s.length-i),u=o.l,c=7&(o.r||0);if(e){c&&(l[0]=o.r>>3);for(var h=un[e-1],f=h>>13,d=8191&h,p=(1<<n)-1,g=o.p||new Se(32768),m=o.h||new Se(p+1),v=Math.ceil(n/3),b=2*v,y=function(e){return(t[e]^t[e+1]<<v^t[e+2]<<b)&p},w=new _e(25e3),x=new Se(288),N=new Se(32),L=0,A=0,S=o.i||0,_=0,P=o.w||0,k=0;S+2<a;++S){var F=y(S),C=32767&S,j=m[F];if(g[C]=j,m[F]=C,P<=S){var I=a-S;if((L>7e3||_>24576)&&(I>423||!u)){c=ln(t,l,0,w,x,N,A,_,k,S-k,c),_=L=A=0,k=S;for(var O=0;O<286;++O)x[O]=0;for(O=0;O<30;++O)N[O]=0}var E=2,R=0,B=d,D=C-j&32767;if(I>2&&F==y(S-D))for(var T=Math.min(f,I)-1,M=Math.min(32767,S),q=Math.min(258,I);D<=M&&--B&&C!=j;){if(t[S+E]==t[S+E-D]){for(var U=0;U<q&&t[S+U]==t[S+U-D];++U);if(U>E){if(E=U,R=D,U>T)break;var z=Math.min(D,U-2),W=0;for(O=0;O<z;++O){var H=S-D+O&32767,V=H-g[H]&32767;V>W&&(W=V,j=H)}}}D+=(C=j)-(j=g[C])&32767}if(R){w[_++]=268435456|Oe[E]<<18|Be[R];var G=31&Oe[E],J=31&Be[R];A+=Pe[G]+ke[J],++x[257+G],++N[J],P=S+E,++L}else w[_++]=t[S],++x[t[S]]}}for(S=Math.max(S,P);S<a;++S)w[_++]=t[S],++x[t[S]];c=ln(t,l,u,w,x,N,A,_,k,S-k,c),u||(o.r=7&c|l[c/8|0]<<3,c-=7,o.h=m,o.p=g,o.i=S,o.w=P)}else{for(S=o.w||0;S<a+u;S+=65535){var Y=S+65535;Y>=a&&(l[c/8|0]=u,Y=a),c=sn(l,c+1,t.subarray(S,Y))}o.i=a}return $e(s,0,r+Xe(c)+i)}(t,null==e.level?6:e.level,null==e.mem?i.l?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(t.length)))):20:12+e.mem,n,r,i)},dn=function(t,e,n){for(;n;++e)t[e]=n,n>>>=8};function pn(t,e){e||(e={});var n=hn();n.p(t);var r=fn(t,e,e.dictionary?6:2,4);return function(t,e){var n=e.level,r=0==n?0:n<6?1:9==n?3:2;if(t[0]=120,t[1]=r<<6|(e.dictionary&&32),t[1]|=31-(t[0]<<8|t[1])%31,e.dictionary){var i=hn();i.p(e.dictionary),dn(t,2,i.d())}}(r,e),dn(r,r.length-4,n.d()),r}function gn(t,e){return function(t,e,n,r){var i=t.length;if(!i||e.f&&!e.l)return n||new Ae(0);var o=!n,a=o||2!=e.i,s=e.i;o&&(n=new Ae(3*i));var l=function(t){var e=n.length;if(t>e){var r=new Ae(Math.max(2*e,t));r.set(n),n=r}},u=e.f||0,c=e.p||0,h=e.b||0,f=e.l,d=e.d,p=e.m,g=e.n,m=8*i;do{if(!f){u=Ye(t,c,1);var v=Ye(t,c+1,3);if(c+=3,!v){var b=t[(F=Xe(c)+4)-4]|t[F-3]<<8,y=F+b;if(y>i){s&&Qe(0);break}a&&l(h+b),n.set(t.subarray(F,y),h),e.b=h+=b,e.p=c=8*y,e.f=u;continue}if(1==v)f=He,d=Ge,p=9,g=5;else if(2==v){var w=Ye(t,c,31)+257,x=Ye(t,c+10,15)+4,N=w+Ye(t,c+5,31)+1;c+=14;for(var L=new Ae(N),A=new Ae(19),S=0;S<x;++S)A[Fe[S]]=Ye(t,c+3*S,7);c+=3*x;var _=Je(A),P=(1<<_)-1,k=qe(A,_,1);for(S=0;S<N;){var F,C=k[Ye(t,c,P)];if(c+=15&C,(F=C>>4)<16)L[S++]=F;else{var j=0,I=0;for(16==F?(I=3+Ye(t,c,3),c+=2,j=L[S-1]):17==F?(I=3+Ye(t,c,7),c+=3):18==F&&(I=11+Ye(t,c,127),c+=7);I--;)L[S++]=j}}var O=L.subarray(0,w),E=L.subarray(w);p=Je(O),g=Je(E),f=qe(O,p,1),d=qe(E,g,1)}else Qe(1);if(c>m){s&&Qe(0);break}}a&&l(h+131072);for(var R=(1<<p)-1,B=(1<<g)-1,D=c;;D=c){var T=(j=f[Ke(t,c)&R])>>4;if((c+=15&j)>m){s&&Qe(0);break}if(j||Qe(2),T<256)n[h++]=T;else{if(256==T){D=c,f=null;break}var M=T-254;if(T>264){var q=Pe[S=T-257];M=Ye(t,c,(1<<q)-1)+Ie[S],c+=q}var U=d[Ke(t,c)&B],z=U>>4;if(U||Qe(3),c+=15&U,E=Re[z],z>3&&(q=ke[z],E+=Ke(t,c)&(1<<q)-1,c+=q),c>m){s&&Qe(0);break}a&&l(h+131072);var W=h+M;if(h<E){var H=0-E,V=Math.min(E,W);for(H+h<0&&Qe(3);h<V;++h)n[h]=r[H+h]}for(;h<W;++h)n[h]=n[h-E]}}e.l=f,e.p=D,e.b=h,e.f=u,f&&(u=1,e.m=p,e.d=d,e.n=g)}while(!u);return h!=n.length&&o?$e(n,0,h):n.subarray(0,h)}(t.subarray(((8!=(15&(n=t)[0])||n[0]>>4>7||(n[0]<<8|n[1])%31)&&Qe(6,"invalid zlib data"),1==(n[1]>>5&1)&&Qe(6,"invalid zlib data: "+(32&n[1]?"need":"unexpected")+" dictionary"),2+(n[1]>>3&4)),-4),{i:2},e,e);var n}var mn="undefined"!=typeof TextDecoder&&new TextDecoder;try{mn.decode(cn,{stream:!0})}catch(Tr){}var vn=function(){return"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this}();function bn(){vn.console&&"function"==typeof vn.console.log&&vn.console.log.apply(vn.console,arguments)}var yn={log:bn,warn:function(t){vn.console&&("function"==typeof vn.console.warn?vn.console.warn.apply(vn.console,arguments):bn.call(null,arguments))},error:function(t){vn.console&&("function"==typeof vn.console.error?vn.console.error.apply(vn.console,arguments):bn(t))}};function wn(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){Sn(r.response,e,n)},r.onerror=function(){yn.error("could not download file")},r.send()}function xn(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(n){}return e.status>=200&&e.status<=299}function Nn(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(n){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var Ln,An,Sn=vn.saveAs||("object"!==("undefined"==typeof window?"undefined":Le(window))||window!==vn?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(t,e,n){var r=vn.URL||vn.webkitURL,i=document.createElement("a");e=e||t.name||"download",i.download=e,i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?xn(i.href)?wn(t,e,n):Nn(i,i.target="_blank"):Nn(i)):(i.href=r.createObjectURL(t),setTimeout((function(){r.revokeObjectURL(i.href)}),4e4),setTimeout((function(){Nn(i)}),0))}:"msSaveOrOpenBlob"in navigator?function(t,e,n){if(e=e||t.name||"download","string"==typeof t)if(xn(t))wn(t,e,n);else{var r=document.createElement("a");r.href=t,r.target="_blank",setTimeout((function(){Nn(r)}))}else navigator.msSaveOrOpenBlob((i=t,void 0===(o=n)?o={autoBom:!1}:"object"!==Le(o)&&(yn.warn("Deprecated: Expected third argument to be a object"),o={autoBom:!o}),o.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(i.type)?new Blob([String.fromCharCode(65279),i],{type:i.type}):i),e);var i,o}:function(t,e,n,r){if((r=r||open("","_blank"))&&(r.document.title=r.document.body.innerText="downloading..."),"string"==typeof t)return wn(t,e,n);var i="application/octet-stream"===t.type,o=/constructor/i.test(vn.HTMLElement)||vn.safari,a=/CriOS\/[\d]+/.test(navigator.userAgent);if((a||i&&o)&&"object"===("undefined"==typeof FileReader?"undefined":Le(FileReader))){var s=new FileReader;s.onloadend=function(){var t=s.result;t=a?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=t:location=t,r=null},s.readAsDataURL(t)}else{var l=vn.URL||vn.webkitURL,u=l.createObjectURL(t);r?r.location=u:location.href=u,r=null,setTimeout((function(){l.revokeObjectURL(u)}),4e4)}});
/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function _n(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],r=0;r<n.length;r++){var i=n[r].re,o=n[r].process,a=i.exec(t);a&&(e=o(a),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),n=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==n.length&&(n="0"+n),"#"+t+e+n}}
/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function Pn(t,e){var n=t[0],r=t[1],i=t[2],o=t[3];n=Fn(n,r,i,o,e[0],7,-680876936),o=Fn(o,n,r,i,e[1],12,-389564586),i=Fn(i,o,n,r,e[2],17,606105819),r=Fn(r,i,o,n,e[3],22,-**********),n=Fn(n,r,i,o,e[4],7,-176418897),o=Fn(o,n,r,i,e[5],12,**********),i=Fn(i,o,n,r,e[6],17,-**********),r=Fn(r,i,o,n,e[7],22,-45705983),n=Fn(n,r,i,o,e[8],7,**********),o=Fn(o,n,r,i,e[9],12,-**********),i=Fn(i,o,n,r,e[10],17,-42063),r=Fn(r,i,o,n,e[11],22,-**********),n=Fn(n,r,i,o,e[12],7,**********),o=Fn(o,n,r,i,e[13],12,-40341101),i=Fn(i,o,n,r,e[14],17,-**********),n=Cn(n,r=Fn(r,i,o,n,e[15],22,**********),i,o,e[1],5,-165796510),o=Cn(o,n,r,i,e[6],9,-**********),i=Cn(i,o,n,r,e[11],14,643717713),r=Cn(r,i,o,n,e[0],20,-373897302),n=Cn(n,r,i,o,e[5],5,-701558691),o=Cn(o,n,r,i,e[10],9,38016083),i=Cn(i,o,n,r,e[15],14,-660478335),r=Cn(r,i,o,n,e[4],20,-405537848),n=Cn(n,r,i,o,e[9],5,568446438),o=Cn(o,n,r,i,e[14],9,-1019803690),i=Cn(i,o,n,r,e[3],14,-187363961),r=Cn(r,i,o,n,e[8],20,1163531501),n=Cn(n,r,i,o,e[13],5,-1444681467),o=Cn(o,n,r,i,e[2],9,-51403784),i=Cn(i,o,n,r,e[7],14,1735328473),n=jn(n,r=Cn(r,i,o,n,e[12],20,-1926607734),i,o,e[5],4,-378558),o=jn(o,n,r,i,e[8],11,-2022574463),i=jn(i,o,n,r,e[11],16,1839030562),r=jn(r,i,o,n,e[14],23,-35309556),n=jn(n,r,i,o,e[1],4,-1530992060),o=jn(o,n,r,i,e[4],11,1272893353),i=jn(i,o,n,r,e[7],16,-155497632),r=jn(r,i,o,n,e[10],23,-1094730640),n=jn(n,r,i,o,e[13],4,681279174),o=jn(o,n,r,i,e[0],11,-358537222),i=jn(i,o,n,r,e[3],16,-722521979),r=jn(r,i,o,n,e[6],23,76029189),n=jn(n,r,i,o,e[9],4,-640364487),o=jn(o,n,r,i,e[12],11,-421815835),i=jn(i,o,n,r,e[15],16,530742520),n=In(n,r=jn(r,i,o,n,e[2],23,-995338651),i,o,e[0],6,-198630844),o=In(o,n,r,i,e[7],10,1126891415),i=In(i,o,n,r,e[14],15,-1416354905),r=In(r,i,o,n,e[5],21,-57434055),n=In(n,r,i,o,e[12],6,1700485571),o=In(o,n,r,i,e[3],10,-1894986606),i=In(i,o,n,r,e[10],15,-1051523),r=In(r,i,o,n,e[1],21,-2054922799),n=In(n,r,i,o,e[8],6,1873313359),o=In(o,n,r,i,e[15],10,-30611744),i=In(i,o,n,r,e[6],15,-1560198380),r=In(r,i,o,n,e[13],21,1309151649),n=In(n,r,i,o,e[4],6,-145523070),o=In(o,n,r,i,e[11],10,-1120210379),i=In(i,o,n,r,e[2],15,718787259),r=In(r,i,o,n,e[9],21,-343485551),t[0]=qn(n,t[0]),t[1]=qn(r,t[1]),t[2]=qn(i,t[2]),t[3]=qn(o,t[3])}function kn(t,e,n,r,i,o){return e=qn(qn(e,t),qn(r,o)),qn(e<<i|e>>>32-i,n)}function Fn(t,e,n,r,i,o,a){return kn(e&n|~e&r,t,e,i,o,a)}function Cn(t,e,n,r,i,o,a){return kn(e&r|n&~r,t,e,i,o,a)}function jn(t,e,n,r,i,o,a){return kn(e^n^r,t,e,i,o,a)}function In(t,e,n,r,i,o,a){return kn(n^(e|~r),t,e,i,o,a)}function On(t){var e,n=t.length,r=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=t.length;e+=64)Pn(r,En(t.substring(e-64,e)));t=t.substring(e-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(Pn(r,i),e=0;e<16;e++)i[e]=0;return i[14]=8*n,Pn(r,i),r}function En(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}Ln=vn.atob.bind(vn),An=vn.btoa.bind(vn);var Rn="0123456789abcdef".split("");function Bn(t){for(var e="",n=0;n<4;n++)e+=Rn[t>>8*n+4&15]+Rn[t>>8*n&15];return e}function Dn(t){return String.fromCharCode(255&t,(65280&t)>>8,(16711680&t)>>16,(**********&t)>>24)}function Tn(t){return On(t).map(Dn).join("")}var Mn="5d41402abc4b2a76b9719d911017c592"!=function(t){for(var e=0;e<t.length;e++)t[e]=Bn(t[e]);return t.join("")}(On("hello"));function qn(t,e){if(Mn){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}return t+e&**********}
/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function Un(t,e){var n,r,i,o;if(t!==n){for(var a=(i=t,o=1+(256/t.length|0),new Array(o+1).join(i)),s=[],l=0;l<256;l++)s[l]=l;var u=0;for(l=0;l<256;l++){var c=s[l];u=(u+c+a.charCodeAt(l))%256,s[l]=s[u],s[u]=c}n=t,r=s}else s=r;var h=e.length,f=0,d=0,p="";for(l=0;l<h;l++)d=(d+(c=s[f=(f+1)%256]))%256,s[f]=s[d],s[d]=c,a=s[(s[f]+s[d])%256],p+=String.fromCharCode(e.charCodeAt(l)^a);return p}
/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var zn={print:4,modify:8,copy:16,"annot-forms":32};function Wn(t,e,n,r){this.v=1,this.r=2;var i=192;t.forEach((function(t){if(void 0!==zn.perm)throw new Error("Invalid permission: "+t);i+=zn[t]})),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),a=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,a),this.P=-(1+(255^i)),this.encryptionKey=Tn(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(r)).substr(0,5),this.U=Un(this.encryptionKey,this.padding)}function Hn(t){if(/[^\u0000-\u00ff]/.test(t))throw new Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);e+=i<33||35===i||37===i||40===i||41===i||47===i||60===i||62===i||91===i||93===i||123===i||125===i||i>126?"#"+("0"+i.toString(16)).slice(-2):t[r]}return e}function Vn(t){if("object"!==Le(t))throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(t,n,r){if(r=r||!1,"string"!=typeof t||"function"!=typeof n||"boolean"!=typeof r)throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(t)||(e[t]={});var i=Math.random().toString(35);return e[t][i]=[n,!!r],i},this.unsubscribe=function(t){for(var n in e)if(e[n][t])return delete e[n][t],0===Object.keys(e[n]).length&&delete e[n],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var r=Array.prototype.slice.call(arguments,1),i=[];for(var o in e[n]){var a=e[n][o];try{a[0].apply(t,r)}catch(s){vn.console&&yn.error("jsPDF PubSub Error",s.message,s)}a[1]&&i.push(o)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function Gn(t){if(!(this instanceof Gn))return new Gn(t);var e="opacity,stroke-opacity".split(",");for(var n in t)t.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=t[n]);this.id="",this.objectNumber=-1}function Jn(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function Yn(t,e,n,r,i){if(!(this instanceof Yn))return new Yn(t,e,n,r,i);this.type="axial"===t?2:3,this.coords=e,this.colors=n,Jn.call(this,r,i)}function Kn(t,e,n,r,i){if(!(this instanceof Kn))return new Kn(t,e,n,r,i);this.boundingBox=t,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,Jn.call(this,r,i)}function Xn(t){var e,n="string"==typeof arguments[0]?arguments[0]:"p",r=arguments[1],i=arguments[2],o=arguments[3],a=[],s=1,l=16,u="S",c=null;"object"===Le(t=t||{})&&(n=t.orientation,r=t.unit||r,i=t.format||i,o=t.compress||t.compressPdf||o,null!==(c=t.encryption||null)&&(c.userPassword=c.userPassword||"",c.ownerPassword=c.ownerPassword||"",c.userPermissions=c.userPermissions||[]),s="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(e=t.precision),void 0!==t.floatPrecision&&(l=t.floatPrecision),u=t.defaultPathOperation||"S"),a=t.filters||(!0===o?["FlateEncode"]:a),r=r||"mm",n=(""+(n||"P")).toLowerCase();var h=t.putOnlyUsedFonts||!1,f={},d={internal:{},__private__:{}};d.__private__.PubSub=Vn;var p="1.3",g=d.__private__.getPdfVersion=function(){return p};d.__private__.setPdfVersion=function(t){p=t};var m={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};d.__private__.getPageFormats=function(){return m};var v=d.__private__.getPageFormat=function(t){return m[t]};i=i||"a4";var b="compat",y="advanced",w=b;function x(){this.saveGraphicsState(),$(new jt(ft,0,0,-ft,0,hn()*ft).toString()+" cm"),this.setFontSize(this.getFontSize()/ft),u="n",w=y}function N(){this.restoreGraphicsState(),u="S",w=b}var L=d.__private__.combineFontStyleAndFontWeight=function(t,e){if("bold"==t&&"normal"==e||"bold"==t&&400==e||"normal"==t&&"italic"==e||"bold"==t&&"italic"==e)throw new Error("Invalid Combination of fontweight and fontstyle");return e&&(t=400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"),t};d.advancedAPI=function(t){var e=w===b;return e&&x.call(this),"function"!=typeof t||(t(this),e&&N.call(this)),this},d.compatAPI=function(t){var e=w===y;return e&&N.call(this),"function"!=typeof t||(t(this),e&&x.call(this)),this},d.isAdvancedAPI=function(){return w===y};var A,S=function(t){if(w!==y)throw new Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},_=d.roundToPrecision=d.__private__.roundToPrecision=function(t,n){var r=e||n;if(isNaN(t)||isNaN(r))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(r).replace(/0+$/,"")};A=d.hpf=d.__private__.hpf="number"==typeof l?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return _(t,l)}:"smart"===l?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return _(t,t>-1&&t<1?16:5)}:function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return _(t,16)};var P=d.f2=d.__private__.f2=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return _(t,2)},k=d.__private__.f3=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f3");return _(t,3)},F=d.scale=d.__private__.scale=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.scale");return w===b?t*ft:w===y?t:void 0},C=function(t){return F(function(t){return w===b?hn()-t:w===y?t:void 0}(t))};d.__private__.setPrecision=d.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(e=parseInt(t,10))};var j,I="00000000000000000000000000000000",O=d.__private__.getFileId=function(){return I},E=d.__private__.setFileId=function(t){return I=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():I.split("").map((function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))})).join(""),null!==c&&(ye=new Wn(c.userPermissions,c.userPassword,c.ownerPassword,I)),I};d.setFileId=function(t){return E(t),this},d.getFileId=function(){return O()};var R=d.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),n=e<0?"+":"-",r=Math.floor(Math.abs(e/60)),i=Math.abs(e%60),o=[n,q(r),"'",q(i),"'"].join("");return["D:",t.getFullYear(),q(t.getMonth()+1),q(t.getDate()),q(t.getHours()),q(t.getMinutes()),q(t.getSeconds()),o].join("")},B=d.__private__.convertPDFDateToDate=function(t){var e=parseInt(t.substr(2,4),10),n=parseInt(t.substr(6,2),10)-1,r=parseInt(t.substr(8,2),10),i=parseInt(t.substr(10,2),10),o=parseInt(t.substr(12,2),10),a=parseInt(t.substr(14,2),10);return new Date(e,n,r,i,o,a,0)},D=d.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=R(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw new Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return j=e},T=d.__private__.getCreationDate=function(t){var e=j;return"jsDate"===t&&(e=B(j)),e};d.setCreationDate=function(t){return D(t),this},d.getCreationDate=function(t){return T(t)};var M,q=d.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},U=d.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},z=0,W=[],H=[],V=0,G=[],J=[],Y=!1,K=H;d.__private__.setCustomOutputDestination=function(t){Y=!0,K=t};var X=function(t){Y||(K=t)};d.__private__.resetCustomOutputDestination=function(){Y=!1,K=H};var $=d.__private__.out=function(t){return t=t.toString(),V+=t.length+1,K.push(t),K},Z=d.__private__.write=function(t){return $(1===arguments.length?t.toString():Array.prototype.join.call(arguments," "))},Q=d.__private__.getArrayBuffer=function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n);e--;)r[e]=t.charCodeAt(e);return n},tt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];d.__private__.getStandardFonts=function(){return tt};var et=t.fontSize||16;d.__private__.setFontSize=d.setFontSize=function(t){return et=w===y?t/ft:t,this};var nt,rt=d.__private__.getFontSize=d.getFontSize=function(){return w===b?et:et*ft},it=t.R2L||!1;d.__private__.setR2L=d.setR2L=function(t){return it=t,this},d.__private__.getR2L=d.getR2L=function(){return it};var ot,at=d.__private__.setZoomMode=function(t){if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(t))nt=t;else if(isNaN(t)){if(-1===[void 0,null,"fullwidth","fullheight","fullpage","original"].indexOf(t))throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');nt=t}else nt=parseInt(t,10)};d.__private__.getZoomMode=function(){return nt};var st,lt=d.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');ot=t};d.__private__.getPageMode=function(){return ot};var ut=d.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');st=t};d.__private__.getLayoutMode=function(){return st},d.__private__.setDisplayMode=d.setDisplayMode=function(t,e,n){return at(t),ut(e),lt(n),this};var ct={title:"",subject:"",author:"",keywords:"",creator:""};d.__private__.getDocumentProperty=function(t){if(-1===Object.keys(ct).indexOf(t))throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return ct[t]},d.__private__.getDocumentProperties=function(){return ct},d.__private__.setDocumentProperties=d.setProperties=d.setDocumentProperties=function(t){for(var e in ct)ct.hasOwnProperty(e)&&t[e]&&(ct[e]=t[e]);return this},d.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(ct).indexOf(t))throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return ct[t]=e};var ht,ft,dt,pt,gt,mt={},vt={},bt=[],yt={},wt={},xt={},Nt={},Lt=null,At=0,St=[],_t=new Vn(d),Pt=t.hotfixes||[],kt={},Ft={},Ct=[],jt=function t(e,n,r,i,o,a){if(!(this instanceof t))return new t(e,n,r,i,o,a);isNaN(e)&&(e=1),isNaN(n)&&(n=0),isNaN(r)&&(r=0),isNaN(i)&&(i=1),isNaN(o)&&(o=0),isNaN(a)&&(a=0),this._matrix=[e,n,r,i,o,a]};Object.defineProperty(jt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(jt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(jt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(jt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(jt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(jt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(jt.prototype,"a",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(jt.prototype,"b",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(jt.prototype,"c",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(jt.prototype,"d",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(jt.prototype,"e",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(jt.prototype,"f",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(jt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(jt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(jt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(jt.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),jt.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(A).join(t)},jt.prototype.multiply=function(t){var e=t.sx*this.sx+t.shy*this.shx,n=t.sx*this.shy+t.shy*this.sy,r=t.shx*this.sx+t.sy*this.shx,i=t.shx*this.shy+t.sy*this.sy,o=t.tx*this.sx+t.ty*this.shx+this.tx,a=t.tx*this.shy+t.ty*this.sy+this.ty;return new jt(e,n,r,i,o,a)},jt.prototype.decompose=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty,a=Math.sqrt(t*t+e*e),s=(t/=a)*n+(e/=a)*r;n-=t*s,r-=e*s;var l=Math.sqrt(n*n+r*r);return s/=l,t*(r/=l)<e*(n/=l)&&(t=-t,e=-e,s=-s,a=-a),{scale:new jt(a,0,0,l,0,0),translate:new jt(1,0,0,1,i,o),rotate:new jt(t,e,-e,t,0,0),skew:new jt(1,0,s,1,0,0)}},jt.prototype.toString=function(t){return this.join(" ")},jt.prototype.inversed=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty,a=1/(t*r-e*n),s=r*a,l=-e*a,u=-n*a,c=t*a;return new jt(s,l,u,c,-s*i-u*o,-l*i-c*o)},jt.prototype.applyToPoint=function(t){var e=t.x*this.sx+t.y*this.shx+this.tx,n=t.x*this.shy+t.y*this.sy+this.ty;return new rn(e,n)},jt.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),n=this.applyToPoint(new rn(t.x+t.w,t.y+t.h));return new on(e.x,e.y,n.x-e.x,n.y-e.y)},jt.prototype.clone=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty;return new jt(t,e,n,r,i,o)},d.Matrix=jt;var It=d.matrixMult=function(t,e){return e.multiply(t)},Ot=new jt(1,0,0,1,0,0);d.unitMatrix=d.identityMatrix=Ot;var Et=function(t,e){if(!wt[t]){var n=(e instanceof Yn?"Sh":"P")+(Object.keys(yt).length+1).toString(10);e.id=n,wt[t]=n,yt[n]=e,_t.publish("addPattern",e)}};d.ShadingPattern=Yn,d.TilingPattern=Kn,d.addShadingPattern=function(t,e){return S("addShadingPattern()"),Et(t,e),this},d.beginTilingPattern=function(t){S("beginTilingPattern()"),sn(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},d.endTilingPattern=function(t,e){S("endTilingPattern()"),e.stream=J[M].join("\n"),Et(t,e),_t.publish("endTilingPattern",e),Ct.pop().restore()};var Rt,Bt=d.__private__.newObject=function(){var t=Dt();return Tt(t,!0),t},Dt=d.__private__.newObjectDeferred=function(){return z++,W[z]=function(){return V},z},Tt=function(t,e){return e="boolean"==typeof e&&e,W[t]=V,e&&$(t+" 0 obj"),t},Mt=d.__private__.newAdditionalObject=function(){var t={objId:Dt(),content:""};return G.push(t),t},qt=Dt(),Ut=Dt(),zt=d.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1])5!==e.length||"k"!==e[4]&&"K"!==e[4]||(e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]);else{var n=parseFloat(e[0]);e=[n,n,n,"r"]}for(var r="#",i=0;i<3;i++)r+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return r},Wt=d.__private__.encodeColorString=function(t){var e;"string"==typeof t&&(t={ch1:t});var n=t.ch1,r=t.ch2,i=t.ch3,o=t.ch4,a="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof n&&"#"!==n.charAt(0)){var s=new _n(n);if(s.ok)n=s.toHex();else if(!/^\d*\.?\d*$/.test(n))throw new Error('Invalid color "'+n+'" passed to jsPDF.encodeColorString.')}if("string"==typeof n&&/^#[0-9A-Fa-f]{3}$/.test(n)&&(n="#"+n[1]+n[1]+n[2]+n[2]+n[3]+n[3]),"string"==typeof n&&/^#[0-9A-Fa-f]{6}$/.test(n)){var l=parseInt(n.substr(1),16);n=l>>16&255,r=l>>8&255,i=255&l}if(void 0===r||void 0===o&&n===r&&r===i)if("string"==typeof n)e=n+" "+a[0];else if(2===t.precision)e=P(n/255)+" "+a[0];else e=k(n/255)+" "+a[0];else if(void 0===o||"object"===Le(o)){if(o&&!isNaN(o.a)&&0===o.a)return["1.","1.","1.",a[1]].join(" ");if("string"==typeof n)e=[n,r,i,a[1]].join(" ");else if(2===t.precision)e=[P(n/255),P(r/255),P(i/255),a[1]].join(" ");else e=[k(n/255),k(r/255),k(i/255),a[1]].join(" ")}else if("string"==typeof n)e=[n,r,i,o,a[2]].join(" ");else if(2===t.precision)e=[P(n),P(r),P(i),P(o),a[2]].join(" ");else e=[k(n),k(r),k(i),k(o),a[2]].join(" ");return e},Ht=d.__private__.getFilters=function(){return a},Vt=d.__private__.putStream=function(t){var e=(t=t||{}).data||"",n=t.filters||Ht(),r=t.alreadyAppliedFilters||[],i=t.addLength1||!1,o=e.length,a=t.objectId,s=function(t){return t};if(null!==c&&void 0===a)throw new Error("ObjectId must be passed to putStream for file encryption");null!==c&&(s=ye.encryptor(a,0));var l={};!0===n&&(n=["FlateEncode"]);var u=t.additionalKeyValues||[],h=(l=void 0!==Xn.API.processDataByFilters?Xn.API.processDataByFilters(e,n):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(r)?r.join(" "):r.toString());if(0!==l.data.length&&(u.push({key:"Length",value:l.data.length}),!0===i&&u.push({key:"Length1",value:o})),0!=h.length)if(h.split("/").length-1==1)u.push({key:"Filter",value:h});else{u.push({key:"Filter",value:"["+h+"]"});for(var f=0;f<u.length;f+=1)if("DecodeParms"===u[f].key){for(var d=[],p=0;p<l.reverseChain.split("/").length-1;p+=1)d.push("null");d.push(u[f].value),u[f].value="["+d.join(" ")+"]"}}$("<<");for(var g=0;g<u.length;g++)$("/"+u[g].key+" "+u[g].value);$(">>"),0!==l.data.length&&($("stream"),$(s(l.data)),$("endstream"))},Gt=d.__private__.putPage=function(t){var e=t.number,n=t.data,r=t.objId,i=t.contentsObjId;Tt(r,!0),$("<</Type /Page"),$("/Parent "+t.rootDictionaryObjId+" 0 R"),$("/Resources "+t.resourceDictionaryObjId+" 0 R"),$("/MediaBox ["+parseFloat(A(t.mediaBox.bottomLeftX))+" "+parseFloat(A(t.mediaBox.bottomLeftY))+" "+A(t.mediaBox.topRightX)+" "+A(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&$("/CropBox ["+A(t.cropBox.bottomLeftX)+" "+A(t.cropBox.bottomLeftY)+" "+A(t.cropBox.topRightX)+" "+A(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&$("/BleedBox ["+A(t.bleedBox.bottomLeftX)+" "+A(t.bleedBox.bottomLeftY)+" "+A(t.bleedBox.topRightX)+" "+A(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&$("/TrimBox ["+A(t.trimBox.bottomLeftX)+" "+A(t.trimBox.bottomLeftY)+" "+A(t.trimBox.topRightX)+" "+A(t.trimBox.topRightY)+"]"),null!==t.artBox&&$("/ArtBox ["+A(t.artBox.bottomLeftX)+" "+A(t.artBox.bottomLeftY)+" "+A(t.artBox.topRightX)+" "+A(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&$("/UserUnit "+t.userUnit),_t.publish("putPage",{objId:r,pageContext:St[e],pageNumber:e,page:n}),$("/Contents "+i+" 0 R"),$(">>"),$("endobj");var o=n.join("\n");return w===y&&(o+="\nQ"),Tt(i,!0),Vt({data:o,filters:Ht(),objectId:i}),$("endobj"),r},Jt=d.__private__.putPages=function(){var t,e,n=[];for(t=1;t<=At;t++)St[t].objId=Dt(),St[t].contentsObjId=Dt();for(t=1;t<=At;t++)n.push(Gt({number:t,data:J[t],objId:St[t].objId,contentsObjId:St[t].contentsObjId,mediaBox:St[t].mediaBox,cropBox:St[t].cropBox,bleedBox:St[t].bleedBox,trimBox:St[t].trimBox,artBox:St[t].artBox,userUnit:St[t].userUnit,rootDictionaryObjId:qt,resourceDictionaryObjId:Ut}));Tt(qt,!0),$("<</Type /Pages");var r="/Kids [";for(e=0;e<At;e++)r+=n[e]+" 0 R ";$(r+"]"),$("/Count "+At),$(">>"),$("endobj"),_t.publish("postPutPages")},Yt=function(t){_t.publish("putFont",{font:t,out:$,newObject:Bt,putStream:Vt}),!0!==t.isAlreadyPutted&&(t.objectNumber=Bt(),$("<<"),$("/Type /Font"),$("/BaseFont /"+Hn(t.postScriptName)),$("/Subtype /Type1"),"string"==typeof t.encoding&&$("/Encoding /"+t.encoding),$("/FirstChar 32"),$("/LastChar 255"),$(">>"),$("endobj"))},Kt=function(t){t.objectNumber=Bt();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[A(t.x),A(t.y),A(t.x+t.width),A(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"});var n=t.pages[1].join("\n");Vt({data:n,additionalKeyValues:e,objectId:t.objectNumber}),$("endobj")},Xt=function(t,e){e||(e=21);var n=Bt(),r=function(t,e){var n,r=[],i=1/(e-1);for(n=0;n<1;n+=i)r.push(n);if(r.push(1),0!=t[0].offset){var o={offset:0,color:t[0].color};t.unshift(o)}if(1!=t[t.length-1].offset){var a={offset:1,color:t[t.length-1].color};t.push(a)}for(var s="",l=0,u=0;u<r.length;u++){for(n=r[u];n>t[l+1].offset;)l++;var c=t[l].offset,h=(n-c)/(t[l+1].offset-c),f=t[l].color,d=t[l+1].color;s+=U(Math.round((1-h)*f[0]+h*d[0]).toString(16))+U(Math.round((1-h)*f[1]+h*d[1]).toString(16))+U(Math.round((1-h)*f[2]+h*d[2]).toString(16))}return s.trim()}(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),Vt({data:r,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:n}),$("endobj"),t.objectNumber=Bt(),$("<< /ShadingType "+t.type),$("/ColorSpace /DeviceRGB");var o="/Coords ["+A(parseFloat(t.coords[0]))+" "+A(parseFloat(t.coords[1]))+" ";2===t.type?o+=A(parseFloat(t.coords[2]))+" "+A(parseFloat(t.coords[3])):o+=A(parseFloat(t.coords[2]))+" "+A(parseFloat(t.coords[3]))+" "+A(parseFloat(t.coords[4]))+" "+A(parseFloat(t.coords[5])),$(o+="]"),t.matrix&&$("/Matrix ["+t.matrix.toString()+"]"),$("/Function "+n+" 0 R"),$("/Extend [true true]"),$(">>"),$("endobj")},$t=function(t,e){var n=Dt(),r=Bt();e.push({resourcesOid:n,objectOid:r}),t.objectNumber=r;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(A).join(" ")+"]"}),i.push({key:"XStep",value:A(t.xStep)}),i.push({key:"YStep",value:A(t.yStep)}),i.push({key:"Resources",value:n+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),Vt({data:t.stream,additionalKeyValues:i,objectId:t.objectNumber}),$("endobj")},Zt=function(t){for(var e in t.objectNumber=Bt(),$("<<"),t)switch(e){case"opacity":$("/ca "+P(t[e]));break;case"stroke-opacity":$("/CA "+P(t[e]))}$(">>"),$("endobj")},Qt=function(t){Tt(t.resourcesOid,!0),$("<<"),$("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),function(){for(var t in $("/Font <<"),mt)mt.hasOwnProperty(t)&&(!1===h||!0===h&&f.hasOwnProperty(t))&&$("/"+t+" "+mt[t].objectNumber+" 0 R");$(">>")}(),function(){if(Object.keys(yt).length>0){for(var t in $("/Shading <<"),yt)yt.hasOwnProperty(t)&&yt[t]instanceof Yn&&yt[t].objectNumber>=0&&$("/"+t+" "+yt[t].objectNumber+" 0 R");_t.publish("putShadingPatternDict"),$(">>")}}(),function(t){if(Object.keys(yt).length>0){for(var e in $("/Pattern <<"),yt)yt.hasOwnProperty(e)&&yt[e]instanceof d.TilingPattern&&yt[e].objectNumber>=0&&yt[e].objectNumber<t&&$("/"+e+" "+yt[e].objectNumber+" 0 R");_t.publish("putTilingPatternDict"),$(">>")}}(t.objectOid),function(){if(Object.keys(xt).length>0){var t;for(t in $("/ExtGState <<"),xt)xt.hasOwnProperty(t)&&xt[t].objectNumber>=0&&$("/"+t+" "+xt[t].objectNumber+" 0 R");_t.publish("putGStateDict"),$(">>")}}(),function(){for(var t in $("/XObject <<"),kt)kt.hasOwnProperty(t)&&kt[t].objectNumber>=0&&$("/"+t+" "+kt[t].objectNumber+" 0 R");_t.publish("putXobjectDict"),$(">>")}(),$(">>"),$("endobj")},te=function(){var t=[];(function(){for(var t in mt)mt.hasOwnProperty(t)&&(!1===h||!0===h&&f.hasOwnProperty(t))&&Yt(mt[t])})(),function(){var t;for(t in xt)xt.hasOwnProperty(t)&&Zt(xt[t])}(),function(){for(var t in kt)kt.hasOwnProperty(t)&&Kt(kt[t])}(),function(t){var e;for(e in yt)yt.hasOwnProperty(e)&&(yt[e]instanceof Yn?Xt(yt[e]):yt[e]instanceof Kn&&$t(yt[e],t))}(t),_t.publish("putResources"),t.forEach(Qt),Qt({resourcesOid:Ut,objectOid:Number.MAX_SAFE_INTEGER}),_t.publish("postPutResources")},ee=function(t){vt[t.fontName]=vt[t.fontName]||{},vt[t.fontName][t.fontStyle]=t.id},ne=function(t,e,n,r,i){var o={id:"F"+(Object.keys(mt).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:n,encoding:r,isStandardFont:i||!1,metadata:{}};return _t.publish("addFont",{font:o,instance:this}),mt[o.id]=o,ee(o),o.id},re=d.__private__.pdfEscape=d.pdfEscape=function(t,e){return function(t,e){var n,r,i,o,a,s,l,u,c;if(i=(e=e||{}).sourceEncoding||"Unicode",a=e.outputEncoding,(e.autoencode||a)&&mt[ht].metadata&&mt[ht].metadata[i]&&mt[ht].metadata[i].encoding&&(o=mt[ht].metadata[i].encoding,!a&&mt[ht].encoding&&(a=mt[ht].encoding),!a&&o.codePages&&(a=o.codePages[0]),"string"==typeof a&&(a=o[a]),a)){for(l=!1,s=[],n=0,r=t.length;n<r;n++)(u=a[t.charCodeAt(n)])?s.push(String.fromCharCode(u)):s.push(t[n]),s[n].charCodeAt(0)>>8&&(l=!0);t=s.join("")}for(n=t.length;void 0===l&&0!==n;)t.charCodeAt(n-1)>>8&&(l=!0),n--;if(!l)return t;for(s=e.noBOM?[]:[254,255],n=0,r=t.length;n<r;n++){if((c=(u=t.charCodeAt(n))>>8)>>8)throw new Error("Character at position "+n+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(c),s.push(u-(c<<8))}return String.fromCharCode.apply(void 0,s)}(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},ie=d.__private__.beginPage=function(t){J[++At]=[],St[At]={objId:0,contentsObjId:0,userUnit:Number(s),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},se(At),X(J[M])},oe=function(t,e){var r,o,a;switch(n=e||n,"string"==typeof t&&(r=v(t.toLowerCase()),Array.isArray(r)&&(o=r[0],a=r[1])),Array.isArray(t)&&(o=t[0]*ft,a=t[1]*ft),isNaN(o)&&(o=i[0],a=i[1]),(o>14400||a>14400)&&(yn.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),o=Math.min(14400,o),a=Math.min(14400,a)),i=[o,a],n.substr(0,1)){case"l":a>o&&(i=[a,o]);break;case"p":o>a&&(i=[a,o])}ie(i),qe(Te),$(Ye),0!==tn&&$(tn+" J"),0!==en&&$(en+" j"),_t.publish("addPage",{pageNumber:At})},ae=function(t){t>0&&t<=At&&(J.splice(t,1),St.splice(t,1),At--,M>At&&(M=At),this.setPage(M))},se=function(t){t>0&&t<=At&&(M=t)},le=d.__private__.getNumberOfPages=d.getNumberOfPages=function(){return J.length-1},ue=function(t,e,n){var r,i=void 0;return n=n||{},t=void 0!==t?t:mt[ht].fontName,e=void 0!==e?e:mt[ht].fontStyle,r=t.toLowerCase(),void 0!==vt[r]&&void 0!==vt[r][e]?i=vt[r][e]:void 0!==vt[t]&&void 0!==vt[t][e]?i=vt[t][e]:!1===n.disableWarning&&yn.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||n.noFallback||null==(i=vt.times[e])&&(i=vt.times.normal),i},ce=d.__private__.putInfo=function(){var t=Bt(),e=function(t){return t};for(var n in null!==c&&(e=ye.encryptor(t,0)),$("<<"),$("/Producer ("+re(e("jsPDF "+Xn.version))+")"),ct)ct.hasOwnProperty(n)&&ct[n]&&$("/"+n.substr(0,1).toUpperCase()+n.substr(1)+" ("+re(e(ct[n]))+")");$("/CreationDate ("+re(e(j))+")"),$(">>"),$("endobj")},he=d.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||qt;switch(Bt(),$("<<"),$("/Type /Catalog"),$("/Pages "+e+" 0 R"),nt||(nt="fullwidth"),nt){case"fullwidth":$("/OpenAction [3 0 R /FitH null]");break;case"fullheight":$("/OpenAction [3 0 R /FitV null]");break;case"fullpage":$("/OpenAction [3 0 R /Fit]");break;case"original":$("/OpenAction [3 0 R /XYZ null null 1]");break;default:var n=""+nt;"%"===n.substr(n.length-1)&&(nt=parseInt(nt)/100),"number"==typeof nt&&$("/OpenAction [3 0 R /XYZ null null "+P(nt)+"]")}switch(st||(st="continuous"),st){case"continuous":$("/PageLayout /OneColumn");break;case"single":$("/PageLayout /SinglePage");break;case"two":case"twoleft":$("/PageLayout /TwoColumnLeft");break;case"tworight":$("/PageLayout /TwoColumnRight")}ot&&$("/PageMode /"+ot),_t.publish("putCatalog"),$(">>"),$("endobj")},fe=d.__private__.putTrailer=function(){$("trailer"),$("<<"),$("/Size "+(z+1)),$("/Root "+z+" 0 R"),$("/Info "+(z-1)+" 0 R"),null!==c&&$("/Encrypt "+ye.oid+" 0 R"),$("/ID [ <"+I+"> <"+I+"> ]"),$(">>")},de=d.__private__.putHeader=function(){$("%PDF-"+p),$("%ºß¬à")},pe=d.__private__.putXRef=function(){var t="0000000000";$("xref"),$("0 "+(z+1)),$("0000000000 65535 f ");for(var e=1;e<=z;e++)"function"==typeof W[e]?$((t+W[e]()).slice(-10)+" 00000 n "):void 0!==W[e]?$((t+W[e]).slice(-10)+" 00000 n "):$("0000000000 00000 n ")},ge=d.__private__.buildDocument=function(){z=0,V=0,H=[],W=[],G=[],qt=Dt(),Ut=Dt(),X(H),_t.publish("buildDocument"),de(),Jt(),function(){_t.publish("putAdditionalObjects");for(var t=0;t<G.length;t++){var e=G[t];Tt(e.objId,!0),$(e.content),$("endobj")}_t.publish("postPutAdditionalObjects")}(),te(),null!==c&&(ye.oid=Bt(),$("<<"),$("/Filter /Standard"),$("/V "+ye.v),$("/R "+ye.r),$("/U <"+ye.toHexString(ye.U)+">"),$("/O <"+ye.toHexString(ye.O)+">"),$("/P "+ye.P),$(">>"),$("endobj")),ce(),he();var t=V;return pe(),fe(),$("startxref"),$(""+t),$("%%EOF"),X(J[M]),H.join("\n")},me=d.__private__.getBlob=function(t){return new Blob([Q(t)],{type:"application/pdf"})},ve=d.output=d.__private__.output=((Rt=function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return ge();case"save":d.save(e.filename);break;case"arraybuffer":return Q(ge());case"blob":return me(ge());case"bloburi":case"bloburl":if(void 0!==vn.URL&&"function"==typeof vn.URL.createObjectURL)return vn.URL&&vn.URL.createObjectURL(me(ge()))||void 0;yn.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var n="",r=ge();try{n=An(r)}catch(p){n=An(unescape(encodeURIComponent(r)))}return"data:application/pdf;filename="+e.filename+";base64,"+n;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(vn)){var i="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",o=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';e.pdfObjectUrl&&(i=e.pdfObjectUrl,o="");var a='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+i+'"'+o+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",s=vn.open();return null!==s&&s.document.write(a),s}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(vn)){var l='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+e.filename+'" width="500px" height="400px" /></body></html>',u=vn.open();if(null!==u){u.document.write(l);var c=this;u.document.documentElement.querySelector("#pdfViewer").onload=function(){u.document.title=e.filename,u.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(c.output("bloburl"))}}return u}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(vn))throw new Error("The option dataurlnewwindow just works in a browser-environment.");var h='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',f=vn.open();if(null!==f&&(f.document.write(h),f.document.title=e.filename),f||"undefined"==typeof safari)return f;break;case"datauri":case"dataurl":return vn.document.location.href=this.output("datauristring",e);default:return null}}).foo=function(){try{return Rt.apply(this,arguments)}catch(n){var t=n.stack||"";~t.indexOf(" at ")&&(t=t.split(" at ")[1]);var e="Error in function "+t.split("\n")[0].split("<")[0]+": "+n.message;if(!vn.console)throw new Error(e);vn.console.error(e,n),vn.alert&&alert(e)}},Rt.foo.bar=Rt,Rt.foo),be=function(t){return!0===Array.isArray(Pt)&&Pt.indexOf(t)>-1};switch(r){case"pt":ft=1;break;case"mm":ft=72/25.4;break;case"cm":ft=72/2.54;break;case"in":ft=72;break;case"px":ft=1==be("px_scaling")?.75:96/72;break;case"pc":case"em":ft=12;break;case"ex":ft=6;break;default:if("number"!=typeof r)throw new Error("Invalid unit: "+r);ft=r}var ye=null;D(),E();var we=d.__private__.getPageInfo=d.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:St[t].objId,pageNumber:t,pageContext:St[t]}},xe=d.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in St)if(St[e].objId===t)break;return we(e)},Ne=d.__private__.getCurrentPageInfo=d.getCurrentPageInfo=function(){return{objId:St[M].objId,pageNumber:M,pageContext:St[M]}};d.addPage=function(){return oe.apply(this,arguments),this},d.setPage=function(){return se.apply(this,arguments),X.call(this,J[M]),this},d.insertPage=function(t){return this.addPage(),this.movePage(M,t),this},d.movePage=function(t,e){var n,r;if(t>e){n=J[t],r=St[t];for(var i=t;i>e;i--)J[i]=J[i-1],St[i]=St[i-1];J[e]=n,St[e]=r,this.setPage(e)}else if(t<e){n=J[t],r=St[t];for(var o=t;o<e;o++)J[o]=J[o+1],St[o]=St[o+1];J[e]=n,St[e]=r,this.setPage(e)}return this},d.deletePage=function(){return ae.apply(this,arguments),this},d.__private__.text=d.text=function(t,e,n,r,i){var o,a,s,l,u,c,h,d,p,g=(r=r||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof n||Array.isArray(n))){var m=n;n=e,e=t,t=m}if(arguments[3]instanceof jt==0?(s=arguments[4],l=arguments[5],"object"===Le(h=arguments[3])&&null!==h||("string"==typeof s&&(l=s,s=null),"string"==typeof h&&(l=h,h=null),"number"==typeof h&&(s=h,h=null),r={flags:h,angle:s,align:l})):(S("The transform parameter of text() with a Matrix value"),p=i),isNaN(e)||isNaN(n)||null==t)throw new Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return g;var v,b="",x="number"==typeof r.lineHeightFactor?r.lineHeightFactor:De,N=g.internal.scaleFactor;function L(t){return t=t.split("\t").join(Array(r.TabLen||9).join(" ")),re(t,h)}function _(t){for(var e,n=t.concat(),r=[],i=n.length;i--;)"string"==typeof(e=n.shift())?r.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?r.push(e[0]):r.push([e[0],e[1],e[2]]);return r}function P(t,e){var n;if("string"==typeof t)n=e(t)[0];else if(Array.isArray(t)){for(var r,i,o=t.concat(),a=[],s=o.length;s--;)"string"==typeof(r=o.shift())?a.push(e(r)[0]):Array.isArray(r)&&"string"==typeof r[0]&&(i=e(r[0],r[1],r[2]),a.push([i[0],i[1],i[2]]));n=a}return n}var k=!1,C=!0;if("string"==typeof t)k=!0;else if(Array.isArray(t)){var j=t.concat();a=[];for(var I,O=j.length;O--;)("string"!=typeof(I=j.shift())||Array.isArray(I)&&"string"!=typeof I[0])&&(C=!1);k=C}if(!1===k)throw new Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var E=et/g.internal.scaleFactor,R=E*(x-1);switch(r.baseline){case"bottom":n-=R;break;case"top":n+=E-R;break;case"hanging":n+=E-2*R;break;case"middle":n+=E/2-R}if((c=r.maxWidth||0)>0&&("string"==typeof t?t=g.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce((function(t,e){return t.concat(g.splitTextToSize(e,c))}),[]))),o={text:t,x:e,y:n,options:r,mutex:{pdfEscape:re,activeFontKey:ht,fonts:mt,activeFontSize:et}},_t.publish("preProcessText",o),t=o.text,s=(r=o.options).angle,p instanceof jt==0&&s&&"number"==typeof s){s*=Math.PI/180,0===r.rotationDirection&&(s=-s),w===y&&(s=-s);var B=Math.cos(s),D=Math.sin(s);p=new jt(B,D,-D,B,0,0)}else s&&s instanceof jt&&(p=s);w!==y||p||(p=Ot),void 0!==(u=r.charSpace||Ze)&&(b+=A(F(u))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(d=r.horizontalScale)&&(b+=A(100*d)+" Tz\n"),r.lang;var T=-1,M=void 0!==r.renderingMode?r.renderingMode:r.stroke,q=g.internal.getCurrentPageInfo().pageContext;switch(M){case 0:case!1:case"fill":T=0;break;case 1:case!0:case"stroke":T=1;break;case 2:case"fillThenStroke":T=2;break;case 3:case"invisible":T=3;break;case 4:case"fillAndAddForClipping":T=4;break;case 5:case"strokeAndAddPathForClipping":T=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":T=6;break;case 7:case"addToPathForClipping":T=7}var U=void 0!==q.usedRenderingMode?q.usedRenderingMode:-1;-1!==T?b+=T+" Tr\n":-1!==U&&(b+="0 Tr\n"),-1!==T&&(q.usedRenderingMode=T),l=r.align||"left";var z,W=et*x,H=g.internal.pageSize.getWidth(),V=mt[ht];u=r.charSpace||Ze,c=r.maxWidth||0,h=Object.assign({autoencode:!0,noBOM:!0},r.flags);var G=[],J=function(t){return g.getStringUnitWidth(t,{font:V,charSpace:u,fontSize:et,doKerning:!1})*et/N};if("[object Array]"===Object.prototype.toString.call(t)){var Y;a=_(t),"left"!==l&&(z=a.map(J));var K,X=0;if("right"===l){e-=z[0],t=[],O=a.length;for(var Z=0;Z<O;Z++)0===Z?(K=He(e),Y=Ve(n)):(K=F(X-z[Z]),Y=-W),t.push([a[Z],K,Y]),X=z[Z]}else if("center"===l){e-=z[0]/2,t=[],O=a.length;for(var Q=0;Q<O;Q++)0===Q?(K=He(e),Y=Ve(n)):(K=F((X-z[Q])/2),Y=-W),t.push([a[Q],K,Y]),X=z[Q]}else if("left"===l){t=[],O=a.length;for(var tt=0;tt<O;tt++)t.push(a[tt])}else if("justify"===l&&"Identity-H"===V.encoding){t=[],O=a.length,c=0!==c?c:H;for(var nt=0,rt=0;rt<O;rt++)if(Y=0===rt?Ve(n):-W,K=0===rt?He(e):nt,rt<O-1){var ot=F((c-z[rt])/(a[rt].split(" ").length-1)),at=a[rt].split(" ");t.push([at[0]+" ",K,Y]),nt=0;for(var st=1;st<at.length;st++){var lt=(J(at[st-1]+" "+at[st])-J(at[st]))*N+ot;st==at.length-1?t.push([at[st],lt,0]):t.push([at[st]+" ",lt,0]),nt-=lt}}else t.push([a[rt],K,Y]);t.push(["",nt,0])}else{if("justify"!==l)throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(t=[],O=a.length,c=0!==c?c:H,rt=0;rt<O;rt++)Y=0===rt?Ve(n):-W,K=0===rt?He(e):0,rt<O-1?G.push(A(F((c-z[rt])/(a[rt].split(" ").length-1)))):G.push(0),t.push([a[rt],K,Y])}}!0===("boolean"==typeof r.R2L?r.R2L:it)&&(t=P(t,(function(t,e,n){return[t.split("").reverse().join(""),e,n]}))),o={text:t,x:e,y:n,options:r,mutex:{pdfEscape:re,activeFontKey:ht,fonts:mt,activeFontSize:et}},_t.publish("postProcessText",o),t=o.text,v=o.mutex.isHex||!1;var ut=mt[ht].encoding;"WinAnsiEncoding"!==ut&&"StandardEncoding"!==ut||(t=P(t,(function(t,e,n){return[L(t),e,n]}))),a=_(t),t=[];for(var ct,ft,dt,pt=Array.isArray(a[0])?1:0,gt="",vt=function(t,e,n){var i="";return n instanceof jt?(n="number"==typeof r.angle?It(n,new jt(1,0,0,1,t,e)):It(new jt(1,0,0,1,t,e),n),w===y&&(n=It(new jt(1,0,0,-1,0,0),n)),i=n.join(" ")+" Tm\n"):i=A(t)+" "+A(e)+" Td\n",i},bt=0;bt<a.length;bt++){switch(gt="",pt){case 1:dt=(v?"<":"(")+a[bt][0]+(v?">":")"),ct=parseFloat(a[bt][1]),ft=parseFloat(a[bt][2]);break;case 0:dt=(v?"<":"(")+a[bt]+(v?">":")"),ct=He(e),ft=Ve(n)}void 0!==G&&void 0!==G[bt]&&(gt=G[bt]+" Tw\n"),0===bt?t.push(gt+vt(ct,ft,p)+dt):0===pt?t.push(gt+dt):1===pt&&t.push(gt+vt(ct,ft,p)+dt)}t=0===pt?t.join(" Tj\nT* "):t.join(" Tj\n"),t+=" Tj\n";var yt="BT\n/";return yt+=ht+" "+et+" Tf\n",yt+=A(et*x)+" TL\n",yt+=Xe+"\n",yt+=b,yt+=t,$(yt+="ET"),f[ht]=!0,g};var Ae=d.__private__.clip=d.clip=function(t){return $("evenodd"===t?"W*":"W"),this};d.clipEvenOdd=function(){return Ae("evenodd")},d.__private__.discardPath=d.discardPath=function(){return $("n"),this};var Se=d.__private__.isValidStyle=function(t){var e=!1;return-1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};d.__private__.setDefaultPathOperation=d.setDefaultPathOperation=function(t){return Se(t)&&(u=t),this};var _e=d.__private__.getStyle=d.getStyle=function(t){var e=u;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},Pe=d.close=function(){return $("h"),this};d.stroke=function(){return $("S"),this},d.fill=function(t){return ke("f",t),this},d.fillEvenOdd=function(t){return ke("f*",t),this},d.fillStroke=function(t){return ke("B",t),this},d.fillStrokeEvenOdd=function(t){return ke("B*",t),this};var ke=function(t,e){"object"===Le(e)?je(e,t):$(t)},Fe=function(t){null===t||w===y&&void 0===t||(t=_e(t),$(t))};function Ce(t,e,n,r,i){var o=new Kn(e||this.boundingBox,n||this.xStep,r||this.yStep,this.gState,i||this.matrix);o.stream=this.stream;var a=t+"$$"+this.cloneIndex+++"$$";return Et(a,o),o}var je=function(t,e){var n=wt[t.key],r=yt[n];if(r instanceof Yn)$("q"),$(Ie(e)),r.gState&&d.setGState(r.gState),$(t.matrix.toString()+" cm"),$("/"+n+" sh"),$("Q");else if(r instanceof Kn){var i=new jt(1,0,0,-1,0,hn());t.matrix&&(i=i.multiply(t.matrix||Ot),n=Ce.call(r,t.key,t.boundingBox,t.xStep,t.yStep,i).id),$("q"),$("/Pattern cs"),$("/"+n+" scn"),r.gState&&d.setGState(r.gState),$(e),$("Q")}},Ie=function(t){switch(t){case"f":case"F":case"n":return"W n";case"f*":return"W* n";case"B":case"S":return"W S";case"B*":return"W* S"}},Oe=d.moveTo=function(t,e){return $(A(F(t))+" "+A(C(e))+" m"),this},Ee=d.lineTo=function(t,e){return $(A(F(t))+" "+A(C(e))+" l"),this},Re=d.curveTo=function(t,e,n,r,i,o){return $([A(F(t)),A(C(e)),A(F(n)),A(C(r)),A(F(i)),A(C(o)),"c"].join(" ")),this};d.__private__.line=d.line=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Se(i))throw new Error("Invalid arguments passed to jsPDF.line");return w===b?this.lines([[n-t,r-e]],t,e,[1,1],i||"S"):this.lines([[n-t,r-e]],t,e,[1,1]).stroke()},d.__private__.lines=d.lines=function(t,e,n,r,i,o){var a,s,l,u,c,h,f,d,p,g,m,v;if("number"==typeof t&&(v=n,n=e,e=t,t=v),r=r||[1,1],o=o||!1,isNaN(e)||isNaN(n)||!Array.isArray(t)||!Array.isArray(r)||!Se(i)||"boolean"!=typeof o)throw new Error("Invalid arguments passed to jsPDF.lines");for(Oe(e,n),a=r[0],s=r[1],u=t.length,g=e,m=n,l=0;l<u;l++)2===(c=t[l]).length?(g=c[0]*a+g,m=c[1]*s+m,Ee(g,m)):(h=c[0]*a+g,f=c[1]*s+m,d=c[2]*a+g,p=c[3]*s+m,g=c[4]*a+g,m=c[5]*s+m,Re(h,f,d,p,g,m));return o&&Pe(),Fe(i),this},d.path=function(t){for(var e=0;e<t.length;e++){var n=t[e],r=n.c;switch(n.op){case"m":Oe(r[0],r[1]);break;case"l":Ee(r[0],r[1]);break;case"c":Re.apply(this,r);break;case"h":Pe()}}return this},d.__private__.rect=d.rect=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Se(i))throw new Error("Invalid arguments passed to jsPDF.rect");return w===b&&(r=-r),$([A(F(t)),A(C(e)),A(F(n)),A(F(r)),"re"].join(" ")),Fe(i),this},d.__private__.triangle=d.triangle=function(t,e,n,r,i,o,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o)||!Se(a))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[n-t,r-e],[i-n,o-r],[t-i,e-o]],t,e,[1,1],a,!0),this},d.__private__.roundedRect=d.roundedRect=function(t,e,n,r,i,o,a){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o)||!Se(a))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var s=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*n),o=Math.min(o,.5*r),this.lines([[n-2*i,0],[i*s,0,i,o-o*s,i,o],[0,r-2*o],[0,o*s,-i*s,o,-i,o],[2*i-n,0],[-i*s,0,-i,-o*s,-i,-o],[0,2*o-r],[0,-o*s,i*s,-o,i,-o]],t+i,e,[1,1],a,!0),this},d.__private__.ellipse=d.ellipse=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Se(i))throw new Error("Invalid arguments passed to jsPDF.ellipse");var o=4/3*(Math.SQRT2-1)*n,a=4/3*(Math.SQRT2-1)*r;return Oe(t+n,e),Re(t+n,e-a,t+o,e-r,t,e-r),Re(t-o,e-r,t-n,e-a,t-n,e),Re(t-n,e+a,t-o,e+r,t,e+r),Re(t+o,e+r,t+n,e+a,t+n,e),Fe(i),this},d.__private__.circle=d.circle=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||!Se(r))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,n,n,r)},d.setFont=function(t,e,n){return n&&(e=L(e,n)),ht=ue(t,e,{disableWarning:!1}),this};var Be=d.__private__.getFont=d.getFont=function(){return mt[ue.apply(d,arguments)]};d.__private__.getFontList=d.getFontList=function(){var t,e,n={};for(t in vt)if(vt.hasOwnProperty(t))for(e in n[t]=[],vt[t])vt[t].hasOwnProperty(e)&&n[t].push(e);return n},d.addFont=function(t,e,n,r,i){var o=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==o.indexOf(arguments[3])?i=arguments[3]:arguments[3]&&-1==o.indexOf(arguments[3])&&(n=L(n,r)),ne.call(this,t,e,n,i=i||"Identity-H")};var De,Te=t.lineWidth||.200025,Me=d.__private__.getLineWidth=d.getLineWidth=function(){return Te},qe=d.__private__.setLineWidth=d.setLineWidth=function(t){return Te=t,$(A(F(t))+" w"),this};d.__private__.setLineDash=Xn.API.setLineDash=Xn.API.setLineDashPattern=function(t,e){if(t=t||[],e=e||0,isNaN(e)||!Array.isArray(t))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return t=t.map((function(t){return A(F(t))})).join(" "),e=A(F(e)),$("["+t+"] "+e+" d"),this};var Ue=d.__private__.getLineHeight=d.getLineHeight=function(){return et*De};d.__private__.getLineHeight=d.getLineHeight=function(){return et*De};var ze=d.__private__.setLineHeightFactor=d.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(De=t),this},We=d.__private__.getLineHeightFactor=d.getLineHeightFactor=function(){return De};ze(t.lineHeight);var He=d.__private__.getHorizontalCoordinate=function(t){return F(t)},Ve=d.__private__.getVerticalCoordinate=function(t){return w===y?t:St[M].mediaBox.topRightY-St[M].mediaBox.bottomLeftY-F(t)},Ge=d.__private__.getHorizontalCoordinateString=d.getHorizontalCoordinateString=function(t){return A(He(t))},Je=d.__private__.getVerticalCoordinateString=d.getVerticalCoordinateString=function(t){return A(Ve(t))},Ye=t.strokeColor||"0 G";d.__private__.getStrokeColor=d.getDrawColor=function(){return zt(Ye)},d.__private__.setStrokeColor=d.setDrawColor=function(t,e,n,r){return Ye=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"draw",precision:2}),$(Ye),this};var Ke=t.fillColor||"0 g";d.__private__.getFillColor=d.getFillColor=function(){return zt(Ke)},d.__private__.setFillColor=d.setFillColor=function(t,e,n,r){return Ke=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"fill",precision:2}),$(Ke),this};var Xe=t.textColor||"0 g",$e=d.__private__.getTextColor=d.getTextColor=function(){return zt(Xe)};d.__private__.setTextColor=d.setTextColor=function(t,e,n,r){return Xe=Wt({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"text",precision:3}),this};var Ze=t.charSpace,Qe=d.__private__.getCharSpace=d.getCharSpace=function(){return parseFloat(Ze||0)};d.__private__.setCharSpace=d.setCharSpace=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Ze=t,this};var tn=0;d.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},d.__private__.setLineCap=d.setLineCap=function(t){var e=d.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return tn=e,$(e+" J"),this};var en=0;d.__private__.setLineJoin=d.setLineJoin=function(t){var e=d.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return en=e,$(e+" j"),this},d.__private__.setLineMiterLimit=d.__private__.setMiterLimit=d.setLineMiterLimit=d.setMiterLimit=function(t){if(t=t||0,isNaN(t))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return $(A(F(t))+" M"),this},d.GState=Gn,d.setGState=function(t){(t="string"==typeof t?xt[Nt[t]]:nn(null,t)).equals(Lt)||($("/"+t.id+" gs"),Lt=t)};var nn=function(t,e){if(!t||!Nt[t]){var n=!1;for(var r in xt)if(xt.hasOwnProperty(r)&&xt[r].equals(e)){n=!0;break}if(n)e=xt[r];else{var i="GS"+(Object.keys(xt).length+1).toString(10);xt[i]=e,e.id=i}return t&&(Nt[t]=e.id),_t.publish("addGState",e),e}};d.addGState=function(t,e){return nn(t,e),this},d.saveGraphicsState=function(){return $("q"),bt.push({key:ht,size:et,color:Xe}),this},d.restoreGraphicsState=function(){$("Q");var t=bt.pop();return ht=t.key,et=t.size,Xe=t.color,Lt=null,this},d.setCurrentTransformationMatrix=function(t){return $(t.toString()+" cm"),this},d.comment=function(t){return $("#"+t),this};var rn=function(t,e){var n=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var r=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},on=function(t,e,n,r){rn.call(this,t,e),this.type="rect";var i=n||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var o=r||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return o},set:function(t){isNaN(t)||(o=parseFloat(t))}}),this},an=function(){this.page=At,this.currentPage=M,this.pages=J.slice(0),this.pagesContext=St.slice(0),this.x=dt,this.y=pt,this.matrix=gt,this.width=un(M),this.height=hn(M),this.outputDestination=K,this.id="",this.objectNumber=-1};an.prototype.restore=function(){At=this.page,M=this.currentPage,St=this.pagesContext,J=this.pages,dt=this.x,pt=this.y,gt=this.matrix,cn(M,this.width),fn(M,this.height),K=this.outputDestination};var sn=function(t,e,n,r,i){Ct.push(new an),At=M=0,J=[],dt=t,pt=e,gt=i,ie([n,r])};for(var ln in d.beginFormObject=function(t,e,n,r,i){return sn(t,e,n,r,i),this},d.endFormObject=function(t){return function(t){if(Ft[t])Ct.pop().restore();else{var e=new an,n="Xo"+(Object.keys(kt).length+1).toString(10);e.id=n,Ft[t]=n,kt[n]=e,_t.publish("addFormObject",e),Ct.pop().restore()}}(t),this},d.doFormObject=function(t,e){var n=kt[Ft[t]];return $("q"),$(e.toString()+" cm"),$("/"+n.id+" Do"),$("Q"),this},d.getFormObject=function(t){var e=kt[Ft[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},d.save=function(t,e){return t=t||"generated.pdf",(e=e||{}).returnPromise=e.returnPromise||!1,!1===e.returnPromise?(Sn(me(ge()),t),"function"==typeof Sn.unload&&vn.setTimeout&&setTimeout(Sn.unload,911),this):new Promise((function(e,n){try{var r=Sn(me(ge()),t);"function"==typeof Sn.unload&&vn.setTimeout&&setTimeout(Sn.unload,911),e(r)}catch(i){n(i.message)}}))},Xn.API)Xn.API.hasOwnProperty(ln)&&("events"===ln&&Xn.API.events.length?function(t,e){var n,r,i;for(i=e.length-1;-1!==i;i--)n=e[i][0],r=e[i][1],t.subscribe.apply(t,[n].concat("function"==typeof r?[r]:r))}(_t,Xn.API.events):d[ln]=Xn.API[ln]);var un=d.getPageWidth=function(t){return(St[t=t||M].mediaBox.topRightX-St[t].mediaBox.bottomLeftX)/ft},cn=d.setPageWidth=function(t,e){St[t].mediaBox.topRightX=e*ft+St[t].mediaBox.bottomLeftX},hn=d.getPageHeight=function(t){return(St[t=t||M].mediaBox.topRightY-St[t].mediaBox.bottomLeftY)/ft},fn=d.setPageHeight=function(t,e){St[t].mediaBox.topRightY=e*ft+St[t].mediaBox.bottomLeftY};return d.internal={pdfEscape:re,getStyle:_e,getFont:Be,getFontSize:rt,getCharSpace:Qe,getTextColor:$e,getLineHeight:Ue,getLineHeightFactor:We,getLineWidth:Me,write:Z,getHorizontalCoordinate:He,getVerticalCoordinate:Ve,getCoordinateString:Ge,getVerticalCoordinateString:Je,collections:{},newObject:Bt,newAdditionalObject:Mt,newObjectDeferred:Dt,newObjectDeferredBegin:Tt,getFilters:Ht,putStream:Vt,events:_t,scaleFactor:ft,pageSize:{getWidth:function(){return un(M)},setWidth:function(t){cn(M,t)},getHeight:function(){return hn(M)},setHeight:function(t){fn(M,t)}},encryptionOptions:c,encryption:ye,getEncryptor:function(t){return null!==c?ye.encryptor(t,0):function(t){return t}},output:ve,getNumberOfPages:le,pages:J,out:$,f2:P,f3:k,getPageInfo:we,getPageInfoByObjId:xe,getCurrentPageInfo:Ne,getPDFVersion:g,Point:rn,Rectangle:on,Matrix:jt,hasHotfix:be},Object.defineProperty(d.internal.pageSize,"width",{get:function(){return un(M)},set:function(t){cn(M,t)},enumerable:!0,configurable:!0}),Object.defineProperty(d.internal.pageSize,"height",{get:function(){return hn(M)},set:function(t){fn(M,t)},enumerable:!0,configurable:!0}),function(t){for(var e=0,n=tt.length;e<n;e++){var r=ne.call(this,t[e][0],t[e][1],t[e][2],tt[e][3],!0);!1===h&&(f[r]=!0);var i=t[e][0].split("-");ee({id:r,fontName:i[0],fontStyle:i[1]||""})}_t.publish("addFonts",{fonts:mt,dictionary:vt})}.call(d,tt),ht="F1",oe(i,n),_t.publish("initialized"),d}Wn.prototype.lsbFirstWord=function(t){return String.fromCharCode(255&t,t>>8&255,t>>16&255,t>>24&255)},Wn.prototype.toHexString=function(t){return t.split("").map((function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)})).join("")},Wn.prototype.hexToBytes=function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(String.fromCharCode(parseInt(t.substr(n,2),16)));return e.join("")},Wn.prototype.processOwnerPassword=function(t,e){return Un(Tn(e).substr(0,5),t)},Wn.prototype.encryptor=function(t,e){var n=Tn(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(t){return Un(n,t)}},Gn.prototype.equals=function(t){var e,n="id,objectNumber,equals";if(!t||Le(t)!==Le(this))return!1;var r=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e))return!1;if(this[e]!==t[e])return!1;r++}for(e in t)t.hasOwnProperty(e)&&n.indexOf(e)<0&&r--;return 0===r},Xn.API={events:[]},Xn.version="3.0.1";var $n=Xn.API,Zn=1,Qn=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},tr=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},er=function(t){return t.toFixed(2)},nr=function(t){return t.toFixed(5)};$n.__acroform__={};var rr=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},ir=function(t){return t*Zn},or=function(t){var e=new Nr,n=Br.internal.getHeight(t)||0,r=Br.internal.getWidth(t)||0;return e.BBox=[0,0,Number(er(r)),Number(er(n))],e},ar=$n.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|1<<e},sr=$n.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&~(1<<e)},lr=$n.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return t&1<<e?1:0},ur=$n.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return lr(t,e-1)},cr=$n.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return ar(t,e-1)},hr=$n.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return sr(t,e-1)},fr=$n.__acroform__.calculateCoordinates=function(t,e){var n=e.internal.getHorizontalCoordinate,r=e.internal.getVerticalCoordinate,i=t[0],o=t[1],a=t[2],s=t[3],l={};return l.lowerLeft_X=n(i)||0,l.lowerLeft_Y=r(o+s)||0,l.upperRight_X=n(i+a)||0,l.upperRight_Y=r(o)||0,[Number(er(l.lowerLeft_X)),Number(er(l.lowerLeft_Y)),Number(er(l.upperRight_X)),Number(er(l.upperRight_Y))]},dr=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],n=t._V||t.DV,r=pr(t,n),i=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+i+" "+er(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=or(t);return o.scope=t.scope,o.stream=e.join("\n"),o}},pr=function(t,e){var n=0===t.fontSize?t.maxFontSize:t.fontSize,r={text:"",fontSize:""},i=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" ");i=t.multiline?i.map((function(t){return t.split("\n")})):i.map((function(t){return[t]}));var o=n,a=Br.internal.getHeight(t)||0;a=a<0?-a:a;var s=Br.internal.getWidth(t)||0;s=s<0?-s:s;var l=function(e,n,r){if(e+1<i.length){var o=n+" "+i[e+1][0];return gr(o,t,r).width<=s-4}return!1};o++;t:for(;o>0;){e="",o--;var u,c,h=gr("3",t,o).height,f=t.multiline?a-o:(a-h)/2,d=f+=2,p=0,g=0,m=0;if(o<=0){e="(...) Tj\n",e+="% Width of Text: "+gr(e,t,o=12).width+", FieldWidth:"+s+"\n";break}for(var v="",b=0,y=0;y<i.length;y++)if(i.hasOwnProperty(y)){var w=!1;if(1!==i[y].length&&m!==i[y].length-1){if((h+2)*(b+2)+2>a)continue t;v+=i[y][m],w=!0,g=y,y--}else{v=" "==(v+=i[y][m]+" ").substr(v.length-1)?v.substr(0,v.length-1):v;var x=parseInt(y),N=l(x,v,o),L=y>=i.length-1;if(N&&!L){v+=" ",m=0;continue}if(N||L){if(L)g=x;else if(t.multiline&&(h+2)*(b+2)+2>a)continue t}else{if(!t.multiline)continue t;if((h+2)*(b+2)+2>a)continue t;g=x}}for(var A="",S=p;S<=g;S++){var _=i[S];if(t.multiline){if(S===g){A+=_[m]+" ",m=(m+1)%_.length;continue}if(S===p){A+=_[_.length-1]+" ";continue}}A+=_[0]+" "}switch(A=" "==A.substr(A.length-1)?A.substr(0,A.length-1):A,c=gr(A,t,o).width,t.textAlign){case"right":u=s-c-2;break;case"center":u=(s-c)/2;break;default:u=2}e+=er(u)+" "+er(d)+" Td\n",e+="("+Qn(A)+") Tj\n",e+=-er(u)+" 0 Td\n",d=-(o+2),c=0,p=w?g:g+1,b++,v=""}break}return r.text=e,r.fontSize=o,r},gr=function(t,e,n){var r=e.scope.internal.getFont(e.fontName,e.fontStyle),i=e.scope.getStringUnitWidth(t,{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:i}},mr={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},vr=function(t,e){var n={type:"reference",object:t};void 0===e.internal.getPageInfo(t.page).pageContext.annotations.find((function(t){return t.type===n.type&&t.object===n.object}))&&e.internal.getPageInfo(t.page).pageContext.annotations.push(n)},br=function(t,e){if(e.scope=t,void 0!==t.internal&&(void 0===t.internal.acroformPlugin||!1===t.internal.acroformPlugin.isInitialized)){if(Ar.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(mr)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Zn=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new Lr,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",(function(){var e;(e=t).internal.events.unsubscribe(e.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete e.internal.acroformPlugin.acroFormDictionaryRoot._eventID,e.internal.acroformPlugin.printedOut=!0})),t.internal.events.subscribe("buildDocument",(function(){!function(t){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var e=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];r.objId=void 0,r.hasAnnotation&&vr(r,t)}}(t)})),t.internal.events.subscribe("putCatalog",(function(){!function(t){if(void 0===t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}(t)})),t.internal.events.subscribe("postPutPages",(function(e){!function(t,e){var n=!t;for(var r in t||(e.internal.newObjectDeferredBegin(e.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),e.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||e.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(r)){var i=t[r],o=[],a=i.Rect;if(i.Rect&&(i.Rect=fr(i.Rect,e)),e.internal.newObjectDeferredBegin(i.objId,!0),i.DA=Br.createDefaultAppearanceStream(i),"object"===Le(i)&&"function"==typeof i.getKeyValueListForStream&&(o=i.getKeyValueListForStream()),i.Rect=a,i.hasAppearanceStream&&!i.appearanceStreamContent){var s=dr(i);o.push({key:"AP",value:"<</N "+s+">>"}),e.internal.acroformPlugin.xForms.push(s)}if(i.appearanceStreamContent){var l="";for(var u in i.appearanceStreamContent)if(i.appearanceStreamContent.hasOwnProperty(u)){var c=i.appearanceStreamContent[u];if(l+="/"+u+" ",l+="<<",Object.keys(c).length>=1||Array.isArray(c)){for(var r in c)if(c.hasOwnProperty(r)){var h=c[r];"function"==typeof h&&(h=h.call(e,i)),l+="/"+r+" "+h+" ",e.internal.acroformPlugin.xForms.indexOf(h)>=0||e.internal.acroformPlugin.xForms.push(h)}}else"function"==typeof(h=c)&&(h=h.call(e,i)),l+="/"+r+" "+h,e.internal.acroformPlugin.xForms.indexOf(h)>=0||e.internal.acroformPlugin.xForms.push(h);l+=">>"}o.push({key:"AP",value:"<<\n"+l+">>"})}e.internal.putStream({additionalKeyValues:o,objectId:i.objId}),e.internal.out("endobj")}n&&function(t,e){for(var n in t)if(t.hasOwnProperty(n)){var r=n,i=t[n];e.internal.newObjectDeferredBegin(i.objId,!0),"object"===Le(i)&&"function"==typeof i.putStream&&i.putStream(),delete t[r]}}(e.internal.acroformPlugin.xForms,e)}(e,t)})),t.internal.acroformPlugin.isInitialized=!0}},yr=$n.__acroform__.arrayToPdfArray=function(t,e,n){var r=function(t){return t};if(Array.isArray(t)){for(var i="[",o=0;o<t.length;o++)switch(0!==o&&(i+=" "),Le(t[o])){case"boolean":case"number":case"object":i+=t[o].toString();break;case"string":"/"!==t[o].substr(0,1)?(void 0!==e&&n&&(r=n.internal.getEncryptor(e)),i+="("+Qn(r(t[o].toString()))+")"):i+=t[o].toString()}return i+"]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},wr=function(t,e,n){var r=function(t){return t};return void 0!==e&&n&&(r=n.internal.getEncryptor(e)),(t=t||"").toString(),"("+Qn(r(t))+")"},xr=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};xr.prototype.toString=function(){return this.objId+" 0 R"},xr.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},xr.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter((function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"scope"!=t&&"objId"!=t&&"_"!=t.substring(0,1)}));for(var n in e)if(!1===Object.getOwnPropertyDescriptor(this,e[n]).configurable){var r=e[n],i=this[r];i&&(Array.isArray(i)?t.push({key:r,value:yr(i,this.objId,this.scope)}):i instanceof xr?(i.scope=this.scope,t.push({key:r,value:i.objId+" 0 R"})):"function"!=typeof i&&t.push({key:r,value:i}))}return t};var Nr=function(){xr.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){t=e.trim()},get:function(){return t||null}})};rr(Nr,xr);var Lr=function(){xr.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+Qn(e(t))+")"}},set:function(e){t=e}})};rr(Lr,xr);var Ar=function t(){xr.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(e,3))},set:function(t){!0===Boolean(t)?this.F=cr(e,3):this.F=hr(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute Ff supplied.');n=t}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==r.length)return r},set:function(t){r=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:r[0]},set:function(t){r[0]=t}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:r[1]},set:function(t){r[1]=t}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:r[2]},set:function(t){r[2]=t}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:r[3]},set:function(t){r[3]=t}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw new Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof Ir)return;o="FieldObject"+t.FieldNum++}var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+Qn(e(o))+")"},set:function(t){o=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(t){o=t}});var a="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return a},set:function(t){a=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var l=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return l},set:function(t){l=t}});var u=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===u?50/Zn:u},set:function(t){u=t}});var c="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return c},set:function(t){c=t}});var h="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!h||this instanceof Ir||this instanceof Er))return wr(h,this.objId,this.scope)},set:function(t){t=t.toString(),h=t}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof Fr==0?wr(f,this.objId,this.scope):f},set:function(t){t=t.toString(),f=this instanceof Fr==0?"("===t.substr(0,1)?tr(t.substr(1,t.length-2)):tr(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Fr==1?tr(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof Fr==1?"/"+t:t}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(t){this.V=t}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof Fr==0?wr(d,this.objId,this.scope):d},set:function(t){t=t.toString(),d=this instanceof Fr==0?"("===t.substr(0,1)?tr(t.substr(1,t.length-2)):tr(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Fr==1?tr(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof Fr==1?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,g=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return g},set:function(t){t=Boolean(t),g=t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,1))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,1):this.Ff=hr(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,2))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,2):this.Ff=hr(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,3))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,3):this.Ff=hr(this.Ff,3)}});var m=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==m)return m},set:function(t){if(-1===[0,1,2].indexOf(t))throw new Error('Invalid value "'+t+'" for attribute Q supplied.');m=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(m){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:m=2;break;case"center":case 1:m=1;break;default:m=0}}})};rr(Ar,xr);var Sr=function(){Ar.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){t=e}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return yr(e,this.objId,this.scope)},set:function(t){var n,r;r=[],"string"==typeof(n=t)&&(r=function(t,e,n){n||(n=1);for(var r,i=[];r=e.exec(t);)i.push(r[n]);return i}(n,/\((.*?)\)/g)),e=r}}),this.getOptions=function(){return e},this.setOptions=function(t){e=t,this.sort&&e.sort()},this.addOption=function(t){t=(t=t||"").toString(),e.push(t),this.sort&&e.sort()},this.removeOption=function(t,n){for(n=n||!1,t=(t=t||"").toString();-1!==e.indexOf(t)&&(e.splice(e.indexOf(t),1),!1!==n););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,18))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,18):this.Ff=hr(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,19))},set:function(t){!0===this.combo&&(!0===Boolean(t)?this.Ff=cr(this.Ff,19):this.Ff=hr(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,20))},set:function(t){!0===Boolean(t)?(this.Ff=cr(this.Ff,20),e.sort()):this.Ff=hr(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,22))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,22):this.Ff=hr(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,23):this.Ff=hr(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,27))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,27):this.Ff=hr(this.Ff,27)}}),this.hasAppearanceStream=!1};rr(Sr,Ar);var _r=function(){Sr.call(this),this.fontName="helvetica",this.combo=!1};rr(_r,Sr);var Pr=function(){_r.call(this),this.combo=!0};rr(Pr,_r);var kr=function(){Pr.call(this),this.edit=!0};rr(kr,Pr);var Fr=function(){Ar.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,15))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,15):this.Ff=hr(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,16))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,16):this.Ff=hr(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,17))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,17):this.Ff=hr(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,26):this.Ff=hr(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(e).length){var n,r=[];for(n in r.push("<<"),e)r.push("/"+n+" ("+Qn(t(e[n]))+")");return r.push(">>"),r.join("\n")}},set:function(t){"object"===Le(t)&&(e=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(t){"string"==typeof t&&(e.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(e){t="/"+e}})};rr(Fr,Ar);var Cr=function(){Fr.call(this),this.pushButton=!0};rr(Cr,Fr);var jr=function(){Fr.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=void 0!==e?e:[]}})};rr(jr,Fr);var Ir=function(){var t,e;Ar.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(t){e=t}});var n,r={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var e,n=[];for(e in n.push("<<"),r)n.push("/"+e+" ("+Qn(t(r[e]))+")");return n.push(">>"),n.join("\n")},set:function(t){"object"===Le(t)&&(r=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return r.CA||""},set:function(t){"string"==typeof t&&(r.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){n=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(t){n="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Br.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};rr(Ir,Ar),jr.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n.optionName),n.caption=t.getCA()}},jr.prototype.createOption=function(t){var e=new Ir;return e.Parent=this,e.optionName=t,this.Kids.push(e),Mr.call(this.scope,e),e};var Or=function(){Fr.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Br.CheckBox.createAppearanceStream()};rr(Or,Fr);var Er=function(){Ar.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,13))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,13):this.Ff=hr(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,21))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,21):this.Ff=hr(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,23):this.Ff=hr(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,24))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,24):this.Ff=hr(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,25))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,25):this.Ff=hr(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,26):this.Ff=hr(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};rr(Er,Ar);var Rr=function(){Er.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return Boolean(ur(this.Ff,14))},set:function(t){!0===Boolean(t)?this.Ff=cr(this.Ff,14):this.Ff=hr(this.Ff,14)}}),this.password=!0};rr(Rr,Er);var Br={CheckBox:{createAppearanceStream:function(){return{N:{On:Br.CheckBox.YesNormal},D:{On:Br.CheckBox.YesPushDown,Off:Br.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=or(t);e.scope=t.scope;var n=[],r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,i=t.scope.__private__.encodeColorString(t.color),o=pr(t,t.caption);return n.push("0.749023 g"),n.push("0 0 "+er(Br.internal.getWidth(t))+" "+er(Br.internal.getHeight(t))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+r+" "+er(o.fontSize)+" Tf "+i),n.push("BT"),n.push(o.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join("\n"),e},YesNormal:function(t){var e=or(t);e.scope=t.scope;var n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color),i=[],o=Br.internal.getHeight(t),a=Br.internal.getWidth(t),s=pr(t,t.caption);return i.push("1 g"),i.push("0 0 "+er(a)+" "+er(o)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+er(a-1)+" "+er(o-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+n+" "+er(s.fontSize)+" Tf "+r),i.push(s.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=or(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+er(Br.internal.getWidth(t))+" "+er(Br.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:Br.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=Br.RadioButton.Circle.YesNormal,e.D[t]=Br.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=or(t);e.scope=t.scope;var n=[],r=Br.internal.getWidth(t)<=Br.internal.getHeight(t)?Br.internal.getWidth(t)/4:Br.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Br.internal.Bezier_C,o=Number((r*i).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+nr(Br.internal.getWidth(t)/2)+" "+nr(Br.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+o+" "+o+" "+r+" 0 "+r+" c"),n.push("-"+o+" "+r+" -"+r+" "+o+" -"+r+" 0 c"),n.push("-"+r+" -"+o+" -"+o+" -"+r+" 0 -"+r+" c"),n.push(o+" -"+r+" "+r+" -"+o+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=or(t);e.scope=t.scope;var n=[],r=Br.internal.getWidth(t)<=Br.internal.getHeight(t)?Br.internal.getWidth(t)/4:Br.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),o=Number((i*Br.internal.Bezier_C).toFixed(5)),a=Number((r*Br.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+nr(Br.internal.getWidth(t)/2)+" "+nr(Br.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),n.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),n.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),n.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+nr(Br.internal.getWidth(t)/2)+" "+nr(Br.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+a+" "+a+" "+r+" 0 "+r+" c"),n.push("-"+a+" "+r+" -"+r+" "+a+" -"+r+" 0 c"),n.push("-"+r+" -"+a+" -"+a+" -"+r+" 0 -"+r+" c"),n.push(a+" -"+r+" "+r+" -"+a+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=or(t);e.scope=t.scope;var n=[],r=Br.internal.getWidth(t)<=Br.internal.getHeight(t)?Br.internal.getWidth(t)/4:Br.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),o=Number((i*Br.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+nr(Br.internal.getWidth(t)/2)+" "+nr(Br.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),n.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),n.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),n.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:Br.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=Br.RadioButton.Cross.YesNormal,e.D[t]=Br.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=or(t);e.scope=t.scope;var n=[],r=Br.internal.calculateCross(t);return n.push("q"),n.push("1 1 "+er(Br.internal.getWidth(t)-2)+" "+er(Br.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(er(r.x1.x)+" "+er(r.x1.y)+" m"),n.push(er(r.x2.x)+" "+er(r.x2.y)+" l"),n.push(er(r.x4.x)+" "+er(r.x4.y)+" m"),n.push(er(r.x3.x)+" "+er(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=or(t);e.scope=t.scope;var n=Br.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+er(Br.internal.getWidth(t))+" "+er(Br.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+er(Br.internal.getWidth(t)-2)+" "+er(Br.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(er(n.x1.x)+" "+er(n.x1.y)+" m"),r.push(er(n.x2.x)+" "+er(n.x2.y)+" l"),r.push(er(n.x4.x)+" "+er(n.x4.y)+" m"),r.push(er(n.x3.x)+" "+er(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=or(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+er(Br.internal.getWidth(t))+" "+er(Br.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+n}};Br.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=Br.internal.getWidth(t),n=Br.internal.getHeight(t),r=Math.min(e,n);return{x1:{x:(e-r)/2,y:(n-r)/2+r},x2:{x:(e-r)/2+r,y:(n-r)/2},x3:{x:(e-r)/2,y:(n-r)/2},x4:{x:(e-r)/2+r,y:(n-r)/2+r}}}},Br.internal.getWidth=function(t){var e=0;return"object"===Le(t)&&(e=ir(t.Rect[2])),e},Br.internal.getHeight=function(t){var e=0;return"object"===Le(t)&&(e=ir(t.Rect[3])),e};var Dr,Tr,Mr=$n.addField=function(t){if(br(this,t),!(t instanceof Ar))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=t).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};function qr(t){return t.reduce((function(t,e,n){return t[e]=n,t}),{})}$n.AcroFormChoiceField=Sr,$n.AcroFormListBox=_r,$n.AcroFormComboBox=Pr,$n.AcroFormEditBox=kr,$n.AcroFormButton=Fr,$n.AcroFormPushButton=Cr,$n.AcroFormRadioButton=jr,$n.AcroFormCheckBox=Or,$n.AcroFormTextField=Er,$n.AcroFormPasswordField=Rr,$n.AcroFormAppearance=Br,$n.AcroForm={ChoiceField:Sr,ListBox:_r,ComboBox:Pr,EditBox:kr,Button:Fr,PushButton:Cr,RadioButton:jr,CheckBox:Or,TextField:Er,PasswordField:Rr,Appearance:Br},Xn.AcroForm={ChoiceField:Sr,ListBox:_r,ComboBox:Pr,EditBox:kr,Button:Fr,PushButton:Cr,RadioButton:jr,CheckBox:Or,TextField:Er,PasswordField:Rr,Appearance:Br},Xn.AcroForm,function(t){t.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},r=t.__addimage__.getImageFileTypeByImageData=function(t,r){var i,o,a,s,l,u=e;if("RGBA"===(r=r||e)||void 0!==t.data&&t.data instanceof Uint8ClampedArray&&"height"in t&&"width"in t)return"RGBA";if(N(t))for(l in n)for(a=n[l],i=0;i<a.length;i+=1){for(s=!0,o=0;o<a[i].length;o+=1)if(void 0!==a[i][o]&&a[i][o]!==t[o]){s=!1;break}if(!0===s){u=l;break}}else for(l in n)for(a=n[l],i=0;i<a.length;i+=1){for(s=!0,o=0;o<a[i].length;o+=1)if(void 0!==a[i][o]&&a[i][o]!==t.charCodeAt(o)){s=!1;break}if(!0===s){u=l;break}}return u===e&&r!==e&&(u=r),u},i=function t(e){for(var n=this.internal.write,r=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.objectId=this.internal.newObject();var o=[];if(o.push({key:"Type",value:"/XObject"}),o.push({key:"Subtype",value:"/Image"}),o.push({key:"Width",value:e.width}),o.push({key:"Height",value:e.height}),e.colorSpace===m.INDEXED?o.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.palette.length/3-1)+" "+("sMask"in e&&void 0!==e.sMask?e.objectId+2:e.objectId+1)+" 0 R]"}):(o.push({key:"ColorSpace",value:"/"+e.colorSpace}),e.colorSpace===m.DEVICE_CMYK&&o.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),o.push({key:"BitsPerComponent",value:e.bitsPerComponent}),"decodeParameters"in e&&void 0!==e.decodeParameters&&o.push({key:"DecodeParms",value:"<<"+e.decodeParameters+">>"}),"transparency"in e&&Array.isArray(e.transparency)){for(var a="",s=0,l=e.transparency.length;s<l;s++)a+=e.transparency[s]+" "+e.transparency[s]+" ";o.push({key:"Mask",value:"["+a+"]"})}void 0!==e.sMask&&o.push({key:"SMask",value:e.objectId+1+" 0 R"});var u=void 0!==e.filter?["/"+e.filter]:void 0;if(r({data:e.data,additionalKeyValues:o,alreadyAppliedFilters:u,objectId:e.objectId}),n("endobj"),"sMask"in e&&void 0!==e.sMask){var c="/Predictor "+e.predictor+" /Colors 1 /BitsPerComponent "+e.bitsPerComponent+" /Columns "+e.width,h={width:e.width,height:e.height,colorSpace:"DeviceGray",bitsPerComponent:e.bitsPerComponent,decodeParameters:c,data:e.sMask};"filter"in e&&(h.filter=e.filter),t.call(this,h)}if(e.colorSpace===m.INDEXED){var f=this.internal.newObject();r({data:A(new Uint8Array(e.palette)),objectId:f}),n("endobj")}},o=function(){var t=this.internal.collections.addImage_images;for(var e in t)i.call(this,t[e])},a=function(){var t,e=this.internal.collections.addImage_images,n=this.internal.write;for(var r in e)n("/I"+(t=e[r]).index,t.objectId,"0","R")},s=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",a))},l=function(){var t=this.internal.collections.addImage_images;return s.call(this),t},u=function(){return Object.keys(this.internal.collections.addImage_images).length},c=function(e){return"function"==typeof t["process"+e.toUpperCase()]},h=function(t){return"object"===Le(t)&&1===t.nodeType},f=function(e,n){if("IMG"===e.nodeName&&e.hasAttribute("src")){var r=""+e.getAttribute("src");if(0===r.indexOf("data:image/"))return Ln(unescape(r).split("base64,").pop());var i=t.loadFile(r,!0);if(void 0!==i)return i}if("CANVAS"===e.nodeName){if(0===e.width||0===e.height)throw new Error("Given canvas must have data. Canvas width: "+e.width+", height: "+e.height);var o;switch(n){case"PNG":o="image/png";break;case"WEBP":o="image/webp";break;default:o="image/jpeg"}return Ln(e.toDataURL(o,1).split("base64,").pop())}},d=function(t){var e=this.internal.collections.addImage_images;if(e)for(var n in e)if(t===e[n].alias)return e[n]},p=function(t,e,n){return t||e||(t=-96,e=-96),t<0&&(t=-1*n.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*n.height*72/e/this.internal.scaleFactor),0===t&&(t=e*n.width/n.height),0===e&&(e=t*n.height/n.width),[t,e]},g=function(t,e,n,r,i,o){var a=p.call(this,n,r,i),s=this.internal.getCoordinateString,u=this.internal.getVerticalCoordinateString,c=l.call(this);if(n=a[0],r=a[1],c[i.index]=i,o){o*=Math.PI/180;var h=Math.cos(o),f=Math.sin(o),d=function(t){return t.toFixed(4)},g=[d(h),d(f),d(-1*f),d(h),0,0,"cm"]}this.internal.write("q"),o?(this.internal.write([1,"0","0",1,s(t),u(e+r),"cm"].join(" ")),this.internal.write(g.join(" ")),this.internal.write([s(n),"0","0",s(r),"0","0","cm"].join(" "))):this.internal.write([s(n),"0","0",s(r),s(t),u(e+r),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")},m=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var v=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},b=t.__addimage__.sHashCode=function(t){var e,n,r=0;if("string"==typeof t)for(n=t.length,e=0;e<n;e++)r=(r<<5)-r+t.charCodeAt(e),r|=0;else if(N(t))for(n=t.byteLength/2,e=0;e<n;e++)r=(r<<5)-r+t[e],r|=0;return r},y=t.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},w=t.__addimage__.extractImageFromDataUrl=function(t){if(null==t)return null;if(!(t=t.trim()).startsWith("data:"))return null;var e=t.indexOf(",");return e<0?null:t.substring(0,e).trim().endsWith("base64")?t.substring(e+1):null},x=t.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};t.__addimage__.isArrayBuffer=function(t){return x()&&t instanceof ArrayBuffer};var N=t.__addimage__.isArrayBufferView=function(t){return x()&&"undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)},L=t.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n},A=t.__addimage__.arrayBufferToBinaryString=function(t){for(var e="",n=N(t)?t:new Uint8Array(t),r=0;r<n.length;r+=8192)e+=String.fromCharCode.apply(null,n.subarray(r,r+8192));return e};t.addImage=function(){var t,n,r,i,o,a,l,u,c;if("number"==typeof arguments[1]?(n=e,r=arguments[1],i=arguments[2],o=arguments[3],a=arguments[4],l=arguments[5],u=arguments[6],c=arguments[7]):(n=arguments[1],r=arguments[2],i=arguments[3],o=arguments[4],a=arguments[5],l=arguments[6],u=arguments[7],c=arguments[8]),"object"===Le(t=arguments[0])&&!h(t)&&"imageData"in t){var f=t;t=f.imageData,n=f.format||n||e,r=f.x||r||0,i=f.y||i||0,o=f.w||f.width||o,a=f.h||f.height||a,l=f.alias||l,u=f.compression||u,c=f.rotation||f.angle||c}var d=this.internal.getFilters();if(void 0===u&&-1!==d.indexOf("FlateEncode")&&(u="SLOW"),isNaN(r)||isNaN(i))throw new Error("Invalid coordinates passed to jsPDF.addImage");s.call(this);var p=S.call(this,t,n,l,u);return g.call(this,r,i,o,a,p,c),this};var S=function(n,i,o,a){var s,l,p,g;if("string"==typeof n&&r(n)===e){n=unescape(n);var m=_(n,!1);(""!==m||void 0!==(m=t.loadFile(n,!0)))&&(n=m)}if(h(n)&&(n=f(n,i)),i=r(n,i),!c(i))throw new Error("addImage does not support files of type '"+i+"', please ensure that a plugin for '"+i+"' support is added.");if((null==(p=o)||0===p.length)&&(o="string"==typeof(g=n)||N(g)?b(g):N(g.data)?b(g.data):null),(s=d.call(this,o))||(x()&&(n instanceof Uint8Array||"RGBA"===i||(l=n,n=L(n))),s=this["process"+i.toUpperCase()](n,u.call(this),o,function(e){return e&&"string"==typeof e&&(e=e.toUpperCase()),e in t.image_compression?e:v.NONE}(a),l)),!s)throw new Error("An unknown error occurred whilst processing the image.");return s},_=t.__addimage__.convertBase64ToBinaryString=function(t,e){e="boolean"!=typeof e||e;var n,r="";if("string"==typeof t){var i;n=null!==(i=w(t))&&void 0!==i?i:t;try{r=Ln(n)}catch(o){if(e)throw y(n)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+o.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return r};t.getImageProperties=function(n){var i,o,a="";if(h(n)&&(n=f(n)),"string"==typeof n&&r(n)===e&&(""===(a=_(n,!1))&&(a=t.loadFile(n)||""),n=a),o=r(n),!c(o))throw new Error("addImage does not support files of type '"+o+"', please ensure that a plugin for '"+o+"' support is added.");if(!x()||n instanceof Uint8Array||(n=L(n)),!(i=this["process"+o.toUpperCase()](n)))throw new Error("An unknown error occurred whilst processing the image");return i.fileType=o,i}}(Xn.API),
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
Dr=Xn.API,Tr=function(t){if(void 0!==t&&""!=t)return!0},Xn.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),Dr.events.push(["putPage",function(t){for(var e,n,r,i=this.internal.getCoordinateString,o=this.internal.getVerticalCoordinateString,a=this.internal.getPageInfoByObjId(t.objId),s=t.pageContext.annotations,l=!1,u=0;u<s.length&&!l;u++)switch((e=s[u]).type){case"link":(Tr(e.options.url)||Tr(e.options.pageNumber))&&(l=!0);break;case"reference":case"text":case"freetext":l=!0}if(0!=l){this.internal.write("/Annots [");for(var c=0;c<s.length;c++){e=s[c];var h=this.internal.pdfEscape,f=this.internal.getEncryptor(t.objId);switch(e.type){case"reference":this.internal.write(" "+e.object.objId+" 0 R ");break;case"text":var d=this.internal.newAdditionalObject(),p=this.internal.newAdditionalObject(),g=this.internal.getEncryptor(d.objId),m=e.title||"Note";r="<</Type /Annot /Subtype /Text "+(n="/Rect ["+i(e.bounds.x)+" "+o(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w)+" "+o(e.bounds.y)+"] ")+"/Contents ("+h(g(e.contents))+")",r+=" /Popup "+p.objId+" 0 R",r+=" /P "+a.objId+" 0 R",r+=" /T ("+h(g(m))+") >>",d.content=r;var v=d.objId+" 0 R";r="<</Type /Annot /Subtype /Popup "+(n="/Rect ["+i(e.bounds.x+30)+" "+o(e.bounds.y+e.bounds.h)+" "+i(e.bounds.x+e.bounds.w+30)+" "+o(e.bounds.y)+"] ")+" /Parent "+v,e.open&&(r+=" /Open true"),r+=" >>",p.content=r,this.internal.write(d.objId,"0 R",p.objId,"0 R");break;case"freetext":n="/Rect ["+i(e.bounds.x)+" "+o(e.bounds.y)+" "+i(e.bounds.x+e.bounds.w)+" "+o(e.bounds.y+e.bounds.h)+"] ";var b=e.color||"#000000";r="<</Type /Annot /Subtype /FreeText "+n+"/Contents ("+h(f(e.contents))+")",r+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+b+")",r+=" /Border [0 0 0]",r+=" >>",this.internal.write(r);break;case"link":if(e.options.name){var y=this.annotations._nameMap[e.options.name];e.options.pageNumber=y.page,e.options.top=y.y}else e.options.top||(e.options.top=0);if(n="/Rect ["+e.finalBounds.x+" "+e.finalBounds.y+" "+e.finalBounds.w+" "+e.finalBounds.h+"] ",r="",e.options.url)r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /A <</S /URI /URI ("+h(f(e.options.url))+") >>";else if(e.options.pageNumber)switch(r="<</Type /Annot /Subtype /Link "+n+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(e.options.pageNumber).objId+" 0 R",e.options.magFactor=e.options.magFactor||"XYZ",e.options.magFactor){case"Fit":r+=" /Fit]";break;case"FitH":r+=" /FitH "+e.options.top+"]";break;case"FitV":e.options.left=e.options.left||0,r+=" /FitV "+e.options.left+"]";break;default:var w=o(e.options.top);e.options.left=e.options.left||0,void 0===e.options.zoom&&(e.options.zoom=0),r+=" /XYZ "+e.options.left+" "+w+" "+e.options.zoom+"]"}""!=r&&(r+=" >>",this.internal.write(r))}}this.internal.write("]")}}]),Dr.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},Dr.link=function(t,e,n,r,i){var o=this.internal.getCurrentPageInfo(),a=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString;o.pageContext.annotations.push({finalBounds:{x:a(t),y:s(e),w:a(t+n),h:s(e+r)},options:i,type:"link"})},Dr.textWithLink=function(t,e,n,r){var i,o,a=this.getTextWidth(t),s=this.internal.getLineHeight()/this.internal.scaleFactor;if(void 0!==r.maxWidth){o=r.maxWidth;var l=this.splitTextToSize(t,o).length;i=Math.ceil(s*l)}else o=a,i=s;return this.text(t,e,n,r),n+=.2*s,"center"===r.align&&(e-=a/2),"right"===r.align&&(e-=a),this.link(e,n-s,o,i,r),a},Dr.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor},
/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},r={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];t.__arabicParser__={};var o=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==e[t.charCodeAt(0)]},a=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},s=t.__arabicParser__.isArabicEndLetter=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length<=2},l=t.__arabicParser__.isArabicAlfLetter=function(t){return a(t)&&i.indexOf(t.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=1};var u=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return a(t)&&o(t)&&e[t.charCodeAt(0)].length>=3};var c=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return a(t)&&o(t)&&4==e[t.charCodeAt(0)].length},h=t.__arabicParser__.resolveLigatures=function(t){var e=0,r=n,i="",o=0;for(e=0;e<t.length;e+=1)void 0!==r[t.charCodeAt(e)]?(o++,"number"==typeof(r=r[t.charCodeAt(e)])&&(i+=String.fromCharCode(r),r=n,o=0),e===t.length-1&&(r=n,i+=t.charAt(e-(o-1)),e-=o-1,o=0)):(r=n,i+=t.charAt(e-o),e-=o,o=0);return i};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==r[t.charCodeAt(0)]};var f=t.__arabicParser__.getCorrectForm=function(t,e,n){return a(t)?!1===o(t)?-1:!u(t)||!a(e)&&!a(n)||!a(n)&&s(e)||s(t)&&!a(e)||s(t)&&l(e)||s(t)&&s(e)?0:c(t)&&a(e)&&!s(e)&&a(n)&&u(n)?3:s(t)||!a(n)?1:2:-1},d=function(t){var n=0,r=0,i=0,o="",s="",l="",u=(t=t||"").split("\\s+"),c=[];for(n=0;n<u.length;n+=1){for(c.push(""),r=0;r<u[n].length;r+=1)o=u[n][r],s=u[n][r-1],l=u[n][r+1],a(o)?(i=f(o,s,l),c[n]+=-1!==i?String.fromCharCode(e[o.charCodeAt(0)][i]):o):c[n]+=o;c[n]=h(c[n])}return c.join(" ")},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,n=[];if(Array.isArray(e)){var r=0;for(n=[],r=0;r<e.length;r+=1)Array.isArray(e[r])?n.push([d(e[r][0]),e[r][1],e[r][2]]):n.push([d(e[r])]);t=n}else t=d(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(Xn.API),Xn.API.autoPrint=function(t){var e;if("javascript"===((t=t||{}).variant=t.variant||"non-conform",t.variant))this.addJS("print({});");else this.internal.events.subscribe("postPutResources",(function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")})),this.internal.events.subscribe("putCatalog",(function(){this.internal.out("/OpenAction "+e+" 0 R")}));return this},
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(e){t=e}});var e=150;Object.defineProperty(this,"width",{get:function(){return e},set:function(t){e=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=e+1)}});var n=300;Object.defineProperty(this,"height",{get:function(){return n},set:function(t){n=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=n+1)}});var r=[];Object.defineProperty(this,"childNodes",{get:function(){return r},set:function(t){r=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(t,e){var n;if("2d"!==(t=t||"2d"))return null;for(n in e)this.pdf.context2d.hasOwnProperty(n)&&(this.pdf.context2d[n]=e[n]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Xn.API),function(t){var e={left:0,top:0,bottom:0,right:0},n=!1,r=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),i.call(this))},i=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var t=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return t},set:function(e){t=e}});var e=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var n=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var r=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var i=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var o=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return o},set:function(t){o=t}});var a=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return a},set:function(t){a=t}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return r.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){r.call(this);var n=(e=e||{}).fontSize||this.getFontSize(),i=e.font||this.getFont(),o=e.scaleFactor||this.internal.scaleFactor,a=0,s=0,l=0,u=this;if(!Array.isArray(t)&&"string"!=typeof t){if("number"!=typeof t)throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");t=String(t)}var c=e.maxWidth;c>0?"string"==typeof t?t=this.splitTextToSize(t,c):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce((function(t,e){return t.concat(u.splitTextToSize(e,c))}),[])):t=Array.isArray(t)?t:[t];for(var h=0;h<t.length;h++)a<(l=this.getStringUnitWidth(t[h],{font:i})*n)&&(a=l);return 0!==a&&(s=t.length),{w:a/=o,h:Math.max((s*n*this.getLineHeightFactor()-n*(this.getLineHeightFactor()-1))/o,0)}},t.cellAddPage=function(){r.call(this),this.addPage();var t=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var a=t.cell=function(){var t;t=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),r.call(this);var i=this.internal.__cell__.lastCell,a=this.internal.__cell__.padding,s=this.internal.__cell__.margins||e,l=this.internal.__cell__.tableHeaderRow,u=this.internal.__cell__.printHeaders;return void 0!==i.lineNumber&&(i.lineNumber===t.lineNumber?(t.x=(i.x||0)+(i.width||0),t.y=i.y||0):i.y+i.height+t.height+s.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=s.top,u&&l&&(this.printHeaderRow(t.lineNumber,!0),t.y+=l[0].height)):t.y=i.y+i.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===n?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-a,t.y+a,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+a,{align:"center",baseline:"top",maxWidth:t.width-a-a}):this.text(t.text,t.x+a,t.y+a,{align:"left",baseline:"top",maxWidth:t.width-a-a})),this.internal.__cell__.lastCell=t,this};t.table=function(t,n,l,u,c){if(r.call(this),!l)throw new Error("No data for PDF table.");var h,f,d,p,g=[],m=[],v=[],b={},y={},w=[],x=[],N=(c=c||{}).autoSize||!1,L=!1!==c.printHeaders,A=c.css&&void 0!==c.css["font-size"]?16*c.css["font-size"]:c.fontSize||12,S=c.margins||Object.assign({width:this.getPageWidth()},e),_="number"==typeof c.padding?c.padding:3,P=c.headerBackgroundColor||"#c8c8c8",k=c.headerTextColor||"#000";if(i.call(this),this.internal.__cell__.printHeaders=L,this.internal.__cell__.margins=S,this.internal.__cell__.table_font_size=A,this.internal.__cell__.padding=_,this.internal.__cell__.headerBackgroundColor=P,this.internal.__cell__.headerTextColor=k,this.setFontSize(A),null==u)m=g=Object.keys(l[0]),v=g.map((function(){return"left"}));else if(Array.isArray(u)&&"object"===Le(u[0]))for(g=u.map((function(t){return t.name})),m=u.map((function(t){return t.prompt||t.name||""})),v=u.map((function(t){return t.align||"left"})),h=0;h<u.length;h+=1)y[u[h].name]=u[h].width*(19.049976/25.4);else Array.isArray(u)&&"string"==typeof u[0]&&(m=g=u,v=g.map((function(){return"left"})));if(N||Array.isArray(u)&&"string"==typeof u[0])for(h=0;h<g.length;h+=1){for(b[p=g[h]]=l.map((function(t){return t[p]})),this.setFont(void 0,"bold"),w.push(this.getTextDimensions(m[h],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),f=b[p],this.setFont(void 0,"normal"),d=0;d<f.length;d+=1)w.push(this.getTextDimensions(f[d],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);y[p]=Math.max.apply(null,w)+_+_,w=[]}if(L){var F={};for(h=0;h<g.length;h+=1)F[g[h]]={},F[g[h]].text=m[h],F[g[h]].align=v[h];var C=s.call(this,F,y);x=g.map((function(e){return new o(t,n,y[e],C,F[e].text,void 0,F[e].align)})),this.setTableHeaderRow(x),this.printHeaderRow(1,!1)}var j=u.reduce((function(t,e){return t[e.name]=e.align,t}),{});for(h=0;h<l.length;h+=1){"rowStart"in c&&c.rowStart instanceof Function&&c.rowStart({row:h,data:l[h]},this);var I=s.call(this,l[h],y);for(d=0;d<g.length;d+=1){var O=l[h][g[d]];"cellStart"in c&&c.cellStart instanceof Function&&c.cellStart({row:h,col:d,data:O},this),a.call(this,new o(t,n,y[g[d]],I,O,h+2,j[g[d]]))}}return this.internal.__cell__.table_x=t,this.internal.__cell__.table_y=n,this};var s=function(t,e){var n=this.internal.__cell__.padding,r=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map((function(r){var i=t[r];return this.splitTextToSize(i.hasOwnProperty("text")?i.text:i,e[r]-n-n)}),this).map((function(t){return this.getLineHeightFactor()*t.length*r/i+n+n}),this).reduce((function(t,e){return Math.max(t,e)}),0)};t.setTableHeaderRow=function(t){r.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(r.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var i;if(n=!0,"function"==typeof this.internal.__cell__.headerFunction){var s=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(s[0],s[1],s[2],s[3],void 0,-1)}this.setFont(void 0,"bold");for(var l=[],u=0;u<this.internal.__cell__.tableHeaderRow.length;u+=1){i=this.internal.__cell__.tableHeaderRow[u].clone(),e&&(i.y=this.internal.__cell__.margins.top||0,l.push(i)),i.lineNumber=t;var c=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),a.call(this,i),this.setTextColor(c)}l.length>0&&this.setTableHeaderRow(l),this.setFont(void 0,"normal"),n=!1}}(Xn.API);var Ur={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},zr=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Wr=qr(zr),Hr=[100,200,300,400,500,600,700,800,900],Vr=qr(Hr);function Gr(t){var e,n=t.family.replace(/"|'/g,"").toLowerCase(),r=(e=t.style,Ur[e=e||"normal"]?e:"normal"),i=function(t){return t?"number"==typeof t?t>=100&&t<=900&&t%100==0?t:400:/^\d00$/.test(t)?parseInt(t):"bold"===t?700:400:400}(t.weight),o=function(t){return"number"==typeof Wr[t=t||"normal"]?t:"normal"}(t.stretch);return{family:n,style:r,weight:i,stretch:o,src:t.src||[],ref:t.ref||{name:n,style:[o,r,i].join(" ")}}}function Jr(t,e,n,r){var i;for(i=n;i>=0&&i<e.length;i+=r)if(t[e[i]])return t[e[i]];for(i=n;i>=0&&i<e.length;i-=r)if(t[e[i]])return t[e[i]]}var Yr={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},Kr={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Xr(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function $r(t){return t.trimLeft()}function Zr(t,e){for(var n=0;n<t.length;){if(t.charAt(n)===e)return[t.substring(0,n),t.substring(n+1)];n+=1}return null}function Qr(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===e?null:[e[0],t.substring(e[0].length)]}var ti,ei,ni,ri=["times"];!function(t){var e,n,r,i,o,a,s,l,u,c=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new l,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new a,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new a,this.lineDashOffset=t.lineDashOffset||0,this.lineDash=t.lineDash||[],this.margin=t.margin||[0,0,0,0],this.prevPageLastElemOffset=t.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new h(this),e=this.internal.f2,n=this.internal.getCoordinateString,r=this.internal.getVerticalCoordinateString,i=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,a=this.internal.Point,s=this.internal.Rectangle,l=this.internal.Matrix,u=new c}]);var h=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var e=t;Object.defineProperty(this,"pdf",{get:function(){return e}});var n=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return n},set:function(t){n=Boolean(t)}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=Boolean(t)}});var i=0;Object.defineProperty(this,"posX",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}});var o=0;Object.defineProperty(this,"posY",{get:function(){return o},set:function(t){isNaN(t)||(o=t)}}),Object.defineProperty(this,"margin",{get:function(){return u.margin},set:function(t){var e;"number"==typeof t?e=[t,t,t,t]:((e=new Array(4))[0]=t[0],e[1]=t.length>=2?t[1]:e[0],e[2]=t.length>=3?t[2]:e[0],e[3]=t.length>=4?t[3]:e[1]),u.margin=e}});var a=!1;Object.defineProperty(this,"autoPaging",{get:function(){return a},set:function(t){a=t}});var s=0;Object.defineProperty(this,"lastBreak",{get:function(){return s},set:function(t){s=t}});var l=[];Object.defineProperty(this,"pageBreaks",{get:function(){return l},set:function(t){l=t}}),Object.defineProperty(this,"ctx",{get:function(){return u},set:function(t){t instanceof c&&(u=t)}}),Object.defineProperty(this,"path",{get:function(){return u.path},set:function(t){u.path=t}});var h=[];Object.defineProperty(this,"ctxStack",{get:function(){return h},set:function(t){h=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=f(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=f(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}});var d=null;var p=null;Object.defineProperty(this,"fontFaces",{get:function(){return p},set:function(t){d=null,p=t}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var n=e[1];e[2];var r=e[3],i=e[4];e[5];var o=e[6],a=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===a?Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor):"em"===a?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(i);var s=function(t){var e,n,r=[],i=t.trim();if(""===i)return ri;if(i in Kr)return[Kr[i]];for(;""!==i;){switch(n=null,e=(i=$r(i)).charAt(0)){case'"':case"'":n=Zr(i.substring(1),e);break;default:n=Qr(i)}if(null===n)return ri;if(r.push(n[0]),""!==(i=$r(n[1]))&&","!==i.charAt(0))return ri;i=i.replace(/^,/,"")}return r}(o);if(this.fontFaces){var l=function(t,e,n){for(var r=(n=n||{}).defaultFontFamily||"times",i=Object.assign({},Yr,n.genericFontFamilies||{}),o=null,a=null,s=0;s<e.length;++s)if(i[(o=Gr(e[s])).family]&&(o.family=i[o.family]),t.hasOwnProperty(o.family)){a=t[o.family];break}if(!(a=a||t[r]))throw new Error("Could not find a font-family for the rule '"+Xr(o)+"' and default family '"+r+"'.");if(a=function(t,e){if(e[t])return e[t];var n=Wr[t],r=n<=Wr.normal?-1:1,i=Jr(e,zr,n,r);if(!i)throw new Error("Could not find a matching font-stretch value for "+t);return i}(o.stretch,a),a=function(t,e){if(e[t])return e[t];for(var n=Ur[t],r=0;r<n.length;++r)if(e[n[r]])return e[n[r]];throw new Error("Could not find a matching font-style for "+t)}(o.style,a),!(a=function(t,e){if(e[t])return e[t];if(400===t&&e[500])return e[500];if(500===t&&e[400])return e[400];var n=Vr[t],r=Jr(e,Hr,n,t<400?-1:1);if(!r)throw new Error("Could not find a matching font-weight for value "+t);return r}(o.weight,a)))throw new Error("Failed to resolve a font for the rule '"+Xr(o)+"'.");return a}(function(t,e){if(null===d){var n=(r=t.getFontList(),i=[],Object.keys(r).forEach((function(t){r[t].forEach((function(e){var n=null;switch(e){case"bold":n={family:t,weight:"bold"};break;case"italic":n={family:t,style:"italic"};break;case"bolditalic":n={family:t,weight:"bold",style:"italic"};break;case"":case"normal":n={family:t}}null!==n&&(n.ref={name:t,style:e},i.push(n))}))})),i);d=function(t){for(var e={},n=0;n<t.length;++n){var r=Gr(t[n]),i=r.family,o=r.stretch,a=r.style,s=r.weight;e[i]=e[i]||{},e[i][o]=e[i][o]||{},e[i][o][a]=e[i][o][a]||{},e[i][o][a][s]=r}return e}(n.concat(e))}var r,i;return d}(this.pdf,this.fontFaces),s.map((function(t){return{family:t,stretch:"normal",weight:r,style:n}})));this.pdf.setFont(l.ref.name,l.ref.style)}else{var u="";("bold"===r||parseInt(r,10)>=700||"bold"===n)&&(u="bold"),"italic"===n&&(u+="italic"),0===u.length&&(u="normal");for(var c="",h={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},f=0;f<s.length;f++){if(void 0!==this.pdf.internal.getFont(s[f],u,{noFallback:!0,disableWarning:!0})){c=s[f];break}if("bolditalic"===u&&void 0!==this.pdf.internal.getFont(s[f],"bold",{noFallback:!0,disableWarning:!0}))c=s[f],u="bold";else if(void 0!==this.pdf.internal.getFont(s[f],"normal",{noFallback:!0,disableWarning:!0})){c=s[f],u="normal";break}}if(""===c)for(var p=0;p<s.length;p++)if(h[s[p]]){c=h[s[p]];break}c=""===c?"Times":c,this.pdf.setFont(c,u)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(t){this.ctx.lineDashOffset=t,D.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(t){this.ctx.lineDash=t,D.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=Boolean(t)}})};h.prototype.setLineDash=function(t){this.lineDash=t},h.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},h.prototype.fill=function(){w.call(this,"fill",!1)},h.prototype.stroke=function(){w.call(this,"stroke",!1)},h.prototype.beginPath=function(){this.path=[{type:"begin"}]},h.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw yn.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var n=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"mt",x:n.x,y:n.y}),this.ctx.lastPoint=new a(t,e)},h.prototype.closePath=function(){var t=new a(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===Le(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new a(this.path[e+1].x,this.path[e+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new a(t.x,t.y)},h.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw yn.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var n=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"lt",x:n.x,y:n.y}),this.ctx.lastPoint=new a(n.x,n.y)},h.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),w.call(this,null,!0)},h.prototype.quadraticCurveTo=function(t,e,n,r){if(isNaN(n)||isNaN(r)||isNaN(t)||isNaN(e))throw yn.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new a(n,r)),o=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"qct",x1:o.x,y1:o.y,x:i.x,y:i.y}),this.ctx.lastPoint=new a(i.x,i.y)},h.prototype.bezierCurveTo=function(t,e,n,r,i,o){if(isNaN(i)||isNaN(o)||isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw yn.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var s=this.ctx.transform.applyToPoint(new a(i,o)),l=this.ctx.transform.applyToPoint(new a(t,e)),u=this.ctx.transform.applyToPoint(new a(n,r));this.path.push({type:"bct",x1:l.x,y1:l.y,x2:u.x,y2:u.y,x:s.x,y:s.y}),this.ctx.lastPoint=new a(s.x,s.y)},h.prototype.arc=function(t,e,n,r,i,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i))throw yn.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(o=Boolean(o),!this.ctx.transform.isIdentity){var s=this.ctx.transform.applyToPoint(new a(t,e));t=s.x,e=s.y;var l=this.ctx.transform.applyToPoint(new a(0,n)),u=this.ctx.transform.applyToPoint(new a(0,0));n=Math.sqrt(Math.pow(l.x-u.x,2)+Math.pow(l.y-u.y,2))}Math.abs(i-r)>=2*Math.PI&&(r=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:n,startAngle:r,endAngle:i,counterclockwise:o})},h.prototype.arcTo=function(t,e,n,r,i){throw new Error("arcTo not implemented.")},h.prototype.rect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw yn.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+n,e),this.lineTo(t+n,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.lineTo(t+n,e),this.lineTo(t,e)},h.prototype.fillRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw yn.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!d.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,n,r),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},h.prototype.strokeRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw yn.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");p.call(this)||(this.beginPath(),this.rect(t,e,n,r),this.stroke())},h.prototype.clearRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw yn.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,n,r))},h.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var r=new c(this.ctx);this.ctxStack.push(this.ctx),this.ctx=r}},h.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},h.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var f=function(t){var e,n,r,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))e=0,n=0,r=0,i=0;else{var o=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==o)e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=1;else if(null!==(o=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=parseFloat(o[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var a=new _n(t);t=a.ok?a.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,n=t.substring(2,3),n+=n,r=t.substring(3,4),r+=r):(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),n=parseInt(n,16),r=parseInt(r,16)}}return{r:e,g:n,b:r,a:i,style:t}},d=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},p=function(){return Boolean(this.ctx.isStrokeTransparent||0==this.globalAlpha)};h.prototype.fillText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw yn.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(r=isNaN(r)?void 0:r,!d.call(this)){var i=E(this.ctx.transform.rotation),o=this.ctx.transform.scaleX;k.call(this,{text:t,x:e,y:n,scale:o,angle:i,align:this.textAlign,maxWidth:r})}},h.prototype.strokeText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw yn.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!p.call(this)){r=isNaN(r)?void 0:r;var i=E(this.ctx.transform.rotation),o=this.ctx.transform.scaleX;k.call(this,{text:t,x:e,y:n,scale:o,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:r})}},h.prototype.measureText=function(t){if("string"!=typeof t)throw yn.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,n=this.pdf.internal.scaleFactor,r=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*r/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:i*=Math.round(96*n/72*1e4)/1e4})},h.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw yn.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var n=new l(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(n)},h.prototype.rotate=function(t){if(isNaN(t))throw yn.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new l(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},h.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw yn.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var n=new l(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(n)},h.prototype.transform=function(t,e,n,r,i,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o))throw yn.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var a=new l(t,e,n,r,i,o);this.ctx.transform=this.ctx.transform.multiply(a)},h.prototype.setTransform=function(t,e,n,r,i,o){t=isNaN(t)?1:t,e=isNaN(e)?0:e,n=isNaN(n)?0:n,r=isNaN(r)?1:r,i=isNaN(i)?0:i,o=isNaN(o)?0:o,this.ctx.transform=new l(t,e,n,r,i,o)};var g=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};h.prototype.drawImage=function(t,e,n,r,i,o,a,u,c){var h=this.pdf.getImageProperties(t),f=1,d=1,p=1,v=1;void 0!==r&&void 0!==u&&(p=u/r,v=c/i,f=h.width/r*u/r,d=h.height/i*c/i),void 0===o&&(o=e,a=n,e=0,n=0),void 0!==r&&void 0===u&&(u=r,c=i),void 0===r&&void 0===u&&(u=h.width,c=h.height);for(var w,N=this.ctx.transform.decompose(),L=E(N.rotate.shx),A=new l,S=(A=(A=(A=A.multiply(N.translate)).multiply(N.skew)).multiply(N.scale)).applyToRectangle(new s(o-e*p,a-n*v,r*f,i*d)),_=m.call(this,S),P=[],k=0;k<_.length;k+=1)-1===P.indexOf(_[k])&&P.push(_[k]);if(y(P),this.autoPaging)for(var F=P[0],C=P[P.length-1],j=F;j<C+1;j++){this.pdf.setPage(j);var I=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],O=1===j?this.posY+this.margin[0]:this.margin[0],R=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],B=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],D=1===j?0:R+(j-2)*B;if(0!==this.ctx.clip_path.length){var T=this.path;w=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=b(w,this.posX+this.margin[3],-D+O+this.ctx.prevPageLastElemOffset),x.call(this,"fill",!0),this.path=T}var M=JSON.parse(JSON.stringify(S));M=b([M],this.posX+this.margin[3],-D+O+this.ctx.prevPageLastElemOffset)[0];var q=(j>F||j<C)&&g.call(this);q&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],I,B,null).clip().discardPath()),this.pdf.addImage(t,"JPEG",M.x,M.y,M.w,M.h,null,null,L),q&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(t,"JPEG",S.x,S.y,S.w,S.h,null,null,L)};var m=function(t,e,n){var r=[];e=e||this.pdf.internal.pageSize.width,n=n||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var i=this.posY+this.ctx.prevPageLastElemOffset;switch(t.type){default:case"mt":case"lt":r.push(Math.floor((t.y+i)/n)+1);break;case"arc":r.push(Math.floor((t.y+i-t.radius)/n)+1),r.push(Math.floor((t.y+i+t.radius)/n)+1);break;case"qct":var o=R(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);r.push(Math.floor((o.y+i)/n)+1),r.push(Math.floor((o.y+o.h+i)/n)+1);break;case"bct":var a=B(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);r.push(Math.floor((a.y+i)/n)+1),r.push(Math.floor((a.y+a.h+i)/n)+1);break;case"rect":r.push(Math.floor((t.y+i)/n)+1),r.push(Math.floor((t.y+t.h+i)/n)+1)}for(var s=0;s<r.length;s+=1)for(;this.pdf.internal.getNumberOfPages()<r[s];)v.call(this);return r},v=function(){var t=this.fillStyle,e=this.strokeStyle,n=this.font,r=this.lineCap,i=this.lineWidth,o=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=n,this.lineCap=r,this.lineWidth=i,this.lineJoin=o},b=function(t,e,n){for(var r=0;r<t.length;r++)switch(t[r].type){case"bct":t[r].x2+=e,t[r].y2+=n;case"qct":t[r].x1+=e,t[r].y1+=n;default:t[r].x+=e,t[r].y+=n}return t},y=function(t){return t.sort((function(t,e){return t-e}))},w=function(t,e){for(var n,r,i=this.fillStyle,o=this.strokeStyle,a=this.lineCap,s=this.lineWidth,l=Math.abs(s*this.ctx.transform.scaleX),u=this.lineJoin,c=JSON.parse(JSON.stringify(this.path)),h=JSON.parse(JSON.stringify(this.path)),f=[],d=0;d<h.length;d++)if(void 0!==h[d].x)for(var p=m.call(this,h[d]),w=0;w<p.length;w+=1)-1===f.indexOf(p[w])&&f.push(p[w]);for(var N=0;N<f.length;N++)for(;this.pdf.internal.getNumberOfPages()<f[N];)v.call(this);if(y(f),this.autoPaging)for(var L=f[0],A=f[f.length-1],S=L;S<A+1;S++){this.pdf.setPage(S),this.fillStyle=i,this.strokeStyle=o,this.lineCap=a,this.lineWidth=l,this.lineJoin=u;var _=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],P=1===S?this.posY+this.margin[0]:this.margin[0],k=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],F=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],C=1===S?0:k+(S-2)*F;if(0!==this.ctx.clip_path.length){var j=this.path;n=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=b(n,this.posX+this.margin[3],-C+P+this.ctx.prevPageLastElemOffset),x.call(this,t,!0),this.path=j}if(r=JSON.parse(JSON.stringify(c)),this.path=b(r,this.posX+this.margin[3],-C+P+this.ctx.prevPageLastElemOffset),!1===e||0===S){var I=(S>L||S<A)&&g.call(this);I&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],_,F,null).clip().discardPath()),x.call(this,t,e),I&&this.pdf.restoreGraphicsState()}this.lineWidth=s}else this.lineWidth=l,x.call(this,t,e),this.lineWidth=s;this.path=c},x=function(t,e){if(("stroke"!==t||e||!p.call(this))&&("stroke"===t||e||!d.call(this))){for(var n,r,i=[],o=this.path,a=0;a<o.length;a++){var s=o[a];switch(s.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:s,deltas:[],abs:[]});break;case"lt":var l=i.length;if(o[a-1]&&!isNaN(o[a-1].x)&&(n=[s.x-o[a-1].x,s.y-o[a-1].y],l>0))for(;l>=0;l--)if(!0!==i[l-1].close&&!0!==i[l-1].begin){i[l-1].deltas.push(n),i[l-1].abs.push(s);break}break;case"bct":n=[s.x1-o[a-1].x,s.y1-o[a-1].y,s.x2-o[a-1].x,s.y2-o[a-1].y,s.x-o[a-1].x,s.y-o[a-1].y],i[i.length-1].deltas.push(n);break;case"qct":var u=o[a-1].x+2/3*(s.x1-o[a-1].x),c=o[a-1].y+2/3*(s.y1-o[a-1].y),h=s.x+2/3*(s.x1-s.x),f=s.y+2/3*(s.y1-s.y),g=s.x,m=s.y;n=[u-o[a-1].x,c-o[a-1].y,h-o[a-1].x,f-o[a-1].y,g-o[a-1].x,m-o[a-1].y],i[i.length-1].deltas.push(n);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(s)}}r=e?null:"stroke"===t?"stroke":"fill";for(var v=!1,b=0;b<i.length;b++)if(i[b].arc)for(var y=i[b].abs,w=0;w<y.length;w++){var x=y[w];"arc"===x.type?A.call(this,x.x,x.y,x.radius,x.startAngle,x.endAngle,x.counterclockwise,void 0,e,!v):F.call(this,x.x,x.y),v=!0}else if(!0===i[b].close)this.pdf.internal.out("h"),v=!1;else if(!0!==i[b].begin){var N=i[b].start.x,L=i[b].start.y;C.call(this,i[b].deltas,N,L),v=!0}r&&S.call(this,r),e&&_.call(this)}},N=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,n=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-n;case"top":return t+e-n;case"hanging":return t+e-2*n;case"middle":return t+e/2-n;default:return t}},L=function(t){return t+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};h.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},h.prototype.createPattern=function(){return this.createLinearGradient()},h.prototype.createRadialGradient=function(){return this.createLinearGradient()};var A=function(t,e,n,r,i,o,a,s,l){for(var u=I.call(this,n,r,i,o),c=0;c<u.length;c++){var h=u[c];0===c&&(l?P.call(this,h.x1+t,h.y1+e):F.call(this,h.x1+t,h.y1+e)),j.call(this,t,e,h.x2,h.y2,h.x3,h.y3,h.x4,h.y4)}s?_.call(this):S.call(this,a)},S=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},_=function(){this.pdf.clip(),this.pdf.discardPath()},P=function(t,e){this.pdf.internal.out(n(t)+" "+r(e)+" m")},k=function(t){var e;switch(t.align){case"right":case"end":e="right";break;case"center":e="center";break;default:e="left"}var n=this.pdf.getTextDimensions(t.text),r=N.call(this,t.y),i=L.call(this,r)-n.h,o=this.ctx.transform.applyToPoint(new a(t.x,r)),u=this.ctx.transform.decompose(),c=new l;c=(c=(c=c.multiply(u.translate)).multiply(u.skew)).multiply(u.scale);for(var h,f,d,p=this.ctx.transform.applyToRectangle(new s(t.x,r,n.w,n.h)),v=c.applyToRectangle(new s(t.x,i,n.w,n.h)),w=m.call(this,v),A=[],S=0;S<w.length;S+=1)-1===A.indexOf(w[S])&&A.push(w[S]);if(y(A),this.autoPaging)for(var _=A[0],P=A[A.length-1],k=_;k<P+1;k++){this.pdf.setPage(k);var F=1===k?this.posY+this.margin[0]:this.margin[0],C=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],j=this.pdf.internal.pageSize.height-this.margin[2],I=j-this.margin[0],O=this.pdf.internal.pageSize.width-this.margin[1],E=O-this.margin[3],R=1===k?0:C+(k-2)*I;if(0!==this.ctx.clip_path.length){var B=this.path;h=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=b(h,this.posX+this.margin[3],-1*R+F),x.call(this,"fill",!0),this.path=B}var D=b([JSON.parse(JSON.stringify(v))],this.posX+this.margin[3],-R+F+this.ctx.prevPageLastElemOffset)[0];t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale);var T="text"!==this.autoPaging;if(T||D.y+D.h<=j){if(T||D.y>=F&&D.x<=O){var M=T?t.text:this.pdf.splitTextToSize(t.text,t.maxWidth||O-D.x)[0],q=b([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-R+F+this.ctx.prevPageLastElemOffset)[0],U=T&&(k>_||k<P)&&g.call(this);U&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],E,I,null).clip().discardPath()),this.pdf.text(M,q.x,q.y,{angle:t.angle,align:e,renderingMode:t.renderingMode}),U&&this.pdf.restoreGraphicsState()}}else D.y<j&&(this.ctx.prevPageLastElemOffset+=j-D.y);t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)}else t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale),this.pdf.text(t.text,o.x+this.posX,o.y+this.posY,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)},F=function(t,e,i,o){i=i||0,o=o||0,this.pdf.internal.out(n(t+i)+" "+r(e+o)+" l")},C=function(t,e,n){return this.pdf.lines(t,e,n,null,null)},j=function(t,n,r,a,s,l,u,c){this.pdf.internal.out([e(i(r+t)),e(o(a+n)),e(i(s+t)),e(o(l+n)),e(i(u+t)),e(o(c+n)),"c"].join(" "))},I=function(t,e,n,r){for(var i=2*Math.PI,o=Math.PI/2;e>n;)e-=i;var a=Math.abs(n-e);a<i&&r&&(a=i-a);for(var s=[],l=r?-1:1,u=e;a>1e-5;){var c=u+l*Math.min(a,o);s.push(O.call(this,t,u,c)),a-=Math.abs(c-u),u=c}return s},O=function(t,e,n){var r=(n-e)/2,i=t*Math.cos(r),o=t*Math.sin(r),a=i,s=-o,l=a*a+s*s,u=l+a*i+s*o,c=4/3*(Math.sqrt(2*l*u)-u)/(a*o-s*i),h=a-c*s,f=s+c*a,d=h,p=-f,g=r+e,m=Math.cos(g),v=Math.sin(g);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:h*m-f*v,y2:h*v+f*m,x3:d*m-p*v,y3:d*v+p*m,x4:t*Math.cos(n),y4:t*Math.sin(n)}},E=function(t){return 180*t/Math.PI},R=function(t,e,n,r,i,o){var a=t+.5*(n-t),l=e+.5*(r-e),u=i+.5*(n-i),c=o+.5*(r-o),h=Math.min(t,i,a,u),f=Math.max(t,i,a,u),d=Math.min(e,o,l,c),p=Math.max(e,o,l,c);return new s(h,d,f-h,p-d)},B=function(t,e,n,r,i,o,a,l){var u,c,h,f,d,p,g,m,v,b,y,w,x,N,L=n-t,A=r-e,S=i-n,_=o-r,P=a-i,k=l-o;for(c=0;c<41;c++)v=(g=(h=t+(u=c/40)*L)+u*((d=n+u*S)-h))+u*(d+u*(i+u*P-d)-g),b=(m=(f=e+u*A)+u*((p=r+u*_)-f))+u*(p+u*(o+u*k-p)-m),0==c?(y=v,w=b,x=v,N=b):(y=Math.min(y,v),w=Math.min(w,b),x=Math.max(x,v),N=Math.max(N,b));return new s(Math.round(y),Math.round(w),Math.round(x-y),Math.round(N-w))},D=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var t,e,n=(t=this.ctx.lineDash,e=this.ctx.lineDashOffset,JSON.stringify({lineDash:t,lineDashOffset:e}));this.prevLineDash!==n&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=n)}}}(Xn.API),
/**
 * @license
 * jsPDF filters PlugIn
 * Copyright (c) 2014 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(t){var e,n,r,i,o,a,s,l,u,c;for(n=[],r=0,i=(t+=e="\0\0\0\0".slice(t.length%4||4)).length;i>r;r+=4)0!==(o=(t.charCodeAt(r)<<24)+(t.charCodeAt(r+1)<<16)+(t.charCodeAt(r+2)<<8)+t.charCodeAt(r+3))?(a=(o=((o=((o=((o=(o-(c=o%85))/85)-(u=o%85))/85)-(l=o%85))/85)-(s=o%85))/85)%85,n.push(a+33,s+33,l+33,u+33,c+33)):n.push(122);return function(t,e){for(var n=e;n>0;n--)t.pop()}(n,e.length),String.fromCharCode.apply(String,n)+"~>"},n=function(t){var e,n,r,i,o,a=String,s="length",l=255,u="charCodeAt",c="slice",h="replace";for(t[c](-2),t=t[c](0,-2)[h](/\s/g,"")[h]("z","!!!!!"),r=[],i=0,o=(t+=e="uuuuu"[c](t[s]%5||5))[s];o>i;i+=5)n=52200625*(t[u](i)-33)+614125*(t[u](i+1)-33)+7225*(t[u](i+2)-33)+85*(t[u](i+3)-33)+(t[u](i+4)-33),r.push(l&n>>24,l&n>>16,l&n>>8,l&n);return function(t,e){for(var n=e;n>0;n--)t.pop()}(r,e[s]),a.fromCharCode.apply(a,r)},r=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var n="",r=0;r<t.length;r+=2)n+=String.fromCharCode("0x"+(t[r]+t[r+1]));return n},i=function(t){for(var e=new Uint8Array(t.length),n=t.length;n--;)e[n]=t.charCodeAt(n);return(e=pn(e)).reduce((function(t,e){return t+String.fromCharCode(e)}),"")};t.processDataByFilters=function(t,o){var a=0,s=t||"",l=[];for("string"==typeof(o=o||[])&&(o=[o]),a=0;a<o.length;a+=1)switch(o[a]){case"ASCII85Decode":case"/ASCII85Decode":s=n(s),l.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":s=e(s),l.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":s=r(s),l.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":s=s.split("").map((function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)})).join("")+">",l.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":s=i(s),l.push("/FlateDecode");break;default:throw new Error('The filter: "'+o[a]+'" is not implemented')}return{data:s,reverseChain:l.reverse().join(" ")}}}(Xn.API),
/**
 * @license
 * jsPDF fileloading PlugIn
 * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.loadFile=function(t,e,n){return function(t,e,n){e=!1!==e,n="function"==typeof n?n:function(){};var r=void 0;try{r=function(t,e,n){var r=new XMLHttpRequest,i=0,o=function(t){var e=t.length,n=[],r=String.fromCharCode;for(i=0;i<e;i+=1)n.push(r(255&t.charCodeAt(i)));return n.join("")};if(r.open("GET",t,!e),r.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(r.onload=function(){200===r.status?n(o(this.responseText)):n(void 0)}),r.send(null),e&&200===r.status)return o(r.responseText)}(t,e,n)}catch(i){}return r}(t,e,n)},t.loadImageFile=t.loadFile}(Xn.API),function(t){function e(){return(vn.html2canvas?Promise.resolve(vn.html2canvas):Ne((()=>import("./html2canvas.esm-BkzlXUwq.js")),[])).catch((function(t){return Promise.reject(new Error("Could not load html2canvas: "+t))})).then((function(t){return t.default?t.default:t}))}function n(){return(vn.DOMPurify?Promise.resolve(vn.DOMPurify):Ne((()=>import("./purify.es-CvAnTBAp.js")),[])).catch((function(t){return Promise.reject(new Error("Could not load dompurify: "+t))})).then((function(t){return t.default?t.default:t}))}var r=function(t){var e=Le(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},i=function(t,e){var n=document.createElement(t);for(var r in e.className&&(n.className=e.className),e.innerHTML&&e.dompurify&&(n.innerHTML=e.dompurify.sanitize(e.innerHTML)),e.style)n.style[r]=e.style[r];return n},o=function t(e){var n=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(Promise.resolve(),n);return(r=r.setProgress(1,t,1,[t])).set(e)};(o.prototype=Object.create(Promise.prototype)).constructor=o,o.convert=function(t,e){return t.__proto__=e||o.prototype,t},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},o.prototype.from=function(t,e){return this.then((function(){switch(e=e||function(t){switch(r(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.then(n).then((function(e){return this.set({src:i("div",{innerHTML:t,dompurify:e})})}));case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}}))},o.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then((function(){var t={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},e=function t(e,n){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==n&&1===i.nodeType&&"SCRIPT"===i.nodeName||r.appendChild(t(i,n));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",(function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft}),!0)),r}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=i("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=i("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(i("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"}))},o.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then((function(t){var e=Object.assign({},this.opt.html2canvas);return delete e.onrendered,t(this.prop.container,e)})).then((function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)}))},o.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then((function(t){var e=this.opt.jsPDF,n=this.opt.fontFaces,r="number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,i=Object.assign({async:!0,allowTaint:!0,scale:r,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete i.onrendered,e.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,e.context2d.posX=this.opt.x,e.context2d.posY=this.opt.y,e.context2d.margin=this.opt.margin,e.context2d.fontFaces=n,n)for(var o=0;o<n.length;++o){var a=n[o],s=a.src.find((function(t){return"truetype"===t.format}));s&&e.addFont(s.url,a.ref.name,a.ref.style)}return i.windowHeight=i.windowHeight||0,i.windowHeight=0==i.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):i.windowHeight,e.context2d.save(!0),t(this.prop.container,i)})).then((function(t){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)}))},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then((function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t}))},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then((function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF}))},o.prototype.output=function(t,e,n){return"img"===(n=n||"pdf").toLowerCase()||"image"===n.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},o.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then((function(){return this.prop.pdf.output(t,e)}))},o.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then((function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}}))},o.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then((function(){this.prop.pdf.save(this.opt.filename)}))},o.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then((function(){this.prop.callback(this.prop.pdf)}))},o.prototype.set=function(t){if("object"!==r(t))return this;var e=Object.keys(t||{}).map((function(e){if(e in o.template.prop)return function(){this.prop[e]=t[e]};switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return function(){this.opt[e]=t[e]}}}),this);return this.then((function(){return this.thenList(e)}))},o.prototype.get=function(t,e){return this.then((function(){var n=t in o.template.prop?this.prop[t]:this.opt[t];return e?e(n):n}))},o.prototype.setMargin=function(t){return this.then((function(){switch(r(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t})).then(this.setPageSize)},o.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then((function(){(t=t||Xn.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t}))},o.prototype.setProgress=function(t,e,n,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=n&&(this.progress.n=n),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(t,e,n,r){return this.setProgress(t?this.progress.val+t:null,e||null,n?this.progress.n+n:null,r?this.progress.stack.concat(r):null)},o.prototype.then=function(t,e){var n=this;return this.thenCore(t,e,(function(t,e){return n.updateProgress(null,null,1,[t]),Promise.prototype.then.call(this,(function(e){return n.updateProgress(null,t),e})).then(t,e).then((function(t){return n.updateProgress(1),t}))}))},o.prototype.thenCore=function(t,e,n){n=n||Promise.prototype.then,t&&(t=t.bind(this)),e&&(e=e.bind(this));var r=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:o.convert(Object.assign({},this),Promise.prototype),i=n.call(r,t,e);return o.convert(i,this.__proto__)},o.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},o.prototype.thenList=function(t){var e=this;return t.forEach((function(t){e=e.thenCore(t)})),e},o.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return o.convert(e,this)},o.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},o.prototype.error=function(t){return this.then((function(){throw new Error(t)}))},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,Xn.getPageSize=function(t,e,n){if("object"===Le(t)){var r=t;t=r.orientation,e=r.unit||e,n=r.format||n}e=e||"mm",n=n||"a4",t=(""+(t||"P")).toLowerCase();var i,o=(""+n).toLowerCase(),a={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":i=1;break;case"mm":i=72/25.4;break;case"cm":i=72/2.54;break;case"in":i=72;break;case"px":i=.75;break;case"pc":case"em":i=12;break;case"ex":i=6;break;default:throw"Invalid unit: "+e}var s,l=0,u=0;if(a.hasOwnProperty(o))l=a[o][1]/i,u=a[o][0]/i;else try{l=n[1],u=n[0]}catch(Dr){throw new Error("Invalid format: "+n)}if("p"===t||"portrait"===t)t="p",u>l&&(s=u,u=l,l=s);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",l>u&&(s=u,u=l,l=s)}return{width:u,height:l,unit:e,k:i,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.fontFaces=e.fontFaces?e.fontFaces.map(Gr):null;var n=new o(e);return e.worker?n:n.from(t).doCallback()}}(Xn.API),Xn.API.addJS=function(t){return ni=t,this.internal.events.subscribe("postPutResources",(function(){ti=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ti+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),ei=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+ni+")"),this.internal.out(">>"),this.internal.out("endobj")})),this.internal.events.subscribe("putCatalog",(function(){void 0!==ti&&void 0!==ei&&this.internal.out("/Names <</JavaScript "+ti+" 0 R>>")})),this},
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e;t.events.push(["postPutResources",function(){var t=this,n=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var r=t.outline.render().split(/\r\n/),i=0;i<r.length;i++){var o=r[i],a=n.exec(o);if(null!=a){var s=a[1];t.internal.newObjectDeferredBegin(s,!1)}t.internal.write(o)}if(this.outline.createNamedDestinations){var l=this.internal.pages.length,u=[];for(i=0;i<l;i++){var c=t.internal.newObject();u.push(c);var h=t.internal.getPageInfo(i+1);t.internal.write("<< /D["+h.objId+" 0 R /XYZ null null null]>> endobj")}var f=t.internal.newObject();for(t.internal.write("<< /Names [ "),i=0;i<u.length;i++)t.internal.write("(page_"+(i+1)+")"+u[i]+" 0 R");t.internal.write(" ] >>","endobj"),e=t.internal.newObject(),t.internal.write("<< /Dests "+f+" 0 R"),t.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,n){var r={title:e,options:n,children:[]};return null==t&&(t=this.root),t.children.push(r),r},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var n=0;n<e.children.length;n++)this.genIds_r(e.children[n])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(var n=this.ctx.pdf.internal.getVerticalCoordinateString,r=0;r<e.children.length;r++){var i=e.children[r];this.objStart(i),this.line("/Title "+this.makeString(i.title)),this.line("/Parent "+this.makeRef(e)),r>0&&this.line("/Prev "+this.makeRef(e.children[r-1])),r<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[r+1])),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1])));var o=this.count=this.count_r({count:0},i);if(o>0&&this.line("/Count "+o),i.options&&i.options.pageNumber){var a=t.internal.getPageInfo(i.options.pageNumber);this.line("/Dest ["+a.objId+" 0 R /XYZ 0 "+n(0)+" 0]")}this.objEnd()}for(var s=0;s<e.children.length;s++)this.renderItems(e.children[s])},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var n=0;n<e.children.length;n++)t.count++,this.count_r(t,e.children[n]);return t.count}}])}(Xn.API),
/**
 * @license
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(t,n,r,i,o,a){var s,l=this.decode.DCT_DECODE,u=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=o||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(s=function(t){for(var n,r=256*t.charCodeAt(4)+t.charCodeAt(5),i=t.length,o={width:0,height:0,numcomponents:1},a=4;a<i;a+=2){if(a+=r,-1!==e.indexOf(t.charCodeAt(a+1))){n=256*t.charCodeAt(a+5)+t.charCodeAt(a+6),o={width:256*t.charCodeAt(a+7)+t.charCodeAt(a+8),height:n,numcomponents:t.charCodeAt(a+9)};break}r=256*t.charCodeAt(a+2)+t.charCodeAt(a+3)}return o}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:a=this.color_spaces.DEVICE_GRAY;break;case 4:a=this.color_spaces.DEVICE_CMYK;break;case 3:a=this.color_spaces.DEVICE_RGB}u={data:t,width:s.width,height:s.height,colorSpace:a,bitsPerComponent:8,filter:l,index:n,alias:r}}return u}}(Xn.API);var ii,oi,ai,si,li,ui=function(){var t,e,n;function r(t){var e,n,r,i,o,a,s,l,u,c,h,f,d,p;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},a=null;;){switch(e=this.readUInt32(),u=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}.call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":a&&this.animation.frames.push(a),this.pos+=4,a={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},o=this.readUInt16(),i=this.readUInt16()||100,a.delay=1e3*o/i,a.disposeOp=this.data[this.pos++],a.blendOp=this.data[this.pos++],a.data=[];break;case"IDAT":case"fdAT":for("fdAT"===u&&(this.pos+=4,e-=4),t=(null!=a?a.data:void 0)||this.imgData,f=0;0<=e?f<e:f>e;0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(r=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>r)throw new Error("More transparent colors than palette size");if((c=r-this.transparency.indexed.length)>0)for(d=0;0<=c?d<c:d>c;0<=c?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":s=(h=this.read(e)).indexOf(0),l=String.fromCharCode.apply(String,h.slice(0,s)),this.text[l]=String.fromCharCode.apply(String,h.slice(s+1));break;case"IEND":return a&&this.animation.frames.push(a),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,n=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*n,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}r.prototype.read=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;0<=t?++e:--e)n.push(this.data[this.pos++]);return n},r.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.decodePixels=function(t){var e=this.pixelBitlength/8,n=new Uint8Array(this.width*this.height*e),r=0,i=this;if(null==t&&(t=this.imgData),0===t.length)return new Uint8Array(0);function o(o,a,s,l){var u,c,h,f,d,p,g,m,v,b,y,w,x,N,L,A,S,_,P,k,F,C=Math.ceil((i.width-o)/s),j=Math.ceil((i.height-a)/l),I=i.width==C&&i.height==j;for(N=e*C,w=I?n:new Uint8Array(N*j),p=t.length,x=0,c=0;x<j&&r<p;){switch(t[r++]){case 0:for(f=S=0;S<N;f=S+=1)w[c++]=t[r++];break;case 1:for(f=_=0;_<N;f=_+=1)u=t[r++],d=f<e?0:w[c-e],w[c++]=(u+d)%256;break;case 2:for(f=P=0;P<N;f=P+=1)u=t[r++],h=(f-f%e)/e,L=x&&w[(x-1)*N+h*e+f%e],w[c++]=(L+u)%256;break;case 3:for(f=k=0;k<N;f=k+=1)u=t[r++],h=(f-f%e)/e,d=f<e?0:w[c-e],L=x&&w[(x-1)*N+h*e+f%e],w[c++]=(u+Math.floor((d+L)/2))%256;break;case 4:for(f=F=0;F<N;f=F+=1)u=t[r++],h=(f-f%e)/e,d=f<e?0:w[c-e],0===x?L=A=0:(L=w[(x-1)*N+h*e+f%e],A=h&&w[(x-1)*N+(h-1)*e+f%e]),g=d+L-A,m=Math.abs(g-d),b=Math.abs(g-L),y=Math.abs(g-A),v=m<=b&&m<=y?d:b<=y?L:A,w[c++]=(u+v)%256;break;default:throw new Error("Invalid filter algorithm: "+t[r-1])}if(!I){var O=((a+x*l)*i.width+o)*e,E=x*N;for(f=0;f<C;f+=1){for(var R=0;R<e;R+=1)n[O++]=w[E++];O+=(s-1)*e}}x++}}return t=gn(t),1==i.interlaceMethod?(o(0,0,8,8),o(4,0,8,8),o(0,4,4,8),o(2,0,4,4),o(0,2,2,4),o(1,0,2,2),o(0,1,1,2)):o(0,0,1,1),n},r.prototype.decodePalette=function(){var t,e,n,r,i,o,a,s,l;for(n=this.palette,o=this.transparency.indexed||[],i=new Uint8Array((o.length||0)+n.length),r=0,t=0,e=a=0,s=n.length;a<s;e=a+=3)i[r++]=n[e],i[r++]=n[e+1],i[r++]=n[e+2],i[r++]=null!=(l=o[t++])?l:255;return i},r.prototype.copyToImageData=function(t,e){var n,r,i,o,a,s,l,u,c,h,f;if(r=this.colors,c=null,n=this.hasAlphaChannel,this.palette.length&&(c=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),r=4,n=!0),u=(i=t.data||t).length,a=c||e,o=s=0,1===r)for(;o<u;)l=c?4*e[o/4]:s,h=a[l++],i[o++]=h,i[o++]=h,i[o++]=h,i[o++]=n?a[l++]:255,s=l;else for(;o<u;)l=c?4*e[o/4]:s,i[o++]=a[l++],i[o++]=a[l++],i[o++]=a[l++],i[o++]=n?a[l++]:255,s=l},r.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};var i=function(){if("[object Window]"===Object.prototype.toString.call(vn)){try{e=vn.document.createElement("canvas"),n=e.getContext("2d")}catch(t){return!1}return!0}return!1};return i(),t=function(t){var r;if(!0===i())return n.width=t.width,n.height=t.height,n.clearRect(0,0,t.width,t.height),n.putImageData(t,0,0),(r=new Image).src=e.toDataURL(),r;throw new Error("This method requires a Browser with Canvas-capability.")},r.prototype.decodeFrames=function(e){var n,r,i,o,a,s,l,u;if(this.animation){for(u=[],r=a=0,s=(l=this.animation.frames).length;a<s;r=++a)n=l[r],i=e.createImageData(n.width,n.height),o=this.decodePixels(new Uint8Array(n.data)),this.copyToImageData(i,o),n.imageData=i,u.push(n.image=t(i));return u}},r.prototype.renderFrame=function(t,e){var n,r,i;return n=(r=this.animation.frames)[e],i=r[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===n.blendOp&&t.clearRect(n.xOffset,n.yOffset,n.width,n.height),t.drawImage(n.image,n.xOffset,n.yOffset)},r.prototype.animate=function(t){var e,n,r,i,o,a,s=this;return n=0,a=this.animation,i=a.numFrames,r=a.frames,o=a.numPlays,(e=function(){var a,l;if(a=n++%i,l=r[a],s.renderFrame(t,a),i>1&&n/i<o)return s.animation._timeout=setTimeout(e,l.delay)})()},r.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},r.prototype.render=function(t){var e,n;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(n=e.createImageData(this.width,this.height),this.copyToImageData(n,this.decodePixels()),e.putImageData(n,0,0))},r}();
/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */
/**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function ci(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw new Error("Invalid GIF 87a/89a header.");var n=t[e++]|t[e++]<<8,r=t[e++]|t[e++]<<8,i=t[e++],o=i>>7,a=1<<1+(7&i);t[e++],t[e++];var s=null,l=null;o&&(s=e,l=a,e+=3*a);var u=!0,c=[],h=0,f=null,d=0,p=null;for(this.width=n,this.height=r;u&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,p=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((P=t[e++])>=0))throw Error("Invalid block size");if(0===P)break;e+=P}break;case 249:if(4!==t[e++]||0!==t[e+4])throw new Error("Invalid graphics extension block.");var g=t[e++];h=t[e++]|t[e++]<<8,f=t[e++],!(1&g)&&(f=null),d=g>>2&7,e++;break;case 254:for(;;){if(!((P=t[e++])>=0))throw Error("Invalid block size");if(0===P)break;e+=P}break;default:throw new Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var m=t[e++]|t[e++]<<8,v=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,w=t[e++],x=w>>6&1,N=1<<1+(7&w),L=s,A=l,S=!1;w>>7&&(S=!0,L=e,A=N,e+=3*N);var _=e;for(e++;;){var P;if(!((P=t[e++])>=0))throw Error("Invalid block size");if(0===P)break;e+=P}c.push({x:m,y:v,width:b,height:y,has_local_palette:S,palette_offset:L,palette_size:A,data_offset:_,data_length:e-_,transparent_index:f,interlaced:!!x,delay:h,disposal:d});break;case 59:u=!1;break;default:throw new Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return c.length},this.loopCount=function(){return p},this.frameInfo=function(t){if(t<0||t>=c.length)throw new Error("Frame index out of range.");return c[t]},this.decodeAndBlitFrameBGRA=function(e,r){var i=this.frameInfo(e),o=i.width*i.height,a=new Uint8Array(o);hi(t,i.data_offset,a,o);var s=i.palette_offset,l=i.transparent_index;null===l&&(l=256);var u=i.width,c=n-u,h=u,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,g=4*c;!0===i.interlaced&&(g+=4*n*7);for(var m=8,v=0,b=a.length;v<b;++v){var y=a[v];if(0===h&&(h=u,(p+=g)>=d&&(g=4*c+4*n*(m-1),p=f+(u+c)*(m<<1),m>>=1)),y===l)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],N=t[s+3*y+2];r[p++]=N,r[p++]=x,r[p++]=w,r[p++]=255}--h}},this.decodeAndBlitFrameRGBA=function(e,r){var i=this.frameInfo(e),o=i.width*i.height,a=new Uint8Array(o);hi(t,i.data_offset,a,o);var s=i.palette_offset,l=i.transparent_index;null===l&&(l=256);var u=i.width,c=n-u,h=u,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,g=4*c;!0===i.interlaced&&(g+=4*n*7);for(var m=8,v=0,b=a.length;v<b;++v){var y=a[v];if(0===h&&(h=u,(p+=g)>=d&&(g=4*c+4*n*(m-1),p=f+(u+c)*(m<<1),m>>=1)),y===l)p+=4;else{var w=t[s+3*y],x=t[s+3*y+1],N=t[s+3*y+2];r[p++]=w,r[p++]=x,r[p++]=N,r[p++]=255}--h}}}function hi(t,e,n,r){for(var i=t[e++],o=1<<i,a=o+1,s=a+1,l=i+1,u=(1<<l)-1,c=0,h=0,f=0,d=t[e++],p=new Int32Array(4096),g=null;;){for(;c<16&&0!==d;)h|=t[e++]<<c,c+=8,1===d?d=t[e++]:--d;if(c<l)break;var m=h&u;if(h>>=l,c-=l,m!==o){if(m===a)break;for(var v=m<s?m:g,b=0,y=v;y>o;)y=p[y]>>8,++b;var w=y;if(f+b+(v!==m?1:0)>r)return void yn.log("Warning, gif stream longer than expected.");n[f++]=w;var x=f+=b;for(v!==m&&(n[f++]=w),y=v;b--;)y=p[y],n[--x]=255&y,y>>=8;null!==g&&s<4096&&(p[s++]=g<<8|w,s>=u+1&&l<12&&(++l,u=u<<1|1)),g=m}else s=a+1,u=(1<<(l=i+1))-1,g=null}return f!==r&&yn.log("Warning, gif stream shorter than expected."),n
/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/}function fi(t){var e,n,r,i,o,a=Math.floor,s=new Array(64),l=new Array(64),u=new Array(64),c=new Array(64),h=new Array(65535),f=new Array(65535),d=new Array(64),p=new Array(64),g=[],m=0,v=7,b=new Array(64),y=new Array(64),w=new Array(64),x=new Array(256),N=new Array(2048),L=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],A=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],S=[0,1,2,3,4,5,6,7,8,9,10,11],_=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],F=[0,1,2,3,4,5,6,7,8,9,10,11],C=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],j=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function I(t,e){for(var n=0,r=0,i=new Array,o=1;o<=16;o++){for(var a=1;a<=t[o];a++)i[e[r]]=[],i[e[r]][0]=n,i[e[r]][1]=o,r++,n++;n*=2}return i}function O(t){for(var e=t[0],n=t[1]-1;n>=0;)e&1<<n&&(m|=1<<v),n--,--v<0&&(255==m?(E(255),E(0)):E(m),v=7,m=0)}function E(t){g.push(t)}function R(t){E(t>>8&255),E(255&t)}function B(t,e,n,r,i){for(var o,a=i[0],s=i[240],l=function(t,e){var n,r,i,o,a,s,l,u,c,h,f=0;for(c=0;c<8;++c){n=t[f],r=t[f+1],i=t[f+2],o=t[f+3],a=t[f+4],s=t[f+5],l=t[f+6];var p=n+(u=t[f+7]),g=n-u,m=r+l,v=r-l,b=i+s,y=i-s,w=o+a,x=o-a,N=p+w,L=p-w,A=m+b,S=m-b;t[f]=N+A,t[f+4]=N-A;var _=.707106781*(S+L);t[f+2]=L+_,t[f+6]=L-_;var P=.382683433*((N=x+y)-(S=v+g)),k=.5411961*N+P,F=1.306562965*S+P,C=.707106781*(A=y+v),j=g+C,I=g-C;t[f+5]=I+k,t[f+3]=I-k,t[f+1]=j+F,t[f+7]=j-F,f+=8}for(f=0,c=0;c<8;++c){n=t[f],r=t[f+8],i=t[f+16],o=t[f+24],a=t[f+32],s=t[f+40],l=t[f+48];var O=n+(u=t[f+56]),E=n-u,R=r+l,B=r-l,D=i+s,T=i-s,M=o+a,q=o-a,U=O+M,z=O-M,W=R+D,H=R-D;t[f]=U+W,t[f+32]=U-W;var V=.707106781*(H+z);t[f+16]=z+V,t[f+48]=z-V;var G=.382683433*((U=q+T)-(H=B+E)),J=.5411961*U+G,Y=1.306562965*H+G,K=.707106781*(W=T+B),X=E+K,$=E-K;t[f+40]=$+J,t[f+24]=$-J,t[f+8]=X+Y,t[f+56]=X-Y,f++}for(c=0;c<64;++c)h=t[c]*e[c],d[c]=h>0?h+.5|0:h-.5|0;return d}(t,e),u=0;u<64;++u)p[L[u]]=l[u];var c=p[0]-n;n=p[0],0==c?O(r[0]):(O(r[f[o=32767+c]]),O(h[o]));for(var g=63;g>0&&0==p[g];)g--;if(0==g)return O(a),n;for(var m,v=1;v<=g;){for(var b=v;0==p[v]&&v<=g;)++v;var y=v-b;if(y>=16){m=y>>4;for(var w=1;w<=m;++w)O(s);y&=15}o=32767+p[v],O(i[(y<<4)+f[o]]),O(h[o]),v++}return 63!=g&&O(a),n}function D(t){t=Math.min(Math.max(t,1),100),o!=t&&(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=a((e[n]*t+50)/100);r=Math.min(Math.max(r,1),255),s[L[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var h=a((i[o]*t+50)/100);h=Math.min(Math.max(h,1),255),l[L[o]]=h}for(var f=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var g=0;g<8;g++)u[d]=1/(s[L[d]]*f[p]*f[g]*8),c[d]=1/(l[L[d]]*f[p]*f[g]*8),d++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),o=t)}this.encode=function(t,o){var a,h;o&&D(o),g=new Array,m=0,v=7,R(65496),R(65504),R(16),E(74),E(70),E(73),E(70),E(0),E(1),E(1),E(0),R(1),R(1),E(0),E(0),function(){R(65499),R(132),E(0);for(var t=0;t<64;t++)E(s[t]);E(1);for(var e=0;e<64;e++)E(l[e])}(),a=t.width,h=t.height,R(65472),R(17),E(8),R(h),R(a),E(3),E(1),E(17),E(0),E(2),E(17),E(1),E(3),E(17),E(1),function(){R(65476),R(418),E(0);for(var t=0;t<16;t++)E(A[t+1]);for(var e=0;e<=11;e++)E(S[e]);E(16);for(var n=0;n<16;n++)E(_[n+1]);for(var r=0;r<=161;r++)E(P[r]);E(1);for(var i=0;i<16;i++)E(k[i+1]);for(var o=0;o<=11;o++)E(F[o]);E(17);for(var a=0;a<16;a++)E(C[a+1]);for(var s=0;s<=161;s++)E(j[s])}(),R(65498),R(12),E(3),E(1),E(0),E(2),E(17),E(3),E(17),E(0),E(63),E(0);var f=0,d=0,p=0;m=0,v=7,this.encode.displayName="_encode_";for(var x,L,I,T,M,q,U,z,W,H=t.data,V=t.width,G=t.height,J=4*V,Y=0;Y<G;){for(x=0;x<J;){for(M=J*Y+x,U=-1,z=0,W=0;W<64;W++)q=M+(z=W>>3)*J+(U=4*(7&W)),Y+z>=G&&(q-=J*(Y+1+z-G)),x+U>=J&&(q-=x+U-J+4),L=H[q++],I=H[q++],T=H[q++],b[W]=(N[L]+N[I+256|0]+N[T+512|0]>>16)-128,y[W]=(N[L+768|0]+N[I+1024|0]+N[T+1280|0]>>16)-128,w[W]=(N[L+1280|0]+N[I+1536|0]+N[T+1792|0]>>16)-128;f=B(b,u,f,e,r),d=B(y,c,d,n,i),p=B(w,c,p,n,i),x+=32}Y+=8}if(v>=0){var K=[];K[1]=v+1,K[0]=(1<<v+1)-1,O(K)}return R(65497),new Uint8Array(g)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)x[e]=t(e)}(),e=I(A,S),n=I(k,F),r=I(_,P),i=I(C,j),function(){for(var t=1,e=2,n=1;n<=15;n++){for(var r=t;r<e;r++)f[32767+r]=n,h[32767+r]=[],h[32767+r][1]=n,h[32767+r][0]=r;for(var i=-(e-1);i<=-t;i++)f[32767+i]=n,h[32767+i]=[],h[32767+i][1]=n,h[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)N[t]=19595*t,N[t+256|0]=38470*t,N[t+512|0]=7471*t+32768,N[t+768|0]=-11059*t,N[t+1024|0]=-21709*t,N[t+1280|0]=32768*t+8421375,N[t+1536|0]=-27439*t,N[t+1792|0]=-5329*t}(),D(t)}
/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function di(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function pi(t){function e(t){if(!t)throw Error("assert :P")}function n(t,e,n){for(var r=0;4>r;r++)if(t[e+r]!=n.charCodeAt(r))return!0;return!1}function r(t,e,n,r,i){for(var o=0;o<i;o++)t[e+o]=n[r+o]}function i(t,e,n,r){for(var i=0;i<r;i++)t[e+i]=n}function o(t){return new Int32Array(t)}function a(t,e){for(var n=[],r=0;r<t;r++)n.push(new e);return n}function s(t,e){var n=[];return function t(n,r,i){for(var o=i[r],a=0;a<o&&(n.push(i.length>r+1?[]:new e),!(i.length<r+1));a++)t(n[a],r+1,i)}(n,0,t),n}var l=function(){var t=this;function l(t,e){for(var n=1<<e-1>>>0;t&n;)n>>>=1;return n?(t&n-1)+n:t}function u(t,n,r,i,o){e(!(i%r));do{t[n+(i-=r)]=o}while(0<i)}function c(t,n,r,i,a){if(e(2328>=a),512>=a)var s=o(512);else if(null==(s=o(a)))return 0;return function(t,n,r,i,a,s){var c,f,d=n,p=1<<r,g=o(16),m=o(16);for(e(0!=a),e(null!=i),e(null!=t),e(0<r),f=0;f<a;++f){if(15<i[f])return 0;++g[i[f]]}if(g[0]==a)return 0;for(m[1]=0,c=1;15>c;++c){if(g[c]>1<<c)return 0;m[c+1]=m[c]+g[c]}for(f=0;f<a;++f)c=i[f],0<i[f]&&(s[m[c]++]=f);if(1==m[15])return(i=new h).g=0,i.value=s[0],u(t,d,1,p,i),p;var v,b=-1,y=p-1,w=0,x=1,N=1,L=1<<r;for(f=0,c=1,a=2;c<=r;++c,a<<=1){if(x+=N<<=1,0>(N-=g[c]))return 0;for(;0<g[c];--g[c])(i=new h).g=c,i.value=s[f++],u(t,d+w,a,L,i),w=l(w,c)}for(c=r+1,a=2;15>=c;++c,a<<=1){if(x+=N<<=1,0>(N-=g[c]))return 0;for(;0<g[c];--g[c]){if(i=new h,(w&y)!=b){for(d+=L,v=1<<(b=c)-r;15>b&&!(0>=(v-=g[b]));)++b,v<<=1;p+=L=1<<(v=b-r),t[n+(b=w&y)].g=v+r,t[n+b].value=d-n-b}i.g=c-r,i.value=s[f++],u(t,d+(w>>r),a,L,i),w=l(w,c)}}return x!=2*m[15]-1?0:p}(t,n,r,i,a,s)}function h(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=a(5,h),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=a(Tn,f)}function p(t,n,r,i){e(null!=t),e(null!=n),e(2147483648>i),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=n,t.pa=r,t.Jd=n,t.Yc=r+i,t.Zc=4<=i?r+i-4+1:r,_(t)}function g(t,e){for(var n=0;0<e--;)n|=k(t,128)<<e;return n}function m(t,e){var n=g(t,e);return P(t)?-n:n}function v(t,n,r,i){var o,a=0;for(e(null!=t),e(null!=n),e(4294967288>i),t.Sb=i,t.Ra=0,t.u=0,t.h=0,4<i&&(i=4),o=0;o<i;++o)a+=n[r+o]<<8*o;t.Ra=a,t.bb=i,t.oa=n,t.pa=r}function b(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<Un-8>>>0,++t.bb,t.u-=8;L(t)&&(t.h=1,t.u=0)}function y(t,n){if(e(0<=n),!t.h&&n<=qn){var r=N(t)&Mn[n];return t.u+=n,b(t),r}return t.h=1,t.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function x(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function N(t){return t.Ra>>>(t.u&Un-1)>>>0}function L(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>Un}function A(t,e){t.u=e,t.h=L(t)}function S(t){t.u>=zn&&(e(t.u>=zn),b(t))}function _(t){e(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function P(t){return g(t,1)}function k(t,e){var n=t.Ca;0>t.b&&_(t);var r=t.b,i=n*e>>>8,o=(t.I>>>r>i)+0;for(o?(n-=i,t.I-=i+1<<r>>>0):n=i+1,r=n,i=0;256<=r;)i+=8,r>>=8;return r=7^i+Wn[r],t.b-=r,t.Ca=(n<<r)-1,o}function F(t,e,n){t[e+0]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=255&n}function C(t,e){return t[e+0]|t[e+1]<<8}function j(t,e){return C(t,e)|t[e+2]<<16}function I(t,e){return C(t,e)|C(t,e+2)<<16}function O(t,n){var r=1<<n;return e(null!=t),e(0<n),t.X=o(r),null==t.X?0:(t.Mb=32-n,t.Xa=n,1)}function E(t,n){e(null!=t),e(null!=n),e(t.Xa==n.Xa),r(n.X,0,t.X,0,1<<n.Xa)}function R(){this.X=[],this.Xa=this.Mb=0}function B(t,n,r,i){e(null!=r),e(null!=i);var o=r[0],a=i[0];return 0==o&&(o=(t*a+n/2)/n),0==a&&(a=(n*o+t/2)/t),0>=o||0>=a?0:(r[0]=o,i[0]=a,1)}function D(t,e){return t+(1<<e)-1>>>e}function T(t,e){return((4278255360&t)+(4278255360&e)>>>0&4278255360)+((16711935&t)+(16711935&e)>>>0&16711935)>>>0}function M(e,n){t[n]=function(n,r,i,o,a,s,l){var u;for(u=0;u<a;++u){var c=t[e](s[l+u-1],i,o+u);s[l+u]=T(n[r+u],c)}}}function q(){this.ud=this.hd=this.jd=0}function U(t,e){return((4278124286&(t^e))>>>1)+(t&e)>>>0}function z(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function W(t,e){return z(t+(t-e+.5>>1))}function H(t,e,n){return Math.abs(e-n)-Math.abs(t-n)}function V(t,e,n,r,i,o,a){for(r=o[a-1],n=0;n<i;++n)o[a+n]=r=T(t[e+n],r)}function G(t,e,n,r,i){var o;for(o=0;o<n;++o){var a=t[e+o],s=a>>8&255,l=16711935&(l=(l=16711935&a)+((s<<16)+s));r[i+o]=(4278255360&a)+l>>>0}}function J(t,e){e.jd=255&t,e.hd=t>>8&255,e.ud=t>>16&255}function Y(t,e,n,r,i,o){var a;for(a=0;a<r;++a){var s=e[n+a],l=s>>>8,u=s,c=255&(c=(c=s>>>16)+((t.jd<<24>>24)*(l<<24>>24)>>>5));u=255&(u=(u+=(t.hd<<24>>24)*(l<<24>>24)>>>5)+((t.ud<<24>>24)*(c<<24>>24)>>>5)),i[o+a]=(4278255360&s)+(c<<16)+u}}function K(e,n,r,i,o){t[n]=function(t,e,n,r,a,s,l,u,c){for(r=l;r<u;++r)for(l=0;l<c;++l)a[s++]=o(n[i(t[e++])])},t[e]=function(e,n,a,s,l,u,c){var h=8>>e.b,f=e.Ea,d=e.K[0],p=e.w;if(8>h)for(e=(1<<e.b)-1,p=(1<<h)-1;n<a;++n){var g,m=0;for(g=0;g<f;++g)g&e||(m=i(s[l++])),u[c++]=o(d[m&p]),m>>=h}else t["VP8LMapColor"+r](s,l,d,p,u,c,n,a,f)}}function X(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=o>>16&255,r[i++]=o>>8&255,r[i++]=255&o}}function $(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=o>>16&255,r[i++]=o>>8&255,r[i++]=255&o,r[i++]=o>>24&255}}function Z(t,e,n,r,i){for(n=e+n;e<n;){var o=(a=t[e++])>>16&240|a>>12&15,a=240&a|a>>28&15;r[i++]=o,r[i++]=a}}function Q(t,e,n,r,i){for(n=e+n;e<n;){var o=(a=t[e++])>>16&248|a>>13&7,a=a>>5&224|a>>3&31;r[i++]=o,r[i++]=a}}function tt(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=255&o,r[i++]=o>>8&255,r[i++]=o>>16&255}}function et(t,e,n,i,o,a){if(0==a)for(n=e+n;e<n;)F(i,((a=t[e++])[0]>>24|a[1]>>8&65280|a[2]<<8&16711680|a[3]<<24)>>>0),o+=32;else r(i,o,t,e,n)}function nt(e,n){t[n][0]=t[e+"0"],t[n][1]=t[e+"1"],t[n][2]=t[e+"2"],t[n][3]=t[e+"3"],t[n][4]=t[e+"4"],t[n][5]=t[e+"5"],t[n][6]=t[e+"6"],t[n][7]=t[e+"7"],t[n][8]=t[e+"8"],t[n][9]=t[e+"9"],t[n][10]=t[e+"10"],t[n][11]=t[e+"11"],t[n][12]=t[e+"12"],t[n][13]=t[e+"13"],t[n][14]=t[e+"0"],t[n][15]=t[e+"0"]}function rt(t){return t==zr||t==Wr||t==Hr||t==Vr}function it(){this.eb=[],this.size=this.A=this.fb=0}function ot(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function at(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new it,this.f.kb=new ot,this.sd=null}function st(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function lt(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function ut(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function ct(t,e){var n=t.T,i=e.ba.f.RGBA,o=i.eb,a=i.fb+t.ka*i.A,s=mi[e.ba.S],l=t.y,u=t.O,c=t.f,h=t.N,f=t.ea,d=t.W,p=e.cc,g=e.dc,m=e.Mc,v=e.Nc,b=t.ka,y=t.ka+t.T,w=t.U,x=w+1>>1;for(0==b?s(l,u,null,null,c,h,f,d,c,h,f,d,o,a,null,null,w):(s(e.ec,e.fc,l,u,p,g,m,v,c,h,f,d,o,a-i.A,o,a,w),++n);b+2<y;b+=2)p=c,g=h,m=f,v=d,h+=t.Rc,d+=t.Rc,a+=2*i.A,s(l,(u+=2*t.fa)-t.fa,l,u,p,g,m,v,c,h,f,d,o,a-i.A,o,a,w);return u+=t.fa,t.j+y<t.o?(r(e.ec,e.fc,l,u,w),r(e.cc,e.dc,c,h,x),r(e.Mc,e.Nc,f,d,x),n--):1&y||s(l,u,null,null,c,h,f,d,c,h,f,d,o,a+i.A,null,null,w),n}function ht(t,n,r){var i=t.F,o=[t.J];if(null!=i){var a=t.U,s=n.ba.S,l=s==Mr||s==Hr;n=n.ba.f.RGBA;var u=[0],c=t.ka;u[0]=t.T,t.Kb&&(0==c?--u[0]:(--c,o[0]-=t.width),t.j+t.ka+t.T==t.o&&(u[0]=t.o-t.j-c));var h=n.eb;c=n.fb+c*n.A,t=Ar(i,o[0],t.width,a,u,h,c+(l?0:3),n.A),e(r==u),t&&rt(s)&&Nr(h,c,l,a,u,n.A)}return 0}function ft(t){var e=t.ma,n=e.ba.S,r=11>n,i=n==Br||n==Tr||n==Mr||n==qr||12==n||rt(n);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!Rn(e.Oa,t,i?11:12))return 0;if(i&&rt(n)&&bn(),t.da)alert("todo:use_scaling");else{if(r){if(e.Ib=ut,t.Kb){if(n=t.U+1>>1,e.memory=o(t.U+2*n),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+n,e.Ib=ct,bn()}}else alert("todo:EmitYUV");i&&(e.Jb=ht,r&&mn())}if(r&&!Fi){for(t=0;256>t;++t)Ci[t]=89858*(t-128)+Si>>Ai,Oi[t]=-22014*(t-128)+Si,Ii[t]=-45773*(t-128),ji[t]=113618*(t-128)+Si>>Ai;for(t=_i;t<Pi;++t)e=76283*(t-16)+Si>>Ai,Ei[t-_i]=Vt(e,255),Ri[t-_i]=Vt(e+8>>4,15);Fi=1}return 1}function dt(t){var n=t.ma,r=t.U,i=t.T;return e(!(1&t.ka)),0>=r||0>=i?0:(r=n.Ib(t,n),null!=n.Jb&&n.Jb(t,n,r),n.Dc+=r,1)}function pt(t){t.ma.memory=null}function gt(t,e,n,r){return 47!=y(t,8)?0:(e[0]=y(t,14)+1,n[0]=y(t,14)+1,r[0]=y(t,1),0!=y(t,3)?0:!t.h)}function mt(t,e){if(4>t)return t+1;var n=t-2>>1;return(2+(1&t)<<n)+y(e,n)+1}function vt(t,e){return 120<e?e-120:1<=(n=((n=$r[e-1])>>4)*t+(8-(15&n)))?n:1;var n}function bt(t,e,n){var r=N(n),i=t[e+=255&r].g-8;return 0<i&&(A(n,n.u+8),r=N(n),e+=t[e].value,e+=r&(1<<i)-1),A(n,n.u+t[e].g),t[e].value}function yt(t,n,r){return r.g+=t.g,r.value+=t.value<<n>>>0,e(8>=r.g),t.g}function wt(t,n,r){var i=t.xc;return e((n=0==i?0:t.vc[t.md*(r>>i)+(n>>i)])<t.Wb),t.Ya[n]}function xt(t,n,i,o){var a=t.ab,s=t.c*n,l=t.C;n=l+n;var u=i,c=o;for(o=t.Ta,i=t.Ua;0<a--;){var h=t.gc[a],f=l,d=n,p=u,g=c,m=(c=o,u=i,h.Ea);switch(e(f<d),e(d<=h.nc),h.hc){case 2:Gn(p,g,(d-f)*m,c,u);break;case 0:var v=f,b=d,y=c,w=u,x=(_=h).Ea;0==v&&(Hn(p,g,null,null,1,y,w),V(p,g+1,0,0,x-1,y,w+1),g+=x,w+=x,++v);for(var N=1<<_.b,L=N-1,A=D(x,_.b),S=_.K,_=_.w+(v>>_.b)*A;v<b;){var P=S,k=_,F=1;for(Vn(p,g,y,w-x,1,y,w);F<x;){var C=(F&~L)+N;C>x&&(C=x),(0,$n[P[k++]>>8&15])(p,g+ +F,y,w+F-x,C-F,y,w+F),F=C}g+=x,w+=x,++v&L||(_+=A)}d!=h.nc&&r(c,u-m,c,u+(d-f-1)*m,m);break;case 1:for(m=p,b=g,x=(p=h.Ea)-(w=p&~(y=(g=1<<h.b)-1)),v=D(p,h.b),N=h.K,h=h.w+(f>>h.b)*v;f<d;){for(L=N,A=h,S=new q,_=b+w,P=b+p;b<_;)J(L[A++],S),Zn(S,m,b,g,c,u),b+=g,u+=g;b<P&&(J(L[A++],S),Zn(S,m,b,x,c,u),b+=x,u+=x),++f&y||(h+=v)}break;case 3:if(p==c&&g==u&&0<h.b){for(b=c,p=m=u+(d-f)*m-(w=(d-f)*D(h.Ea,h.b)),g=c,y=u,v=[],w=(x=w)-1;0<=w;--w)v[w]=g[y+w];for(w=x-1;0<=w;--w)b[p+w]=v[w];Jn(h,f,d,c,m,c,u)}else Jn(h,f,d,p,g,c,u)}u=o,c=i}c!=i&&r(o,i,u,c,s)}function Nt(t,n){var r=t.V,i=t.Ba+t.c*t.C,o=n-t.C;if(e(n<=t.l.o),e(16>=o),0<o){var a=t.l,s=t.Ta,l=t.Ua,u=a.width;if(xt(t,o,r,i),o=l=[l],e((r=t.C)<(i=n)),e(a.v<a.va),i>a.o&&(i=a.o),r<a.j){var c=a.j-r;r=a.j,o[0]+=c*u}if(r>=i?r=0:(o[0]+=4*a.v,a.ka=r-a.j,a.U=a.va-a.v,a.T=i-r,r=1),r){if(l=l[0],11>(r=t.ca).S){var h=r.f.RGBA,f=(i=r.S,o=a.U,a=a.T,c=h.eb,h.A),d=a;for(h=h.fb+t.Ma*h.A;0<d--;){var p=s,g=l,m=o,v=c,b=h;switch(i){case Rr:Qn(p,g,m,v,b);break;case Br:tr(p,g,m,v,b);break;case zr:tr(p,g,m,v,b),Nr(v,b,0,m,1,0);break;case Dr:rr(p,g,m,v,b);break;case Tr:et(p,g,m,v,b,1);break;case Wr:et(p,g,m,v,b,1),Nr(v,b,0,m,1,0);break;case Mr:et(p,g,m,v,b,0);break;case Hr:et(p,g,m,v,b,0),Nr(v,b,1,m,1,0);break;case qr:er(p,g,m,v,b);break;case Vr:er(p,g,m,v,b),Lr(v,b,m,1,0);break;case Ur:nr(p,g,m,v,b);break;default:e(0)}l+=u,h+=f}t.Ma+=a}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=r.height)}}t.C=n,e(t.C<=t.i)}function Lt(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var n=t.Ya[e].G,r=t.Ya[e].H;if(0<n[1][r[1]+0].g||0<n[2][r[2]+0].g||0<n[3][r[3]+0].g)return 0}return 1}function At(t,n,r,i,o,a){if(0!=t.Z){var s=t.qd,l=t.rd;for(e(null!=gi[t.Z]);n<r;++n)gi[t.Z](s,l,i,o,i,o,a),s=i,l=o,o+=a;t.qd=s,t.rd=l}}function St(t,n){var r=t.l.ma,i=0==r.Z||1==r.Z?t.l.j:t.C;if(i=t.C<i?i:t.C,e(n<=t.l.o),n>i){var o=t.l.width,a=r.ca,s=r.tb+o*i,l=t.V,u=t.Ba+t.c*i,c=t.gc;e(1==t.ab),e(3==c[0].hc),Kn(c[0],i,n,l,u,a,s),At(r,i,n,a,s,o)}t.C=t.Ma=n}function _t(t,n,r,i,o,a,s){var l=t.$/i,u=t.$%i,c=t.m,h=t.s,f=r+t.$,d=f;o=r+i*o;var p=r+i*a,g=280+h.ua,m=t.Pb?l:16777216,v=0<h.ua?h.Wa:null,b=h.wc,y=f<p?wt(h,u,l):null;e(t.C<a),e(p<=o);var w=!1;t:for(;;){for(;w||f<p;){var x=0;if(l>=m){var _=f-r;e((m=t).Pb),m.wd=m.m,m.xd=_,0<m.s.ua&&E(m.s.Wa,m.s.vb),m=l+Qr}if(u&b||(y=wt(h,u,l)),e(null!=y),y.Qb&&(n[f]=y.qb,w=!0),!w)if(S(c),y.jc){x=c,_=n;var P=f,k=y.pd[N(x)&Tn-1];e(y.jc),256>k.g?(A(x,x.u+k.g),_[P]=k.value,x=0):(A(x,x.u+k.g-256),e(256<=k.value),x=k.value),0==x&&(w=!0)}else x=bt(y.G[0],y.H[0],c);if(c.h)break;if(w||256>x){if(!w)if(y.nd)n[f]=(y.qb|x<<8)>>>0;else{if(S(c),w=bt(y.G[1],y.H[1],c),S(c),_=bt(y.G[2],y.H[2],c),P=bt(y.G[3],y.H[3],c),c.h)break;n[f]=(P<<24|w<<16|x<<8|_)>>>0}if(w=!1,++f,++u>=i&&(u=0,++l,null!=s&&l<=a&&!(l%16)&&s(t,l),null!=v))for(;d<f;)x=n[d++],v.X[(506832829*x&**********)>>>v.Mb]=x}else if(280>x){if(x=mt(x-256,c),_=bt(y.G[4],y.H[4],c),S(c),_=vt(i,_=mt(_,c)),c.h)break;if(f-r<_||o-f<x)break t;for(P=0;P<x;++P)n[f+P]=n[f+P-_];for(f+=x,u+=x;u>=i;)u-=i,++l,null!=s&&l<=a&&!(l%16)&&s(t,l);if(e(f<=o),u&b&&(y=wt(h,u,l)),null!=v)for(;d<f;)x=n[d++],v.X[(506832829*x&**********)>>>v.Mb]=x}else{if(!(x<g))break t;for(w=x-280,e(null!=v);d<f;)x=n[d++],v.X[(506832829*x&**********)>>>v.Mb]=x;x=f,e(!(w>>>(_=v).Xa)),n[x]=_.X[w],w=!0}w||e(c.h==L(c))}if(t.Pb&&c.h&&f<o)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&E(t.s.vb,t.s.Wa);else{if(c.h)break t;null!=s&&s(t,l>a?a:l),t.a=0,t.$=f-r}return 1}return t.a=3,0}function Pt(t){e(null!=t),t.vc=null,t.yc=null,t.Ya=null;var n=t.Wa;null!=n&&(n.X=null),t.vb=null,e(null!=t)}function kt(){var e=new an;return null==e?null:(e.a=0,e.xb=pi,nt("Predictor","VP8LPredictors"),nt("Predictor","VP8LPredictors_C"),nt("PredictorAdd","VP8LPredictorsAdd"),nt("PredictorAdd","VP8LPredictorsAdd_C"),Gn=G,Zn=Y,Qn=X,tr=$,er=Z,nr=Q,rr=tt,t.VP8LMapColor32b=Yn,t.VP8LMapColor8b=Xn,e)}function Ft(t,n,r,s,l){var u=1,f=[t],p=[n],g=s.m,m=s.s,v=null,b=0;t:for(;;){if(r)for(;u&&y(g,1);){var w=f,x=p,L=s,_=1,P=L.m,k=L.gc[L.ab],F=y(P,2);if(L.Oc&1<<F)u=0;else{switch(L.Oc|=1<<F,k.hc=F,k.Ea=w[0],k.nc=x[0],k.K=[null],++L.ab,e(4>=L.ab),F){case 0:case 1:k.b=y(P,3)+2,_=Ft(D(k.Ea,k.b),D(k.nc,k.b),0,L,k.K),k.K=k.K[0];break;case 3:var C,j=y(P,8)+1,I=16<j?0:4<j?1:2<j?2:3;if(w[0]=D(k.Ea,I),k.b=I,C=_=Ft(j,1,0,L,k.K)){var E,R=j,B=k,M=1<<(8>>B.b),q=o(M);if(null==q)C=0;else{var U=B.K[0],z=B.w;for(q[0]=B.K[0][0],E=1;E<1*R;++E)q[E]=T(U[z+E],q[E-1]);for(;E<4*M;++E)q[E]=0;B.K[0]=null,B.K[0]=q,C=1}}_=C;break;case 2:break;default:e(0)}u=_}}if(f=f[0],p=p[0],u&&y(g,1)&&!(u=1<=(b=y(g,4))&&11>=b)){s.a=3;break t}var W;if(W=u)e:{var H,V,G,J=s,Y=f,K=p,X=b,$=r,Z=J.m,Q=J.s,tt=[null],et=1,nt=0,rt=Zr[X];n:for(;;){if($&&y(Z,1)){var it=y(Z,3)+2,ot=D(Y,it),at=D(K,it),st=ot*at;if(!Ft(ot,at,0,J,tt))break n;for(tt=tt[0],Q.xc=it,H=0;H<st;++H){var lt=tt[H]>>8&65535;tt[H]=lt,lt>=et&&(et=lt+1)}}if(Z.h)break n;for(V=0;5>V;++V){var ut=Yr[V];!V&&0<X&&(ut+=1<<X),nt<ut&&(nt=ut)}var ct=a(et*rt,h),ht=et,ft=a(ht,d);if(null==ft)var dt=null;else e(65536>=ht),dt=ft;var pt=o(nt);if(null==dt||null==pt||null==ct){J.a=1;break n}var gt=ct;for(H=G=0;H<et;++H){var mt=dt[H],vt=mt.G,bt=mt.H,wt=0,xt=1,Nt=0;for(V=0;5>V;++V){ut=Yr[V],vt[V]=gt,bt[V]=G,!V&&0<X&&(ut+=1<<X);r:{var Lt,At=ut,St=J,kt=pt,Ct=gt,jt=G,It=0,Ot=St.m,Et=y(Ot,1);if(i(kt,0,0,At),Et){var Rt=y(Ot,1)+1,Bt=y(Ot,1),Dt=y(Ot,0==Bt?1:8);kt[Dt]=1,2==Rt&&(kt[Dt=y(Ot,8)]=1);var Tt=1}else{var Mt=o(19),qt=y(Ot,4)+4;if(19<qt){St.a=3;var Ut=0;break r}for(Lt=0;Lt<qt;++Lt)Mt[Xr[Lt]]=y(Ot,3);var zt=void 0,Wt=void 0,Ht=St,Vt=Mt,Gt=At,Jt=kt,Yt=0,Kt=Ht.m,Xt=8,$t=a(128,h);i:for(;c($t,0,7,Vt,19);){if(y(Kt,1)){var Zt=2+2*y(Kt,3);if((zt=2+y(Kt,Zt))>Gt)break i}else zt=Gt;for(Wt=0;Wt<Gt&&zt--;){S(Kt);var Qt=$t[0+(127&N(Kt))];A(Kt,Kt.u+Qt.g);var te=Qt.value;if(16>te)Jt[Wt++]=te,0!=te&&(Xt=te);else{var ee=16==te,ne=te-16,re=Jr[ne],ie=y(Kt,Gr[ne])+re;if(Wt+ie>Gt)break i;for(var oe=ee?Xt:0;0<ie--;)Jt[Wt++]=oe}}Yt=1;break i}Yt||(Ht.a=3),Tt=Yt}(Tt=Tt&&!Ot.h)&&(It=c(Ct,jt,8,kt,At)),Tt&&0!=It?Ut=It:(St.a=3,Ut=0)}if(0==Ut)break n;if(xt&&1==Kr[V]&&(xt=0==gt[G].g),wt+=gt[G].g,G+=Ut,3>=V){var ae,se=pt[0];for(ae=1;ae<ut;++ae)pt[ae]>se&&(se=pt[ae]);Nt+=se}}if(mt.nd=xt,mt.Qb=0,xt&&(mt.qb=(vt[3][bt[3]+0].value<<24|vt[1][bt[1]+0].value<<16|vt[2][bt[2]+0].value)>>>0,0==wt&&256>vt[0][bt[0]+0].value&&(mt.Qb=1,mt.qb+=vt[0][bt[0]+0].value<<8)),mt.jc=!mt.Qb&&6>Nt,mt.jc){var le,ue=mt;for(le=0;le<Tn;++le){var ce=le,he=ue.pd[ce],fe=ue.G[0][ue.H[0]+ce];256<=fe.value?(he.g=fe.g+256,he.value=fe.value):(he.g=0,he.value=0,ce>>=yt(fe,8,he),ce>>=yt(ue.G[1][ue.H[1]+ce],16,he),ce>>=yt(ue.G[2][ue.H[2]+ce],0,he),yt(ue.G[3][ue.H[3]+ce],24,he))}}}Q.vc=tt,Q.Wb=et,Q.Ya=dt,Q.yc=ct,W=1;break e}W=0}if(!(u=W)){s.a=3;break t}if(0<b){if(m.ua=1<<b,!O(m.Wa,b)){s.a=1,u=0;break t}}else m.ua=0;var de=s,pe=f,ge=p,me=de.s,ve=me.xc;if(de.c=pe,de.i=ge,me.md=D(pe,ve),me.wc=0==ve?-1:(1<<ve)-1,r){s.xb=di;break t}if(null==(v=o(f*p))){s.a=1,u=0;break t}u=(u=_t(s,v,0,f,p,p,null))&&!g.h;break t}return u?(null!=l?l[0]=v:(e(null==v),e(r)),s.$=0,r||Pt(m)):Pt(m),u}function Ct(t,n){var r=t.c*t.i,i=r+n+16*n;return e(t.c<=n),t.V=o(i),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+r+n,1)}function jt(t,n){var r=t.C,i=n-r,o=t.V,a=t.Ba+t.c*r;for(e(n<=t.l.o);0<i;){var s=16<i?16:i,l=t.l.ma,u=t.l.width,c=u*s,h=l.ca,f=l.tb+u*r,d=t.Ta,p=t.Ua;xt(t,s,o,a),Sr(d,p,h,f,c),At(l,r,r+s,h,f,u),i-=s,o+=s*t.c,r+=s}e(r==n),t.C=t.Ma=n}function It(){this.ub=this.yd=this.td=this.Rb=0}function Ot(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Et(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function Rt(){var t;this.Yb=(function t(e,n,r){for(var i=r[n],o=0;o<i&&(e.push(r.length>n+1?[]:0),!(r.length<n+1));o++)t(e[o],n+1,r)}(t=[],0,[3,11]),t)}function Bt(){this.jb=o(3),this.Wc=s([4,8],Rt),this.Xc=s([4,17],Rt)}function Dt(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function Tt(){this.ld=this.La=this.dd=this.tc=0}function Mt(){this.Na=this.la=0}function qt(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Ut(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function zt(){this.uc=this.M=this.Nb=0,this.wa=Array(new Tt),this.Y=0,this.ya=Array(new Ut),this.aa=0,this.l=new Gt}function Wt(){this.y=o(16),this.f=o(8),this.ea=o(8)}function Ht(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new It,this.Kc=new Ot,this.ed=new Dt,this.Qa=new Et,this.Ic=this.$c=this.Aa=0,this.D=new zt,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=a(8,w),this.ia=0,this.pb=a(4,qt),this.Pa=new Bt,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Wt),this.Hd=0,this.rb=Array(new Mt),this.sb=0,this.wa=Array(new Tt),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Ut),this.L=this.aa=0,this.gd=s([4,2],Tt),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Vt(t,e){return 0>t?0:t>e?e:t}function Gt(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Jt(){var t=new Ht;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ni||(ni=$t)),t}function Yt(t,e,n){return 0==t.a&&(t.a=e,t.sc=n,t.cb=0),0}function Kt(t,e,n){return 3<=n&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function Xt(t,n){if(null==t)return 0;if(t.a=0,t.sc="OK",null==n)return Yt(t,2,"null VP8Io passed to VP8GetHeaders()");var r=n.data,o=n.w,a=n.ha;if(4>a)return Yt(t,7,"Truncated header.");var s=r[o+0]|r[o+1]<<8|r[o+2]<<16,l=t.Od;if(l.Rb=!(1&s),l.td=s>>1&7,l.yd=s>>4&1,l.ub=s>>5,3<l.td)return Yt(t,3,"Incorrect keyframe parameters.");if(!l.yd)return Yt(t,4,"Frame not displayable.");o+=3,a-=3;var u=t.Kc;if(l.Rb){if(7>a)return Yt(t,7,"cannot parse picture header");if(!Kt(r,o,a))return Yt(t,3,"Bad code word");u.c=16383&(r[o+4]<<8|r[o+3]),u.Td=r[o+4]>>6,u.i=16383&(r[o+6]<<8|r[o+5]),u.Ud=r[o+6]>>6,o+=7,a-=7,t.za=u.c+15>>4,t.Ub=u.i+15>>4,n.width=u.c,n.height=u.i,n.Da=0,n.j=0,n.v=0,n.va=n.width,n.o=n.height,n.da=0,n.ib=n.width,n.hb=n.height,n.U=n.width,n.T=n.height,i((s=t.Pa).jb,0,255,s.jb.length),e(null!=(s=t.Qa)),s.Cb=0,s.Bb=0,s.Fb=1,i(s.Zb,0,0,s.Zb.length),i(s.Lb,0,0,s.Lb)}if(l.ub>a)return Yt(t,7,"bad partition length");p(s=t.m,r,o,l.ub),o+=l.ub,a-=l.ub,l.Rb&&(u.Ld=P(s),u.Kd=P(s)),u=t.Qa;var c,h=t.Pa;if(e(null!=s),e(null!=u),u.Cb=P(s),u.Cb){if(u.Bb=P(s),P(s)){for(u.Fb=P(s),c=0;4>c;++c)u.Zb[c]=P(s)?m(s,7):0;for(c=0;4>c;++c)u.Lb[c]=P(s)?m(s,6):0}if(u.Bb)for(c=0;3>c;++c)h.jb[c]=P(s)?g(s,8):255}else u.Bb=0;if(s.Ka)return Yt(t,3,"cannot parse segment header");if((u=t.ed).zd=P(s),u.Tb=g(s,6),u.wb=g(s,3),u.Pc=P(s),u.Pc&&P(s)){for(h=0;4>h;++h)P(s)&&(u.vd[h]=m(s,6));for(h=0;4>h;++h)P(s)&&(u.od[h]=m(s,6))}if(t.L=0==u.Tb?0:u.zd?1:2,s.Ka)return Yt(t,3,"cannot parse filter header");var f=a;if(a=c=o,o=c+f,u=f,t.Xb=(1<<g(t.m,2))-1,f<3*(h=t.Xb))r=7;else{for(c+=3*h,u-=3*h,f=0;f<h;++f){var d=r[a+0]|r[a+1]<<8|r[a+2]<<16;d>u&&(d=u),p(t.Jc[+f],r,c,d),c+=d,u-=d,a+=3}p(t.Jc[+h],r,c,u),r=c<o?0:5}if(0!=r)return Yt(t,r,"cannot parse partitions");for(r=g(c=t.m,7),a=P(c)?m(c,4):0,o=P(c)?m(c,4):0,u=P(c)?m(c,4):0,h=P(c)?m(c,4):0,c=P(c)?m(c,4):0,f=t.Qa,d=0;4>d;++d){if(f.Cb){var v=f.Zb[d];f.Fb||(v+=r)}else{if(0<d){t.pb[d]=t.pb[0];continue}v=r}var b=t.pb[d];b.Sc[0]=ti[Vt(v+a,127)],b.Sc[1]=ei[Vt(v+0,127)],b.Eb[0]=2*ti[Vt(v+o,127)],b.Eb[1]=101581*ei[Vt(v+u,127)]>>16,8>b.Eb[1]&&(b.Eb[1]=8),b.Qc[0]=ti[Vt(v+h,117)],b.Qc[1]=ei[Vt(v+c,127)],b.lc=v+c}if(!l.Rb)return Yt(t,4,"Not a key frame.");for(P(s),l=t.Pa,r=0;4>r;++r){for(a=0;8>a;++a)for(o=0;3>o;++o)for(u=0;11>u;++u)h=k(s,li[r][a][o][u])?g(s,8):ai[r][a][o][u],l.Wc[r][a].Yb[o][u]=h;for(a=0;17>a;++a)l.Xc[r][a]=l.Wc[r][ui[a]]}return t.kc=P(s),t.kc&&(t.Bd=g(s,8)),t.cb=1}function $t(t,e,n,r,i,o,a){var s=e[i].Yb[n];for(n=0;16>i;++i){if(!k(t,s[n+0]))return i;for(;!k(t,s[n+1]);)if(s=e[++i].Yb[0],n=0,16==i)return 16;var l=e[i+1].Yb;if(k(t,s[n+2])){var u=t,c=0;if(k(u,(f=s)[(h=n)+3]))if(k(u,f[h+6])){for(s=0,h=2*(c=k(u,f[h+8]))+(f=k(u,f[h+9+c])),c=0,f=ri[h];f[s];++s)c+=c+k(u,f[s]);c+=3+(8<<h)}else k(u,f[h+7])?(c=7+2*k(u,165),c+=k(u,145)):c=5+k(u,159);else c=k(u,f[h+4])?3+k(u,f[h+5]):2;s=l[2]}else c=1,s=l[1];l=a+ii[i],0>(u=t).b&&_(u);var h,f=u.b,d=(h=u.Ca>>1)-(u.I>>f)>>31;--u.b,u.Ca+=d,u.Ca|=1,u.I-=(h+1&d)<<f,o[l]=((c^d)-d)*r[(0<i)+0]}return 16}function Zt(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,i(t.zc,0,0,t.zc.length),t.ja=0}function Qt(t,e,n,r,i){i=t[e+n+32*r]+(i>>3),t[e+n+32*r]=-256&i?0>i?0:255:i}function te(t,e,n,r,i,o){Qt(t,e,0,n,r+i),Qt(t,e,1,n,r+o),Qt(t,e,2,n,r-o),Qt(t,e,3,n,r-i)}function ee(t){return(20091*t>>16)+t}function ne(t,e,n,r){var i,a=0,s=o(16);for(i=0;4>i;++i){var l=t[e+0]+t[e+8],u=t[e+0]-t[e+8],c=(35468*t[e+4]>>16)-ee(t[e+12]),h=ee(t[e+4])+(35468*t[e+12]>>16);s[a+0]=l+h,s[a+1]=u+c,s[a+2]=u-c,s[a+3]=l-h,a+=4,e++}for(i=a=0;4>i;++i)l=(t=s[a+0]+4)+s[a+8],u=t-s[a+8],c=(35468*s[a+4]>>16)-ee(s[a+12]),Qt(n,r,0,0,l+(h=ee(s[a+4])+(35468*s[a+12]>>16))),Qt(n,r,1,0,u+c),Qt(n,r,2,0,u-c),Qt(n,r,3,0,l-h),a++,r+=32}function re(t,e,n,r){var i=t[e+0]+4,o=35468*t[e+4]>>16,a=ee(t[e+4]),s=35468*t[e+1]>>16;te(n,r,0,i+a,t=ee(t[e+1]),s),te(n,r,1,i+o,t,s),te(n,r,2,i-o,t,s),te(n,r,3,i-a,t,s)}function ie(t,e,n,r,i){ne(t,e,n,r),i&&ne(t,e+16,n,r+4)}function oe(t,e,n,r){or(t,e+0,n,r,1),or(t,e+32,n,r+128,1)}function ae(t,e,n,r){var i;for(t=t[e+0]+4,i=0;4>i;++i)for(e=0;4>e;++e)Qt(n,r,e,i,t)}function se(t,e,n,r){t[e+0]&&lr(t,e+0,n,r),t[e+16]&&lr(t,e+16,n,r+4),t[e+32]&&lr(t,e+32,n,r+128),t[e+48]&&lr(t,e+48,n,r+128+4)}function le(t,e,n,r){var i,a=o(16);for(i=0;4>i;++i){var s=t[e+0+i]+t[e+12+i],l=t[e+4+i]+t[e+8+i],u=t[e+4+i]-t[e+8+i],c=t[e+0+i]-t[e+12+i];a[0+i]=s+l,a[8+i]=s-l,a[4+i]=c+u,a[12+i]=c-u}for(i=0;4>i;++i)s=(t=a[0+4*i]+3)+a[3+4*i],l=a[1+4*i]+a[2+4*i],u=a[1+4*i]-a[2+4*i],c=t-a[3+4*i],n[r+0]=s+l>>3,n[r+16]=c+u>>3,n[r+32]=s-l>>3,n[r+48]=c-u>>3,r+=64}function ue(t,e,n){var r,i=e-32,o=Or,a=255-t[i-1];for(r=0;r<n;++r){var s,l=o,u=a+t[e-1];for(s=0;s<n;++s)t[e+s]=l[u+t[i+s]];e+=32}}function ce(t,e){ue(t,e,4)}function he(t,e){ue(t,e,8)}function fe(t,e){ue(t,e,16)}function de(t,e){var n;for(n=0;16>n;++n)r(t,e+32*n,t,e-32,16)}function pe(t,e){var n;for(n=16;0<n;--n)i(t,e,t[e-1],16),e+=32}function ge(t,e,n){var r;for(r=0;16>r;++r)i(e,n+32*r,t,16)}function me(t,e){var n,r=16;for(n=0;16>n;++n)r+=t[e-1+32*n]+t[e+n-32];ge(r>>5,t,e)}function ve(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e-1+32*n];ge(r>>4,t,e)}function be(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e+n-32];ge(r>>4,t,e)}function ye(t,e){ge(128,t,e)}function we(t,e,n){return t+2*e+n+2>>2}function xe(t,e){var n,i=e-32;for(i=new Uint8Array([we(t[i-1],t[i+0],t[i+1]),we(t[i+0],t[i+1],t[i+2]),we(t[i+1],t[i+2],t[i+3]),we(t[i+2],t[i+3],t[i+4])]),n=0;4>n;++n)r(t,e+32*n,i,0,i.length)}function Ne(t,e){var n=t[e-1],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96];F(t,e+0,16843009*we(t[e-1-32],n,r)),F(t,e+32,16843009*we(n,r,i)),F(t,e+64,16843009*we(r,i,o)),F(t,e+96,16843009*we(i,o,o))}function Le(t,e){var n,r=4;for(n=0;4>n;++n)r+=t[e+n-32]+t[e-1+32*n];for(r>>=3,n=0;4>n;++n)i(t,e+32*n,r,4)}function Ae(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1-32],a=t[e+0-32],s=t[e+1-32],l=t[e+2-32],u=t[e+3-32];t[e+0+96]=we(r,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=we(n,r,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=we(o,n,r),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=we(a,o,n),t[e+3+64]=t[e+2+32]=t[e+1+0]=we(s,a,o),t[e+3+32]=t[e+2+0]=we(l,s,a),t[e+3+0]=we(u,l,s)}function Se(t,e){var n=t[e+1-32],r=t[e+2-32],i=t[e+3-32],o=t[e+4-32],a=t[e+5-32],s=t[e+6-32],l=t[e+7-32];t[e+0+0]=we(t[e+0-32],n,r),t[e+1+0]=t[e+0+32]=we(n,r,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=we(r,i,o),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=we(i,o,a),t[e+3+32]=t[e+2+64]=t[e+1+96]=we(o,a,s),t[e+3+64]=t[e+2+96]=we(a,s,l),t[e+3+96]=we(s,l,l)}function _e(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1-32],a=t[e+0-32],s=t[e+1-32],l=t[e+2-32],u=t[e+3-32];t[e+0+0]=t[e+1+64]=o+a+1>>1,t[e+1+0]=t[e+2+64]=a+s+1>>1,t[e+2+0]=t[e+3+64]=s+l+1>>1,t[e+3+0]=l+u+1>>1,t[e+0+96]=we(i,r,n),t[e+0+64]=we(r,n,o),t[e+0+32]=t[e+1+96]=we(n,o,a),t[e+1+32]=t[e+2+96]=we(o,a,s),t[e+2+32]=t[e+3+96]=we(a,s,l),t[e+3+32]=we(s,l,u)}function Pe(t,e){var n=t[e+0-32],r=t[e+1-32],i=t[e+2-32],o=t[e+3-32],a=t[e+4-32],s=t[e+5-32],l=t[e+6-32],u=t[e+7-32];t[e+0+0]=n+r+1>>1,t[e+1+0]=t[e+0+64]=r+i+1>>1,t[e+2+0]=t[e+1+64]=i+o+1>>1,t[e+3+0]=t[e+2+64]=o+a+1>>1,t[e+0+32]=we(n,r,i),t[e+1+32]=t[e+0+96]=we(r,i,o),t[e+2+32]=t[e+1+96]=we(i,o,a),t[e+3+32]=t[e+2+96]=we(o,a,s),t[e+3+64]=we(a,s,l),t[e+3+96]=we(s,l,u)}function ke(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96];t[e+0+0]=n+r+1>>1,t[e+2+0]=t[e+0+32]=r+i+1>>1,t[e+2+32]=t[e+0+64]=i+o+1>>1,t[e+1+0]=we(n,r,i),t[e+3+0]=t[e+1+32]=we(r,i,o),t[e+3+32]=t[e+1+64]=we(i,o,o),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=o}function Fe(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96],a=t[e-1-32],s=t[e+0-32],l=t[e+1-32],u=t[e+2-32];t[e+0+0]=t[e+2+32]=n+a+1>>1,t[e+0+32]=t[e+2+64]=r+n+1>>1,t[e+0+64]=t[e+2+96]=i+r+1>>1,t[e+0+96]=o+i+1>>1,t[e+3+0]=we(s,l,u),t[e+2+0]=we(a,s,l),t[e+1+0]=t[e+3+32]=we(n,a,s),t[e+1+32]=t[e+3+64]=we(r,n,a),t[e+1+64]=t[e+3+96]=we(i,r,n),t[e+1+96]=we(o,i,r)}function Ce(t,e){var n;for(n=0;8>n;++n)r(t,e+32*n,t,e-32,8)}function je(t,e){var n;for(n=0;8>n;++n)i(t,e,t[e-1],8),e+=32}function Ie(t,e,n){var r;for(r=0;8>r;++r)i(e,n+32*r,t,8)}function Oe(t,e){var n,r=8;for(n=0;8>n;++n)r+=t[e+n-32]+t[e-1+32*n];Ie(r>>4,t,e)}function Ee(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e+n-32];Ie(r>>3,t,e)}function Re(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e-1+32*n];Ie(r>>3,t,e)}function Be(t,e){Ie(128,t,e)}function De(t,e,n){var r=t[e-n],i=t[e+0],o=3*(i-r)+jr[1020+t[e-2*n]-t[e+n]],a=Ir[112+(o+4>>3)];t[e-n]=Or[255+r+Ir[112+(o+3>>3)]],t[e+0]=Or[255+i-a]}function Te(t,e,n,r){var i=t[e+0],o=t[e+n];return Er[255+t[e-2*n]-t[e-n]]>r||Er[255+o-i]>r}function Me(t,e,n,r){return 4*Er[255+t[e-n]-t[e+0]]+Er[255+t[e-2*n]-t[e+n]]<=r}function qe(t,e,n,r,i){var o=t[e-3*n],a=t[e-2*n],s=t[e-n],l=t[e+0],u=t[e+n],c=t[e+2*n],h=t[e+3*n];return 4*Er[255+s-l]+Er[255+a-u]>r?0:Er[255+t[e-4*n]-o]<=i&&Er[255+o-a]<=i&&Er[255+a-s]<=i&&Er[255+h-c]<=i&&Er[255+c-u]<=i&&Er[255+u-l]<=i}function Ue(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Me(t,e+r,n,i)&&De(t,e+r,n)}function ze(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Me(t,e+r*n,1,i)&&De(t,e+r*n,1)}function We(t,e,n,r){var i;for(i=3;0<i;--i)Ue(t,e+=4*n,n,r)}function He(t,e,n,r){var i;for(i=3;0<i;--i)ze(t,e+=4,n,r)}function Ve(t,e,n,r,i,o,a,s){for(o=2*o+1;0<i--;){if(qe(t,e,n,o,a))if(Te(t,e,n,s))De(t,e,n);else{var l=t,u=e,c=n,h=l[u-2*c],f=l[u-c],d=l[u+0],p=l[u+c],g=l[u+2*c],m=27*(b=jr[1020+3*(d-f)+jr[1020+h-p]])+63>>7,v=18*b+63>>7,b=9*b+63>>7;l[u-3*c]=Or[255+l[u-3*c]+b],l[u-2*c]=Or[255+h+v],l[u-c]=Or[255+f+m],l[u+0]=Or[255+d-m],l[u+c]=Or[255+p-v],l[u+2*c]=Or[255+g-b]}e+=r}}function Ge(t,e,n,r,i,o,a,s){for(o=2*o+1;0<i--;){if(qe(t,e,n,o,a))if(Te(t,e,n,s))De(t,e,n);else{var l=t,u=e,c=n,h=l[u-c],f=l[u+0],d=l[u+c],p=Ir[112+(4+(g=3*(f-h))>>3)],g=Ir[112+(g+3>>3)],m=p+1>>1;l[u-2*c]=Or[255+l[u-2*c]+m],l[u-c]=Or[255+h+g],l[u+0]=Or[255+f-p],l[u+c]=Or[255+d-m]}e+=r}}function Je(t,e,n,r,i,o){Ve(t,e,n,1,16,r,i,o)}function Ye(t,e,n,r,i,o){Ve(t,e,1,n,16,r,i,o)}function Ke(t,e,n,r,i,o){var a;for(a=3;0<a;--a)Ge(t,e+=4*n,n,1,16,r,i,o)}function Xe(t,e,n,r,i,o){var a;for(a=3;0<a;--a)Ge(t,e+=4,1,n,16,r,i,o)}function $e(t,e,n,r,i,o,a,s){Ve(t,e,i,1,8,o,a,s),Ve(n,r,i,1,8,o,a,s)}function Ze(t,e,n,r,i,o,a,s){Ve(t,e,1,i,8,o,a,s),Ve(n,r,1,i,8,o,a,s)}function Qe(t,e,n,r,i,o,a,s){Ge(t,e+4*i,i,1,8,o,a,s),Ge(n,r+4*i,i,1,8,o,a,s)}function tn(t,e,n,r,i,o,a,s){Ge(t,e+4,1,i,8,o,a,s),Ge(n,r+4,1,i,8,o,a,s)}function en(){this.ba=new at,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new lt,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function nn(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function rn(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function on(){this.ua=0,this.Wa=new R,this.vb=new R,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new h}function an(){this.xb=this.a=0,this.l=new Gt,this.ca=new at,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new x,this.Pb=0,this.wd=new x,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new on,this.ab=0,this.gc=a(4,rn),this.Oc=0}function sn(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Gt,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function ln(t,e,n,r,i,o,a){for(t=null==t?0:t[e+0],e=0;e<a;++e)i[o+e]=t+n[r+e]&255,t=i[o+e]}function un(t,e,n,r,i,o,a){var s;if(null==t)ln(null,null,n,r,i,o,a);else for(s=0;s<a;++s)i[o+s]=t[e+s]+n[r+s]&255}function cn(t,e,n,r,i,o,a){if(null==t)ln(null,null,n,r,i,o,a);else{var s,l=t[e+0],u=l,c=l;for(s=0;s<a;++s)u=c+(l=t[e+s])-u,c=n[r+s]+(-256&u?0>u?0:255:u)&255,u=l,i[o+s]=c}}function hn(t,n,i,a){var s=n.width,l=n.o;if(e(null!=t&&null!=n),0>i||0>=a||i+a>l)return null;if(!t.Cc){if(null==t.ga){var u;if(t.ga=new sn,(u=null==t.ga)||(u=n.width*n.o,e(0==t.Gb.length),t.Gb=o(u),t.Uc=0,null==t.Gb?u=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,u=1),u=!u),!u){u=t.ga;var c=t.Fa,h=t.P,f=t.qc,d=t.mb,p=t.nb,g=h+1,m=f-1,b=u.l;if(e(null!=c&&null!=d&&null!=n),gi[0]=null,gi[1]=ln,gi[2]=un,gi[3]=cn,u.ca=d,u.tb=p,u.c=n.width,u.i=n.height,e(0<u.c&&0<u.i),1>=f)n=0;else if(u.$a=3&c[h+0],u.Z=c[h+0]>>2&3,u.Lc=c[h+0]>>4&3,h=c[h+0]>>6&3,0>u.$a||1<u.$a||4<=u.Z||1<u.Lc||h)n=0;else if(b.put=dt,b.ac=ft,b.bc=pt,b.ma=u,b.width=n.width,b.height=n.height,b.Da=n.Da,b.v=n.v,b.va=n.va,b.j=n.j,b.o=n.o,u.$a)t:{e(1==u.$a),n=kt();e:for(;;){if(null==n){n=0;break t}if(e(null!=u),u.mc=n,n.c=u.c,n.i=u.i,n.l=u.l,n.l.ma=u,n.l.width=u.c,n.l.height=u.i,n.a=0,v(n.m,c,g,m),!Ft(u.c,u.i,1,n,null))break e;if(1==n.ab&&3==n.gc[0].hc&&Lt(n.s)?(u.ic=1,c=n.c*n.i,n.Ta=null,n.Ua=0,n.V=o(c),n.Ba=0,null==n.V?(n.a=1,n=0):n=1):(u.ic=0,n=Ct(n,u.c)),!n)break e;n=1;break t}u.mc=null,n=0}else n=m>=u.c*u.i;u=!n}if(u)return null;1!=t.ga.Lc?t.Ga=0:a=l-i}e(null!=t.ga),e(i+a<=l);t:{if(n=(c=t.ga).c,l=c.l.o,0==c.$a){if(g=t.rc,m=t.Vc,b=t.Fa,h=t.P+1+i*n,f=t.mb,d=t.nb+i*n,e(h<=t.P+t.qc),0!=c.Z)for(e(null!=gi[c.Z]),u=0;u<a;++u)gi[c.Z](g,m,b,h,f,d,n),g=f,m=d,d+=n,h+=n;else for(u=0;u<a;++u)r(f,d,b,h,n),g=f,m=d,d+=n,h+=n;t.rc=g,t.Vc=m}else{if(e(null!=c.mc),n=i+a,e(null!=(u=c.mc)),e(n<=u.i),u.C>=n)n=1;else if(c.ic||mn(),c.ic){c=u.V,g=u.Ba,m=u.c;var y=u.i,w=(b=1,h=u.$/m,f=u.$%m,d=u.m,p=u.s,u.$),x=m*y,N=m*n,A=p.wc,_=w<N?wt(p,f,h):null;e(w<=x),e(n<=y),e(Lt(p));e:for(;;){for(;!d.h&&w<N;){if(f&A||(_=wt(p,f,h)),e(null!=_),S(d),256>(y=bt(_.G[0],_.H[0],d)))c[g+w]=y,++w,++f>=m&&(f=0,++h<=n&&!(h%16)&&St(u,h));else{if(!(280>y)){b=0;break e}y=mt(y-256,d);var P,k=bt(_.G[4],_.H[4],d);if(S(d),!(w>=(k=vt(m,k=mt(k,d)))&&x-w>=y)){b=0;break e}for(P=0;P<y;++P)c[g+w+P]=c[g+w+P-k];for(w+=y,f+=y;f>=m;)f-=m,++h<=n&&!(h%16)&&St(u,h);w<N&&f&A&&(_=wt(p,f,h))}e(d.h==L(d))}St(u,h>n?n:h);break e}!b||d.h&&w<x?(b=0,u.a=d.h?5:3):u.$=w,n=b}else n=_t(u,u.V,u.Ba,u.c,u.i,n,jt);if(!n){a=0;break t}}i+a>=l&&(t.Cc=1),a=1}if(!a)return null;if(t.Cc&&(null!=(a=t.ga)&&(a.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+i*s}function fn(t,e,n,r,i,o){for(;0<i--;){var a,s=t,l=e+(n?1:0),u=t,c=e+(n?0:3);for(a=0;a<r;++a){var h=u[c+4*a];255!=h&&(h*=32897,s[l+4*a+0]=s[l+4*a+0]*h>>23,s[l+4*a+1]=s[l+4*a+1]*h>>23,s[l+4*a+2]=s[l+4*a+2]*h>>23)}e+=o}}function dn(t,e,n,r,i){for(;0<r--;){var o;for(o=0;o<n;++o){var a=t[e+2*o+0],s=15&(u=t[e+2*o+1]),l=4369*s,u=(240&u|u>>4)*l>>16;t[e+2*o+0]=(240&a|a>>4)*l>>16&240|(15&a|a<<4)*l>>16>>4&15,t[e+2*o+1]=240&u|s}e+=i}}function pn(t,e,n,r,i,o,a,s){var l,u,c=255;for(u=0;u<i;++u){for(l=0;l<r;++l){var h=t[e+l];o[a+4*l]=h,c&=h}e+=n,a+=s}return 255!=c}function gn(t,e,n,r,i){var o;for(o=0;o<i;++o)n[r+o]=t[e+o]>>8}function mn(){Nr=fn,Lr=dn,Ar=pn,Sr=gn}function vn(n,r,i){t[n]=function(t,n,o,a,s,l,u,c,h,f,d,p,g,m,v,b,y){var w,x=y-1>>1,N=s[l+0]|u[c+0]<<16,L=h[f+0]|d[p+0]<<16;e(null!=t);var A=3*N+L+131074>>2;for(r(t[n+0],255&A,A>>16,g,m),null!=o&&(A=3*L+N+131074>>2,r(o[a+0],255&A,A>>16,v,b)),w=1;w<=x;++w){var S=s[l+w]|u[c+w]<<16,_=h[f+w]|d[p+w]<<16,P=N+S+L+_+524296,k=P+2*(S+L)>>3;A=k+N>>1,N=(P=P+2*(N+_)>>3)+S>>1,r(t[n+2*w-1],255&A,A>>16,g,m+(2*w-1)*i),r(t[n+2*w-0],255&N,N>>16,g,m+(2*w-0)*i),null!=o&&(A=P+L>>1,N=k+_>>1,r(o[a+2*w-1],255&A,A>>16,v,b+(2*w-1)*i),r(o[a+2*w+0],255&N,N>>16,v,b+(2*w+0)*i)),N=S,L=_}1&y||(A=3*N+L+131074>>2,r(t[n+y-1],255&A,A>>16,g,m+(y-1)*i),null!=o&&(A=3*L+N+131074>>2,r(o[a+y-1],255&A,A>>16,v,b+(y-1)*i)))}}function bn(){mi[Rr]=vi,mi[Br]=yi,mi[Dr]=bi,mi[Tr]=wi,mi[Mr]=xi,mi[qr]=Ni,mi[Ur]=Li,mi[zr]=yi,mi[Wr]=wi,mi[Hr]=xi,mi[Vr]=Ni}function yn(t){return-16384&t?0>t?0:255:t>>ki}function wn(t,e){return yn((19077*t>>8)+(26149*e>>8)-14234)}function xn(t,e,n){return yn((19077*t>>8)-(6419*e>>8)-(13320*n>>8)+8708)}function Nn(t,e){return yn((19077*t>>8)+(33050*e>>8)-17685)}function Ln(t,e,n,r,i){r[i+0]=wn(t,n),r[i+1]=xn(t,e,n),r[i+2]=Nn(t,e)}function An(t,e,n,r,i){r[i+0]=Nn(t,e),r[i+1]=xn(t,e,n),r[i+2]=wn(t,n)}function Sn(t,e,n,r,i){var o=xn(t,e,n);e=o<<3&224|Nn(t,e)>>3,r[i+0]=248&wn(t,n)|o>>5,r[i+1]=e}function _n(t,e,n,r,i){var o=240&Nn(t,e)|15;r[i+0]=240&wn(t,n)|xn(t,e,n)>>4,r[i+1]=o}function Pn(t,e,n,r,i){r[i+0]=255,Ln(t,e,n,r,i+1)}function kn(t,e,n,r,i){An(t,e,n,r,i),r[i+3]=255}function Fn(t,e,n,r,i){Ln(t,e,n,r,i),r[i+3]=255}function Vt(t,e){return 0>t?0:t>e?e:t}function Cn(e,n,r){t[e]=function(t,e,i,o,a,s,l,u,c){for(var h=u+(-2&c)*r;u!=h;)n(t[e+0],i[o+0],a[s+0],l,u),n(t[e+1],i[o+0],a[s+0],l,u+r),e+=2,++o,++s,u+=2*r;1&c&&n(t[e+0],i[o+0],a[s+0],l,u)}}function jn(t,e,n){return 0==n?0==t?0==e?6:5:0==e?4:0:n}function In(t,e,n,r,i){switch(t>>>30){case 3:or(e,n,r,i,0);break;case 2:ar(e,n,r,i);break;case 1:lr(e,n,r,i)}}function On(t,e){var n,o,a=e.M,s=e.Nb,l=t.oc,u=t.pc+40,c=t.oc,h=t.pc+584,f=t.oc,d=t.pc+600;for(n=0;16>n;++n)l[u+32*n-1]=129;for(n=0;8>n;++n)c[h+32*n-1]=129,f[d+32*n-1]=129;for(0<a?l[u-1-32]=c[h-1-32]=f[d-1-32]=129:(i(l,u-32-1,127,21),i(c,h-32-1,127,9),i(f,d-32-1,127,9)),o=0;o<t.za;++o){var p=e.ya[e.aa+o];if(0<o){for(n=-1;16>n;++n)r(l,u+32*n-4,l,u+32*n+12,4);for(n=-1;8>n;++n)r(c,h+32*n-4,c,h+32*n+4,4),r(f,d+32*n-4,f,d+32*n+4,4)}var g=t.Gd,m=t.Hd+o,v=p.ad,b=p.Hc;if(0<a&&(r(l,u-32,g[m].y,0,16),r(c,h-32,g[m].f,0,8),r(f,d-32,g[m].ea,0,8)),p.Za){var y=l,w=u-32+16;for(0<a&&(o>=t.za-1?i(y,w,g[m].y[15],4):r(y,w,g[m+1].y,0,4)),n=0;4>n;n++)y[w+128+n]=y[w+256+n]=y[w+384+n]=y[w+0+n];for(n=0;16>n;++n,b<<=2)y=l,w=u+Bi[n],hi[p.Ob[n]](y,w),In(b,v,16*+n,y,w)}else if(y=jn(o,a,p.Ob[0]),ci[y](l,u),0!=b)for(n=0;16>n;++n,b<<=2)In(b,v,16*+n,l,u+Bi[n]);for(n=p.Gc,y=jn(o,a,p.Dd),fi[y](c,h),fi[y](f,d),b=v,y=c,w=h,255&(p=n|0)&&(170&p?sr(b,256,y,w):ur(b,256,y,w)),p=f,b=d,255&(n>>=8)&&(170&n?sr(v,320,p,b):ur(v,320,p,b)),a<t.Ub-1&&(r(g[m].y,0,l,u+480,16),r(g[m].f,0,c,h+224,8),r(g[m].ea,0,f,d+224,8)),n=8*s*t.B,g=t.sa,m=t.ta+16*o+16*s*t.R,v=t.qa,p=t.ra+8*o+n,b=t.Ha,y=t.Ia+8*o+n,n=0;16>n;++n)r(g,m+n*t.R,l,u+32*n,16);for(n=0;8>n;++n)r(v,p+n*t.B,c,h+32*n,8),r(b,y+n*t.B,f,d+32*n,8)}}function En(t,r,i,o,a,s,l,u,c){var h=[0],f=[0],d=0,p=null!=c?c.kd:0,g=null!=c?c:new nn;if(null==t||12>i)return 7;g.data=t,g.w=r,g.ha=i,r=[r],i=[i],g.gb=[g.gb];t:{var m=r,b=i,y=g.gb;if(e(null!=t),e(null!=b),e(null!=y),y[0]=0,12<=b[0]&&!n(t,m[0],"RIFF")){if(n(t,m[0]+8,"WEBP")){y=3;break t}var w=I(t,m[0]+4);if(12>w||4294967286<w){y=3;break t}if(p&&w>b[0]-8){y=7;break t}y[0]=w,m[0]+=12,b[0]-=12}y=0}if(0!=y)return y;for(w=0<g.gb[0],i=i[0];;){t:{var N=t;b=r,y=i;var L=h,A=f,S=m=[0];if((k=d=[d])[0]=0,8>y[0])y=7;else{if(!n(N,b[0],"VP8X")){if(10!=I(N,b[0]+4)){y=3;break t}if(18>y[0]){y=7;break t}var _=I(N,b[0]+8),P=1+j(N,b[0]+12);if(2147483648<=P*(N=1+j(N,b[0]+15))){y=3;break t}null!=S&&(S[0]=_),null!=L&&(L[0]=P),null!=A&&(A[0]=N),b[0]+=18,y[0]-=18,k[0]=1}y=0}}if(d=d[0],m=m[0],0!=y)return y;if(b=!!(2&m),!w&&d)return 3;if(null!=s&&(s[0]=!!(16&m)),null!=l&&(l[0]=b),null!=u&&(u[0]=0),l=h[0],m=f[0],d&&b&&null==c){y=0;break}if(4>i){y=7;break}if(w&&d||!w&&!d&&!n(t,r[0],"ALPH")){i=[i],g.na=[g.na],g.P=[g.P],g.Sa=[g.Sa];t:{_=t,y=r,w=i;var k=g.gb;L=g.na,A=g.P,S=g.Sa,P=22,e(null!=_),e(null!=w),N=y[0];var F=w[0];for(e(null!=L),e(null!=S),L[0]=null,A[0]=null,S[0]=0;;){if(y[0]=N,w[0]=F,8>F){y=7;break t}var C=I(_,N+4);if(4294967286<C){y=3;break t}var O=8+C+1&-2;if(P+=O,0<k&&P>k){y=3;break t}if(!n(_,N,"VP8 ")||!n(_,N,"VP8L")){y=0;break t}if(F[0]<O){y=7;break t}n(_,N,"ALPH")||(L[0]=_,A[0]=N+8,S[0]=C),N+=O,F-=O}}if(i=i[0],g.na=g.na[0],g.P=g.P[0],g.Sa=g.Sa[0],0!=y)break}i=[i],g.Ja=[g.Ja],g.xa=[g.xa];t:if(k=t,y=r,w=i,L=g.gb[0],A=g.Ja,S=g.xa,_=y[0],N=!n(k,_,"VP8 "),P=!n(k,_,"VP8L"),e(null!=k),e(null!=w),e(null!=A),e(null!=S),8>w[0])y=7;else{if(N||P){if(k=I(k,_+4),12<=L&&k>L-12){y=3;break t}if(p&&k>w[0]-8){y=7;break t}A[0]=k,y[0]+=8,w[0]-=8,S[0]=P}else S[0]=5<=w[0]&&47==k[_+0]&&!(k[_+4]>>5),A[0]=w[0];y=0}if(i=i[0],g.Ja=g.Ja[0],g.xa=g.xa[0],r=r[0],0!=y)break;if(4294967286<g.Ja)return 3;if(null==u||b||(u[0]=g.xa?2:1),l=[l],m=[m],g.xa){if(5>i){y=7;break}u=l,p=m,b=s,null==t||5>i?t=0:5<=i&&47==t[r+0]&&!(t[r+4]>>5)?(w=[0],k=[0],L=[0],v(A=new x,t,r,i),gt(A,w,k,L)?(null!=u&&(u[0]=w[0]),null!=p&&(p[0]=k[0]),null!=b&&(b[0]=L[0]),t=1):t=0):t=0}else{if(10>i){y=7;break}u=m,null==t||10>i||!Kt(t,r+3,i-3)?t=0:(p=t[r+0]|t[r+1]<<8|t[r+2]<<16,b=16383&(t[r+7]<<8|t[r+6]),t=16383&(t[r+9]<<8|t[r+8]),1&p||3<(p>>1&7)||!(p>>4&1)||p>>5>=g.Ja||!b||!t?t=0:(l&&(l[0]=b),u&&(u[0]=t),t=1))}if(!t)return 3;if(l=l[0],m=m[0],d&&(h[0]!=l||f[0]!=m))return 3;null!=c&&(c[0]=g,c.offset=r-c.w,e(4294967286>r-c.w),e(c.offset==c.ha-i));break}return 0==y||7==y&&d&&null==c?(null!=s&&(s[0]|=null!=g.na&&0<g.na.length),null!=o&&(o[0]=l),null!=a&&(a[0]=m),0):y}function Rn(t,e,n){var r=e.width,i=e.height,o=0,a=0,s=r,l=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(s=t.cd,l=t.bd,o=t.v,a=t.j,11>n||(o&=-2,a&=-2),0>o||0>a||0>=s||0>=l||o+s>r||a+l>i))return 0;if(e.v=o,e.j=a,e.va=o+s,e.o=a+l,e.U=s,e.T=l,e.da=null!=t&&0<t.da,e.da){if(!B(s,l,n=[t.ib],o=[t.hb]))return 0;e.ib=n[0],e.hb=o[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*r/4&&e.hb<3*i/4,e.Kb=0),1}function Bn(t){if(null==t)return 2;if(11>t.S){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function Dn(t,e,n,r){if(null==r||0>=t||0>=e)return 2;if(null!=n){if(n.Da){var i=n.cd,a=n.bd,s=-2&n.v,l=-2&n.j;if(0>s||0>l||0>=i||0>=a||s+i>t||l+a>e)return 2;t=i,e=a}if(n.da){if(!B(t,e,i=[n.ib],a=[n.hb]))return 2;t=i[0],e=a[0]}}r.width=t,r.height=e;t:{var u=r.width,c=r.height;if(t=r.S,0>=u||0>=c||!(t>=Rr&&13>t))t=2;else{if(0>=r.Rd&&null==r.sd){s=a=i=e=0;var h=(l=u*qi[t])*c;if(11>t||(a=(c+1)/2*(e=(u+1)/2),12==t&&(s=(i=u)*c)),null==(c=o(h+2*a+s))){t=1;break t}r.sd=c,11>t?((u=r.f.RGBA).eb=c,u.fb=0,u.A=l,u.size=h):((u=r.f.kb).y=c,u.O=0,u.fa=l,u.Fd=h,u.f=c,u.N=0+h,u.Ab=e,u.Cd=a,u.ea=c,u.W=0+h+a,u.Db=e,u.Ed=a,12==t&&(u.F=c,u.J=0+h+2*a),u.Tc=s,u.lb=i)}if(e=1,i=r.S,a=r.width,s=r.height,i>=Rr&&13>i)if(11>i)t=r.f.RGBA,e&=(l=Math.abs(t.A))*(s-1)+a<=t.size,e&=l>=a*qi[i],e&=null!=t.eb;else{t=r.f.kb,l=(a+1)/2,h=(s+1)/2,u=Math.abs(t.fa),c=Math.abs(t.Ab);var f=Math.abs(t.Db),d=Math.abs(t.lb),p=d*(s-1)+a;e&=u*(s-1)+a<=t.Fd,e&=c*(h-1)+l<=t.Cd,e=(e&=f*(h-1)+l<=t.Ed)&u>=a&c>=l&f>=l,e&=null!=t.y,e&=null!=t.f,e&=null!=t.ea,12==i&&(e&=d>=a,e&=p<=t.Tc,e&=null!=t.F)}else e=0;t=e?0:2}}return 0!=t||null!=n&&n.fd&&(t=Bn(r)),t}var Tn=64,Mn=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],qn=24,Un=32,zn=8,Wn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];M("Predictor0","PredictorAdd0"),t.Predictor0=function(){return **********},t.Predictor1=function(t){return t},t.Predictor2=function(t,e,n){return e[n+0]},t.Predictor3=function(t,e,n){return e[n+1]},t.Predictor4=function(t,e,n){return e[n-1]},t.Predictor5=function(t,e,n){return U(U(t,e[n+1]),e[n+0])},t.Predictor6=function(t,e,n){return U(t,e[n-1])},t.Predictor7=function(t,e,n){return U(t,e[n+0])},t.Predictor8=function(t,e,n){return U(e[n-1],e[n+0])},t.Predictor9=function(t,e,n){return U(e[n+0],e[n+1])},t.Predictor10=function(t,e,n){return U(U(t,e[n-1]),U(e[n+0],e[n+1]))},t.Predictor11=function(t,e,n){var r=e[n+0];return 0>=H(r>>24&255,t>>24&255,(e=e[n-1])>>24&255)+H(r>>16&255,t>>16&255,e>>16&255)+H(r>>8&255,t>>8&255,e>>8&255)+H(255&r,255&t,255&e)?r:t},t.Predictor12=function(t,e,n){var r=e[n+0];return(z((t>>24&255)+(r>>24&255)-((e=e[n-1])>>24&255))<<24|z((t>>16&255)+(r>>16&255)-(e>>16&255))<<16|z((t>>8&255)+(r>>8&255)-(e>>8&255))<<8|z((255&t)+(255&r)-(255&e)))>>>0},t.Predictor13=function(t,e,n){var r=e[n-1];return(W((t=U(t,e[n+0]))>>24&255,r>>24&255)<<24|W(t>>16&255,r>>16&255)<<16|W(t>>8&255,r>>8&255)<<8|W(255&t,255&r))>>>0};var Hn=t.PredictorAdd0;t.PredictorAdd1=V,M("Predictor2","PredictorAdd2"),M("Predictor3","PredictorAdd3"),M("Predictor4","PredictorAdd4"),M("Predictor5","PredictorAdd5"),M("Predictor6","PredictorAdd6"),M("Predictor7","PredictorAdd7"),M("Predictor8","PredictorAdd8"),M("Predictor9","PredictorAdd9"),M("Predictor10","PredictorAdd10"),M("Predictor11","PredictorAdd11"),M("Predictor12","PredictorAdd12"),M("Predictor13","PredictorAdd13");var Vn=t.PredictorAdd2;K("ColorIndexInverseTransform","MapARGB","32b",(function(t){return t>>8&255}),(function(t){return t})),K("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",(function(t){return t}),(function(t){return t>>8&255}));var Gn,Jn=t.ColorIndexInverseTransform,Yn=t.MapARGB,Kn=t.VP8LColorIndexInverseTransformAlpha,Xn=t.MapAlpha,$n=t.VP8LPredictorsAdd=[];$n.length=16,(t.VP8LPredictors=[]).length=16,(t.VP8LPredictorsAdd_C=[]).length=16,(t.VP8LPredictors_C=[]).length=16;var Zn,Qn,tr,er,nr,rr,ir,or,ar,sr,lr,ur,cr,hr,fr,dr,pr,gr,mr,vr,br,yr,wr,xr,Nr,Lr,Ar,Sr,_r=o(511),Pr=o(2041),kr=o(225),Fr=o(767),Cr=0,jr=Pr,Ir=kr,Or=Fr,Er=_r,Rr=0,Br=1,Dr=2,Tr=3,Mr=4,qr=5,Ur=6,zr=7,Wr=8,Hr=9,Vr=10,Gr=[2,3,7],Jr=[3,3,11],Yr=[280,256,256,256,40],Kr=[0,1,1,1,0],Xr=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],$r=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Zr=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Qr=8,ti=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ei=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ni=null,ri=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],ii=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],oi=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],ai=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],si=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],li=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],ui=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],ci=[],hi=[],fi=[],di=1,pi=2,gi=[],mi=[];vn("UpsampleRgbLinePair",Ln,3),vn("UpsampleBgrLinePair",An,3),vn("UpsampleRgbaLinePair",Fn,4),vn("UpsampleBgraLinePair",kn,4),vn("UpsampleArgbLinePair",Pn,4),vn("UpsampleRgba4444LinePair",_n,2),vn("UpsampleRgb565LinePair",Sn,2);var vi=t.UpsampleRgbLinePair,bi=t.UpsampleBgrLinePair,yi=t.UpsampleRgbaLinePair,wi=t.UpsampleBgraLinePair,xi=t.UpsampleArgbLinePair,Ni=t.UpsampleRgba4444LinePair,Li=t.UpsampleRgb565LinePair,Ai=16,Si=1<<Ai-1,_i=-227,Pi=482,ki=6,Fi=0,Ci=o(256),ji=o(256),Ii=o(256),Oi=o(256),Ei=o(Pi-_i),Ri=o(Pi-_i);Cn("YuvToRgbRow",Ln,3),Cn("YuvToBgrRow",An,3),Cn("YuvToRgbaRow",Fn,4),Cn("YuvToBgraRow",kn,4),Cn("YuvToArgbRow",Pn,4),Cn("YuvToRgba4444Row",_n,2),Cn("YuvToRgb565Row",Sn,2);var Bi=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Di=[0,2,8],Ti=[8,7,6,4,4,2,2,2,1,1,1,1],Mi=1;this.WebPDecodeRGBA=function(t,n,s,l,u){var c=Br,h=new en,f=new at;h.ba=f,f.S=c,f.width=[f.width],f.height=[f.height];var d=f.width,p=f.height,g=new st;if(null==g||null==t)var m=2;else e(null!=g),m=En(t,n,s,g.width,g.height,g.Pd,g.Qd,g.format,null);if(0!=m?d=0:(null!=d&&(d[0]=g.width[0]),null!=p&&(p[0]=g.height[0]),d=1),d){f.width=f.width[0],f.height=f.height[0],null!=l&&(l[0]=f.width),null!=u&&(u[0]=f.height);t:{if(l=new Gt,(u=new nn).data=t,u.w=n,u.ha=s,u.kd=1,n=[0],e(null!=u),(0==(t=En(u.data,u.w,u.ha,null,null,null,n,null,u))||7==t)&&n[0]&&(t=4),0==(n=t)){if(e(null!=h),l.data=u.data,l.w=u.w+u.offset,l.ha=u.ha-u.offset,l.put=dt,l.ac=ft,l.bc=pt,l.ma=h,u.xa){if(null==(t=kt())){h=1;break t}if(function(t,n){var r=[0],i=[0],o=[0];e:for(;;){if(null==t)return 0;if(null==n)return t.a=2,0;if(t.l=n,t.a=0,v(t.m,n.data,n.w,n.ha),!gt(t.m,r,i,o)){t.a=3;break e}if(t.xb=pi,n.width=r[0],n.height=i[0],!Ft(r[0],i[0],1,t,null))break e;return 1}return e(0!=t.a),0}(t,l)){if(l=0==(n=Dn(l.width,l.height,h.Oa,h.ba))){e:{l=t;n:for(;;){if(null==l){l=0;break e}if(e(null!=l.s.yc),e(null!=l.s.Ya),e(0<l.s.Wb),e(null!=(s=l.l)),e(null!=(u=s.ma)),0!=l.xb){if(l.ca=u.ba,l.tb=u.tb,e(null!=l.ca),!Rn(u.Oa,s,Tr)){l.a=2;break n}if(!Ct(l,s.width))break n;if(s.da)break n;if((s.da||rt(l.ca.S))&&mn(),11>l.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=l.ca.f.kb.F&&mn()),l.Pb&&0<l.s.ua&&null==l.s.vb.X&&!O(l.s.vb,l.s.Wa.Xa)){l.a=1;break n}l.xb=0}if(!_t(l,l.V,l.Ba,l.c,l.i,s.o,Nt))break n;u.Dc=l.Ma,l=1;break e}e(0!=l.a),l=0}l=!l}l&&(n=t.a)}else n=t.a}else{if(null==(t=new Jt)){h=1;break t}if(t.Fa=u.na,t.P=u.P,t.qc=u.Sa,Xt(t,l)){if(0==(n=Dn(l.width,l.height,h.Oa,h.ba))){if(t.Aa=0,s=h.Oa,e(null!=(u=t)),null!=s){if(0<(d=0>(d=s.Md)?0:100<d?255:255*d/100)){for(p=g=0;4>p;++p)12>(m=u.pb[p]).lc&&(m.ia=d*Ti[0>m.lc?0:m.lc]>>3),g|=m.ia;g&&(alert("todo:VP8InitRandom"),u.ia=1)}u.Ga=s.Id,100<u.Ga?u.Ga=100:0>u.Ga&&(u.Ga=0)}(function(t,n){if(null==t)return 0;if(null==n)return Yt(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Xt(t,n))return 0;if(e(t.cb),null==n.ac||n.ac(n)){n.ob&&(t.L=0);var s=Di[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=n.v-s>>4,t.zb=n.j-s>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=n.o+15+s>>4,t.Hb=n.va+15+s>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var l=t.ed;for(s=0;4>s;++s){var u;if(t.Qa.Cb){var c=t.Qa.Lb[s];t.Qa.Fb||(c+=l.Tb)}else c=l.Tb;for(u=0;1>=u;++u){var h=t.gd[s][u],f=c;if(l.Pc&&(f+=l.vd[0],u&&(f+=l.od[0])),0<(f=0>f?0:63<f?63:f)){var d=f;0<l.wb&&(d=4<l.wb?d>>2:d>>1)>9-l.wb&&(d=9-l.wb),1>d&&(d=1),h.dd=d,h.tc=2*f+d,h.ld=40<=f?2:15<=f?1:0}else h.tc=0;h.La=u}}}s=0}else Yt(t,6,"Frame setup failed"),s=t.a;if(s=0==s){if(s){t.$c=0,0<t.Aa||(t.Ic=Mi);e:{s=t.Ic,l=4*(d=t.za);var p=32*d,g=d+1,m=0<t.L?d*(0<t.Aa?2:1):0,v=(2==t.Aa?2:1)*d;if((h=l+832+(u=3*(16*s+Di[t.L])/2*p)+(c=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=h)s=0;else{if(h>t.Vb){if(t.Vb=0,t.Ec=o(h),t.Fc=0,null==t.Ec){s=Yt(t,1,"no memory during frame initialization.");break e}t.Vb=h}h=t.Ec,f=t.Fc,t.Ac=h,t.Bc=f,f+=l,t.Gd=a(p,Wt),t.Hd=0,t.rb=a(g+1,Mt),t.sb=1,t.wa=m?a(m,Tt):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=d),e(!0),t.oc=h,t.pc=f,f+=832,t.ya=a(v,Ut),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=d),t.R=16*d,t.B=8*d,d=(p=Di[t.L])*t.R,p=p/2*t.B,t.sa=h,t.ta=f+d,t.qa=t.sa,t.ra=t.ta+16*s*t.R+p,t.Ha=t.qa,t.Ia=t.ra+8*s*t.B+p,t.$c=0,f+=u,t.mb=c?h:null,t.nb=c?f:null,e(f+c<=t.Fc+t.Vb),Zt(t),i(t.Ac,t.Bc,0,l),s=1}}if(s){if(n.ka=0,n.y=t.sa,n.O=t.ta,n.f=t.qa,n.N=t.ra,n.ea=t.Ha,n.Vd=t.Ia,n.fa=t.R,n.Rc=t.B,n.F=null,n.J=0,!Cr){for(s=-255;255>=s;++s)_r[255+s]=0>s?-s:s;for(s=-1020;1020>=s;++s)Pr[1020+s]=-128>s?-128:127<s?127:s;for(s=-112;112>=s;++s)kr[112+s]=-16>s?-16:15<s?15:s;for(s=-255;510>=s;++s)Fr[255+s]=0>s?0:255<s?255:s;Cr=1}ir=le,or=ie,sr=oe,lr=ae,ur=se,ar=re,cr=Je,hr=Ye,fr=$e,dr=Ze,pr=Ke,gr=Xe,mr=Qe,vr=tn,br=Ue,yr=ze,wr=We,xr=He,hi[0]=Le,hi[1]=ce,hi[2]=xe,hi[3]=Ne,hi[4]=Ae,hi[5]=_e,hi[6]=Se,hi[7]=Pe,hi[8]=Fe,hi[9]=ke,ci[0]=me,ci[1]=fe,ci[2]=de,ci[3]=pe,ci[4]=ve,ci[5]=be,ci[6]=ye,fi[0]=Oe,fi[1]=he,fi[2]=Ce,fi[3]=je,fi[4]=Re,fi[5]=Ee,fi[6]=Be,s=1}else s=0}s&&(s=function(t,n){for(t.M=0;t.M<t.Va;++t.M){var a,s=t.Jc[t.M&t.Xb],l=t.m,u=t;for(a=0;a<u.za;++a){var c=l,h=u,f=h.Ac,d=h.Bc+4*a,p=h.zc,g=h.ya[h.aa+a];if(h.Qa.Bb?g.$b=k(c,h.Pa.jb[0])?2+k(c,h.Pa.jb[2]):k(c,h.Pa.jb[1]):g.$b=0,h.kc&&(g.Ad=k(c,h.Bd)),g.Za=!k(c,145)+0,g.Za){var m=g.Ob,v=0;for(h=0;4>h;++h){var b,y=p[0+h];for(b=0;4>b;++b){y=si[f[d+b]][y];for(var w=oi[k(c,y[0])];0<w;)w=oi[2*w+k(c,y[w])];y=-w,f[d+b]=y}r(m,v,f,d,4),v+=4,p[0+h]=y}}else y=k(c,156)?k(c,128)?1:3:k(c,163)?2:0,g.Ob[0]=y,i(f,d,y,4),i(p,0,y,4);g.Dd=k(c,142)?k(c,114)?k(c,183)?1:3:2:0}if(u.m.Ka)return Yt(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(u=s,c=(l=t).rb[l.sb-1],f=l.rb[l.sb+l.ja],a=l.ya[l.aa+l.ja],d=l.kc?a.Ad:0)c.la=f.la=0,a.Za||(c.Na=f.Na=0),a.Hc=0,a.Gc=0,a.ia=0;else{var x,N;if(c=f,f=u,d=l.Pa.Xc,p=l.ya[l.aa+l.ja],g=l.pb[p.$b],h=p.ad,m=0,v=l.rb[l.sb-1],y=b=0,i(h,m,0,384),p.Za)var L=0,A=d[3];else{w=o(16);var S=c.Na+v.Na;if(S=ni(f,d[1],S,g.Eb,0,w,0),c.Na=v.Na=(0<S)+0,1<S)ir(w,0,h,m);else{var _=w[0]+3>>3;for(w=0;256>w;w+=16)h[m+w]=_}L=1,A=d[0]}var P=15&c.la,F=15&v.la;for(w=0;4>w;++w){var C=1&F;for(_=N=0;4>_;++_)P=P>>1|(C=(S=ni(f,A,S=C+(1&P),g.Sc,L,h,m))>L)<<7,N=N<<2|(3<S?3:1<S?2:0!=h[m+0]),m+=16;P>>=4,F=F>>1|C<<7,b=(b<<8|N)>>>0}for(A=P,L=F>>4,x=0;4>x;x+=2){for(N=0,P=c.la>>4+x,F=v.la>>4+x,w=0;2>w;++w){for(C=1&F,_=0;2>_;++_)S=C+(1&P),P=P>>1|(C=0<(S=ni(f,d[2],S,g.Qc,0,h,m)))<<3,N=N<<2|(3<S?3:1<S?2:0!=h[m+0]),m+=16;P>>=2,F=F>>1|C<<5}y|=N<<4*x,A|=P<<4<<x,L|=(240&F)<<x}c.la=A,v.la=L,p.Hc=b,p.Gc=y,p.ia=43690&y?0:g.ia,d=!(b|y)}if(0<l.L&&(l.wa[l.Y+l.ja]=l.gd[a.$b][a.Za],l.wa[l.Y+l.ja].La|=!d),u.Ka)return Yt(t,7,"Premature end-of-file encountered.")}if(Zt(t),l=n,u=1,a=(s=t).D,c=0<s.L&&s.M>=s.zb&&s.M<=s.Va,0==s.Aa)e:{if(a.M=s.M,a.uc=c,On(s,a),u=1,a=(N=s.D).Nb,c=(y=Di[s.L])*s.R,f=y/2*s.B,w=16*a*s.R,_=8*a*s.B,d=s.sa,p=s.ta-c+w,g=s.qa,h=s.ra-f+_,m=s.Ha,v=s.Ia-f+_,F=0==(P=N.M),b=P>=s.Va-1,2==s.Aa&&On(s,N),N.uc)for(C=(S=s).D.M,e(S.D.uc),N=S.yb;N<S.Hb;++N){L=N,A=C;var j=(I=(U=S).D).Nb;x=U.R;var I=I.wa[I.Y+L],O=U.sa,E=U.ta+16*j*x+16*L,R=I.dd,B=I.tc;if(0!=B)if(e(3<=B),1==U.L)0<L&&yr(O,E,x,B+4),I.La&&xr(O,E,x,B),0<A&&br(O,E,x,B+4),I.La&&wr(O,E,x,B);else{var D=U.B,T=U.qa,M=U.ra+8*j*D+8*L,q=U.Ha,U=U.Ia+8*j*D+8*L;j=I.ld,0<L&&(hr(O,E,x,B+4,R,j),dr(T,M,q,U,D,B+4,R,j)),I.La&&(gr(O,E,x,B,R,j),vr(T,M,q,U,D,B,R,j)),0<A&&(cr(O,E,x,B+4,R,j),fr(T,M,q,U,D,B+4,R,j)),I.La&&(pr(O,E,x,B,R,j),mr(T,M,q,U,D,B,R,j))}}if(s.ia&&alert("todo:DitherRow"),null!=l.put){if(N=16*P,P=16*(P+1),F?(l.y=s.sa,l.O=s.ta+w,l.f=s.qa,l.N=s.ra+_,l.ea=s.Ha,l.W=s.Ia+_):(N-=y,l.y=d,l.O=p,l.f=g,l.N=h,l.ea=m,l.W=v),b||(P-=y),P>l.o&&(P=l.o),l.F=null,l.J=null,null!=s.Fa&&0<s.Fa.length&&N<P&&(l.J=hn(s,l,N,P-N),l.F=s.mb,null==l.F&&0==l.F.length)){u=Yt(s,3,"Could not decode alpha data.");break e}N<l.j&&(y=l.j-N,N=l.j,e(!(1&y)),l.O+=s.R*y,l.N+=s.B*(y>>1),l.W+=s.B*(y>>1),null!=l.F&&(l.J+=l.width*y)),N<P&&(l.O+=l.v,l.N+=l.v>>1,l.W+=l.v>>1,null!=l.F&&(l.J+=l.v),l.ka=N-l.j,l.U=l.va-l.v,l.T=P-N,u=l.put(l))}a+1!=s.Ic||b||(r(s.sa,s.ta-c,d,p+16*s.R,c),r(s.qa,s.ra-f,g,h+8*s.B,f),r(s.Ha,s.Ia-f,m,v+8*s.B,f))}if(!u)return Yt(t,6,"Output aborted.")}return 1}(t,n)),null!=n.bc&&n.bc(n),s&=1}return s?(t.cb=0,s):0})(t,l)||(n=t.a)}}else n=t.a}0==n&&null!=h.Oa&&h.Oa.fd&&(n=Bn(h.ba))}h=n}c=0!=h?null:11>c?f.f.RGBA.eb:f.f.kb.y}else c=null;return c};var qi=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function u(t,e){for(var n="",r=0;r<4;r++)n+=String.fromCharCode(t[e++]);return n}function c(t,e){return(t[e+0]|t[e+1]<<8|t[e+2]<<16)>>>0}function h(t,e){return(t[e+0]|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new l;var f=[0],d=[0],p=[],g=new l,m=t,v=function(t,e){var n={},r=0,i=!1,o=0,a=0;if(n.frames=[],!
/** @license
       * Copyright (c) 2017 Dominik Homberger
      Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
      The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
      THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
      https://webpjs.appspot.com
      WebPRiffParser <EMAIL>
      */
function(t,e){for(var n=0;n<4;n++)if(t[e+n]!="RIFF".charCodeAt(n))return!0;return!1}(t,e)){for(h(t,e+=4),e+=8;e<t.length;){var s=u(t,e),l=h(t,e+=4);e+=4;var f=l+(1&l);switch(s){case"VP8 ":case"VP8L":void 0===n.frames[r]&&(n.frames[r]={}),(g=n.frames[r]).src_off=i?a:e-8,g.src_size=o+l+8,r++,i&&(i=!1,o=0,a=0);break;case"VP8X":(g=n.header={}).feature_flags=t[e];var d=e+4;g.canvas_width=1+c(t,d),d+=3,g.canvas_height=1+c(t,d),d+=3;break;case"ALPH":i=!0,o=f+8,a=e-8;break;case"ANIM":(g=n.header).bgcolor=h(t,e),d=e+4,g.loop_count=(m=t)[(v=d)+0]|m[v+1]<<8,d+=2;break;case"ANMF":var p,g;(g=n.frames[r]={}).offset_x=2*c(t,e),e+=3,g.offset_y=2*c(t,e),e+=3,g.width=1+c(t,e),e+=3,g.height=1+c(t,e),e+=3,g.duration=c(t,e),e+=3,p=t[e++],g.dispose=1&p,g.blend=p>>1&1}"ANMF"!=s&&(e+=f)}var m,v;return n}}(m,0);v.response=m,v.rgbaoutput=!0,v.dataurl=!1;var b=v.header?v.header:null,y=v.frames?v.frames:null;if(b){b.loop_counter=b.loop_count,f=[b.canvas_height],d=[b.canvas_width];for(var w=0;w<y.length&&0!=y[w].blend;w++);}var x=y[0],N=g.WebPDecodeRGBA(m,x.src_off,x.src_size,d,f);x.rgba=N,x.imgwidth=d[0],x.imgheight=f[0];for(var L=0;L<d[0]*f[0]*4;L++)p[L]=N[L];return this.width=d,this.height=f,this.data=p,this}!function(t){var e=function(e,r,l,u){var c=4,h=o;switch(u){case t.image_compression.FAST:c=1,h=i;break;case t.image_compression.MEDIUM:c=6,h=a;break;case t.image_compression.SLOW:c=9,h=s}var f=pn(e=n(e,r,l,h),{level:c});return t.__addimage__.arrayBufferToBinaryString(f)},n=function(t,e,n,r){for(var i,o,a,s=t.length/e,l=new Uint8Array(t.length+s),h=u(),f=0;f<s;f+=1){if(a=f*e,i=t.subarray(a,a+e),r)l.set(r(i,n,o),a+f);else{for(var d,p=h.length,g=[];d<p;d+=1)g[d]=h[d](i,n,o);var m=c(g.concat());l.set(g[m],a+f)}o=i}return l},r=function(t){var e=Array.apply([],t);return e.unshift(0),e},i=function(t,e){var n,r=[],i=t.length;r[0]=1;for(var o=0;o<i;o+=1)n=t[o-e]||0,r[o+1]=t[o]-n+256&255;return r},o=function(t,e,n){var r,i=[],o=t.length;i[0]=2;for(var a=0;a<o;a+=1)r=n&&n[a]||0,i[a+1]=t[a]-r+256&255;return i},a=function(t,e,n){var r,i,o=[],a=t.length;o[0]=3;for(var s=0;s<a;s+=1)r=t[s-e]||0,i=n&&n[s]||0,o[s+1]=t[s]+256-(r+i>>>1)&255;return o},s=function(t,e,n){var r,i,o,a,s=[],u=t.length;s[0]=4;for(var c=0;c<u;c+=1)r=t[c-e]||0,i=n&&n[c]||0,o=n&&n[c-e]||0,a=l(r,i,o),s[c+1]=t[c]-a+256&255;return s},l=function(t,e,n){if(t===e&&e===n)return t;var r=Math.abs(e-n),i=Math.abs(t-n),o=Math.abs(t+e-n-n);return r<=i&&r<=o?t:i<=o?e:n},u=function(){return[r,i,o,a,s]},c=function(t){var e=t.map((function(t){return t.reduce((function(t,e){return t+Math.abs(e)}),0)}));return e.indexOf(Math.min.apply(null,e))};t.processPNG=function(n,r,i,o){var a,s,l,u,c,h,f,d,p,g,m,v,b,y,w,x=this.decode.FLATE_DECODE,N="";if(this.__addimage__.isArrayBuffer(n)&&(n=new Uint8Array(n)),this.__addimage__.isArrayBufferView(n)){if(n=(l=new ui(n)).imgData,s=l.bits,a=l.colorSpace,c=l.colors,-1!==[4,6].indexOf(l.colorType)){if(8===l.bits){p=(d=32==l.pixelBitlength?new Uint32Array(l.decodePixels().buffer):16==l.pixelBitlength?new Uint16Array(l.decodePixels().buffer):new Uint8Array(l.decodePixels().buffer)).length,m=new Uint8Array(p*l.colors),g=new Uint8Array(p);var L,A=l.pixelBitlength-l.bits;for(y=0,w=0;y<p;y++){for(b=d[y],L=0;L<A;)m[w++]=b>>>L&255,L+=l.bits;g[y]=b>>>L&255}}if(16===l.bits){p=(d=new Uint32Array(l.decodePixels().buffer)).length,m=new Uint8Array(p*(32/l.pixelBitlength)*l.colors),g=new Uint8Array(p*(32/l.pixelBitlength)),v=l.colors>1,y=0,w=0;for(var S=0;y<p;)b=d[y++],m[w++]=b>>>0&255,v&&(m[w++]=b>>>16&255,b=d[y++],m[w++]=b>>>0&255),g[S++]=b>>>16&255;s=8}o!==t.image_compression.NONE?(n=e(m,l.width*l.colors,l.colors,o),f=e(g,l.width,1,o)):(n=m,f=g,x=void 0)}if(3===l.colorType&&(a=this.color_spaces.INDEXED,h=l.palette,l.transparency.indexed)){var _=l.transparency.indexed,P=0;for(y=0,p=_.length;y<p;++y)P+=_[y];if((P/=255)==p-1&&-1!==_.indexOf(0))u=[_.indexOf(0)];else if(P!==p){for(d=l.decodePixels(),g=new Uint8Array(d.length),y=0,p=d.length;y<p;y++)g[y]=_[d[y]];f=e(g,l.width,1)}}var k=function(e){var n;switch(e){case t.image_compression.FAST:n=11;break;case t.image_compression.MEDIUM:n=13;break;case t.image_compression.SLOW:n=14;break;default:n=12}return n}(o);return x===this.decode.FLATE_DECODE&&(N="/Predictor "+k+" "),N+="/Colors "+c+" /BitsPerComponent "+s+" /Columns "+l.width,(this.__addimage__.isArrayBuffer(n)||this.__addimage__.isArrayBufferView(n))&&(n=this.__addimage__.arrayBufferToBinaryString(n)),(f&&this.__addimage__.isArrayBuffer(f)||this.__addimage__.isArrayBufferView(f))&&(f=this.__addimage__.arrayBufferToBinaryString(f)),{alias:i,data:n,index:r,filter:x,decodeParameters:N,transparency:u,palette:h,sMask:f,predictor:k,width:l.width,height:l.height,bitsPerComponent:s,colorSpace:a}}}}(Xn.API),function(t){t.processGIF89A=function(e,n,r,i){var o=new ci(e),a=o.width,s=o.height,l=[];o.decodeAndBlitFrameRGBA(0,l);var u={data:l,width:a,height:s},c=new fi(100).encode(u,100);return t.processJPEG.call(this,c,n,r,i)},t.processGIF87A=t.processGIF89A}(Xn.API),di.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var n=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:r,blue:n,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},di.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(n){yn.log("bit decode error:"+n)}},di.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),n=e%4;for(t=this.height-1;t>=0;t--){for(var r=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var o=this.datav.getUint8(this.pos++,!0),a=r*this.width*4+8*i*4,s=0;s<8&&8*i+s<this.width;s++){var l=this.palette[o>>7-s&1];this.data[a+4*s]=l.blue,this.data[a+4*s+1]=l.green,this.data[a+4*s+2]=l.red,this.data[a+4*s+3]=255}0!==n&&(this.pos+=4-n)}},di.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<t;i++){var o=this.datav.getUint8(this.pos++,!0),a=r*this.width*4+2*i*4,s=o>>4,l=15&o,u=this.palette[s];if(this.data[a]=u.blue,this.data[a+1]=u.green,this.data[a+2]=u.red,this.data[a+3]=255,2*i+1>=this.width)break;u=this.palette[l],this.data[a+4]=u.blue,this.data[a+4+1]=u.green,this.data[a+4+2]=u.red,this.data[a+4+3]=255}0!==e&&(this.pos+=4-e)}},di.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var i=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+4*r;if(i<this.palette.length){var a=this.palette[i];this.data[o]=a.red,this.data[o+1]=a.green,this.data[o+2]=a.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}0!==t&&(this.pos+=4-t)}},di.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<this.width;i++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var a=(o&e)/e*255|0,s=(o>>5&e)/e*255|0,l=(o>>10&e)/e*255|0,u=o>>15?255:0,c=r*this.width*4+4*i;this.data[c]=l,this.data[c+1]=s,this.data[c+2]=a,this.data[c+3]=u}this.pos+=t}},di.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,o=0;o<this.width;o++){var a=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(a&e)/e*255|0,l=(a>>5&n)/n*255|0,u=(a>>11)/e*255|0,c=i*this.width*4+4*o;this.data[c]=u,this.data[c+1]=l,this.data[c+2]=s,this.data[c+3]=255}this.pos+=t}},di.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=e*this.width*4+4*n;this.data[a]=o,this.data[a+1]=i,this.data[a+2]=r,this.data[a+3]=255}this.pos+=this.width%4}},di.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*n;this.data[s]=o,this.data[s+1]=i,this.data[s+2]=r,this.data[s+3]=a}},di.prototype.getData=function(){return this.data},
/**
 * @license
 * Copyright (c) 2018 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.processBMP=function(e,n,r,i){var o=new di(e,!1),a=o.width,s=o.height,l={data:o.getData(),width:a,height:s},u=new fi(100).encode(l,100);return t.processJPEG.call(this,u,n,r,i)}}(Xn.API),pi.prototype.getData=function(){return this.data},
/**
 * @license
 * Copyright (c) 2019 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.processWEBP=function(e,n,r,i){var o=new pi(e),a=o.width,s=o.height,l={data:o.getData(),width:a,height:s},u=new fi(100).encode(l,100);return t.processJPEG.call(this,u,n,r,i)}}(Xn.API),Xn.API.processRGBA=function(t,e,n){for(var r=t.data,i=r.length,o=new Uint8Array(i/4*3),a=new Uint8Array(i/4),s=0,l=0,u=0;u<i;u+=4){var c=r[u],h=r[u+1],f=r[u+2],d=r[u+3];o[s++]=c,o[s++]=h,o[s++]=f,a[l++]=d}var p=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(a),data:p,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},Xn.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!=={af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",(function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")})),this.internal.languageSettings.isSubscribed=!0)),this},ii=Xn.API,oi=ii.getCharWidthsArray=function(t,e){var n,r,i=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),a=e.charSpace||this.internal.getCharSpace(),s=e.widths?e.widths:i.metadata.Unicode.widths,l=s.fof?s.fof:1,u=e.kerning?e.kerning:i.metadata.Unicode.kerning,c=u.fof?u.fof:1,h=!1!==e.doKerning,f=0,d=t.length,p=0,g=s[0]||l,m=[];for(n=0;n<d;n++)r=t.charCodeAt(n),"function"==typeof i.metadata.widthOfString?m.push((i.metadata.widthOfGlyph(i.metadata.characterToGlyph(r))+a*(1e3/o)||0)/1e3):(f=h&&"object"===Le(u[r])&&!isNaN(parseInt(u[r][p],10))?u[r][p]/c:0,m.push((s[r]||g)/l+f)),p=r;return m},ai=ii.getStringUnitWidth=function(t,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return ii.processArabic&&(t=ii.processArabic(t)),"function"==typeof r.metadata.widthOfString?r.metadata.widthOfString(t,n,i)/n:oi.apply(this,arguments).reduce((function(t,e){return t+e}),0)},si=function(t,e,n,r){for(var i=[],o=0,a=t.length,s=0;o!==a&&s+e[o]<n;)s+=e[o],o++;i.push(t.slice(0,o));var l=o;for(s=0;o!==a;)s+e[o]>r&&(i.push(t.slice(l,o)),s=0,l=o),s+=e[o],o++;return l!==o&&i.push(t.slice(l,o)),i},li=function(t,e,n){n||(n={});var r,i,o,a,s,l,u,c=[],h=[c],f=n.textIndent||0,d=0,p=0,g=t.split(" "),m=oi.apply(this,[" ",n])[0];if(l=-1===n.lineIndent?g[0].length+2:n.lineIndent||0){var v=Array(l).join(" "),b=[];g.map((function(t){(t=t.split(/\s*\n/)).length>1?b=b.concat(t.map((function(t,e){return(e&&t.length?"\n":"")+t}))):b.push(t[0])})),g=b,l=ai.apply(this,[v,n])}for(o=0,a=g.length;o<a;o++){var y=0;if(r=g[o],l&&"\n"==r[0]&&(r=r.substr(1),y=1),f+d+(p=(i=oi.apply(this,[r,n])).reduce((function(t,e){return t+e}),0))>e||y){if(p>e){for(s=si.apply(this,[r,i,e-(f+d),e]),c.push(s.shift()),c=[s.pop()];s.length;)h.push([s.shift()]);p=i.slice(r.length-(c[0]?c[0].length:0)).reduce((function(t,e){return t+e}),0)}else c=[r];h.push(c),f=p+l,d=m}else c.push(r),f+=d+p,d=m}return u=l?function(t,e){return(e?v:"")+t.join(" ")}:function(t){return t.join(" ")},h.map(u)},ii.splitTextToSize=function(t,e,n){var r,i=(n=n||{}).fontSize||this.internal.getFontSize(),o=function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle);return e.metadata.Unicode?{widths:e.metadata.Unicode.widths||{0:1},kerning:e.metadata.Unicode.kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}.call(this,n);r=Array.isArray(t)?t:String(t).split(/\r?\n/);var a=1*this.internal.scaleFactor*e/i;o.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/i:0,o.lineIndent=n.lineIndent;var s,l,u=[];for(s=0,l=r.length;s<l;s++)u=u.concat(li.apply(this,[r[s],a,o]));return u},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},r={},i=0;i<16;i++)n[e[i]]="0123456789abcdef"[i],r["0123456789abcdef"[i]]=e[i];var o=function(t){return"0x"+parseInt(t,10).toString(16)},a=t.__fontmetrics__.compress=function(t){var e,n,i,s,l=["{"];for(var u in t){if(e=t[u],isNaN(parseInt(u,10))?n="'"+u+"'":(u=parseInt(u,10),n=(n=o(u).slice(2)).slice(0,-1)+r[n.slice(-1)]),"number"==typeof e)e<0?(i=o(e).slice(3),s="-"):(i=o(e).slice(2),s=""),i=s+i.slice(0,-1)+r[i.slice(-1)];else{if("object"!==Le(e))throw new Error("Don't know what to do with value type "+Le(e)+".");i=a(e)}l.push(n+i)}return l.push("}"),l.join("")},s=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw new Error("Invalid argument passed to uncompress.");for(var e,r,i,o,a={},s=1,l=a,u=[],c="",h="",f=t.length-1,d=1;d<f;d+=1)"'"==(o=t[d])?e?(i=e.join(""),e=void 0):e=[]:e?e.push(o):"{"==o?(u.push([l,i]),l={},i=void 0):"}"==o?((r=u.pop())[0][r[1]]=l,i=void 0,l=r[0]):"-"==o?s=-1:void 0===i?n.hasOwnProperty(o)?(c+=n[o],i=parseInt(c,16)*s,s=1,c=""):c+=o:n.hasOwnProperty(o)?(h+=n[o],l[i]=parseInt(h,16)*s,s=1,i=void 0,h=""):h+=o;return a},l={codePages:["WinAnsiEncoding"],WinAnsiEncoding:s("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},u={Unicode:{Courier:l,"Courier-Bold":l,"Courier-BoldOblique":l,"Courier-Oblique":l,Helvetica:l,"Helvetica-Bold":l,"Helvetica-BoldOblique":l,"Helvetica-Oblique":l,"Times-Roman":l,"Times-Bold":l,"Times-BoldItalic":l,"Times-Italic":l}},c={Unicode:{"Courier-Oblique":s("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":s("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":s("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:s("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":s("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":s("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:s("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:s("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":s("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:s("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":s("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":s("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":s("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":s("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,n=c.Unicode[e.postScriptName];n&&(e.metadata.Unicode={},e.metadata.Unicode.widths=n.widths,e.metadata.Unicode.kerning=n.kerning);var r=u.Unicode[e.postScriptName];r&&(e.metadata.Unicode.encoding=r,e.encoding=r.codePages[0])}])}(Xn.API),
/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n};t.API.events.push(["addFont",function(n){var r,i,o=void 0,a=n.font,s=n.instance;if(!a.isStandardFont){if(void 0===s)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+a.postScriptName+"').");if("string"!=typeof(o=!1===s.existsFileInVFS(a.postScriptName)?s.loadFile(a.postScriptName):s.getFileFromVFS(a.postScriptName)))throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+a.postScriptName+"').");r=a,i=/^\x00\x01\x00\x00/.test(i=o)?e(i):e(Ln(i)),r.metadata=t.API.TTFFont.open(i),r.metadata.Unicode=r.metadata.Unicode||{encoding:{},kerning:{},widths:[]},r.metadata.glyIdsUsed=[0]}}])}(Xn),Xn.API.addSvgAsImage=function(t,e,n,r,i,o,a,s){if(isNaN(e)||isNaN(n))throw yn.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(r)||isNaN(i))throw yn.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var l=document.createElement("canvas");l.width=r,l.height=i;var u=l.getContext("2d");u.fillStyle="#fff",u.fillRect(0,0,l.width,l.height);var c={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},h=this;return(vn.canvg?Promise.resolve(vn.canvg):Ne((()=>import("./index.es-CVIejru_.js")),__vite__mapDeps([0,1]))).catch((function(t){return Promise.reject(new Error("Could not load canvg: "+t))})).then((function(t){return t.default?t.default:t})).then((function(e){return e.fromString(u,t,c)}),(function(){return Promise.reject(new Error("Could not load canvg."))})).then((function(t){return t.render(c)})).then((function(){h.addImage(l.toDataURL("image/jpeg",1),e,n,r,i,a,s)}))},Xn.API.putTotalPages=function(t){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var i=0;i<this.internal.pages[r].length;i++)this.internal.pages[r][i]=this.internal.pages[r][i].replace(e,n);return this},Xn.API.viewerPreferences=function(t,e){var n;t=t||{},e=e||!1;var r,i,o,a={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},s=Object.keys(a),l=[],u=0,c=0,h=0;function f(t,e){var n,r=!1;for(n=0;n<t.length;n+=1)t[n]===e&&(r=!0);return r}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(a)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var d=s.length;for(h=0;h<d;h+=1)n[s[h]].value=n[s[h]].defaultValue,n[s[h]].explicitSet=!1}if("object"===Le(t))for(i in t)if(o=t[i],f(s,i)&&void 0!==o){if("boolean"===n[i].type&&"boolean"==typeof o)n[i].value=o;else if("name"===n[i].type&&f(n[i].valueSet,o))n[i].value=o;else if("integer"===n[i].type&&Number.isInteger(o))n[i].value=o;else if("array"===n[i].type){for(u=0;u<o.length;u+=1)if(r=!0,1===o[u].length&&"number"==typeof o[u][0])l.push(String(o[u]-1));else if(o[u].length>1){for(c=0;c<o[u].length;c+=1)"number"!=typeof o[u][c]&&(r=!1);!0===r&&l.push([o[u][0]-1,o[u][1]-1].join(" "))}n[i].value="["+l.join(" ")+"]"}else n[i].value=n[i].defaultValue;n[i].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",(function(){var t,e=[];for(t in n)!0===n[t].explicitSet&&("name"===n[t].type?e.push("/"+t+" /"+n[t].value):e.push("/"+t+" "+n[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")})),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this},
/** ====================================================================
 * @license
 * jsPDF XMP metadata plugin
 * Copyright (c) 2016 Jussi Utunen, <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */
function(t){var e=function(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),n=unescape(encodeURIComponent(t)),r=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),o=unescape(encodeURIComponent("</x:xmpmeta>")),a=n.length+r.length+i.length+e.length+o.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+a+" >>"),this.internal.write("stream"),this.internal.write(e+n+r+i+o),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(t,r){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:r||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(Xn.API),function(t){var e=t.API,n=e.pdfEscape16=function(t,e){for(var n,r=e.metadata.Unicode.widths,i=["","0","00","000","0000"],o=[""],a=0,s=t.length;a<s;++a){if(n=e.metadata.characterToGlyph(t.charCodeAt(a)),e.metadata.glyIdsUsed.push(n),e.metadata.toUnicode[n]=t.charCodeAt(a),-1==r.indexOf(n)&&(r.push(n),r.push([parseInt(e.metadata.widthOfGlyph(n),10)])),"0"==n)return o.join("");n=n.toString(16),o.push(i[4-n.length],n)}return o.join("")},r=function(t){var e,n,r,i,o,a,s;for(o="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",r=[],a=0,s=(n=Object.keys(t).sort((function(t,e){return t-e}))).length;a<s;a++)e=n[a],r.length>=100&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),r.push("<"+e+"><"+i+">"));return r.length&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),o+"endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,o=e.newObject,a=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"Identity-H"===n.encoding){for(var s=n.metadata.Unicode.widths,l=n.metadata.subset.encode(n.metadata.glyIdsUsed,1),u="",c=0;c<l.length;c++)u+=String.fromCharCode(l[c]);var h=o();a({data:u,addLength1:!0,objectId:h}),i("endobj");var f=o();a({data:r(n.metadata.toUnicode),addLength1:!0,objectId:f}),i("endobj");var d=o();i("<<"),i("/Type /FontDescriptor"),i("/FontName /"+Hn(n.fontName)),i("/FontFile2 "+h+" 0 R"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/Flags "+n.metadata.flags),i("/StemV "+n.metadata.stemV),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i(">>"),i("endobj");var p=o();i("<<"),i("/Type /Font"),i("/BaseFont /"+Hn(n.fontName)),i("/FontDescriptor "+d+" 0 R"),i("/W "+t.API.PDFObject.convert(s)),i("/CIDToGIDMap /Identity"),i("/DW 1000"),i("/Subtype /CIDFontType2"),i("/CIDSystemInfo"),i("<<"),i("/Supplement 0"),i("/Registry (Adobe)"),i("/Ordering ("+n.encoding+")"),i(">>"),i(">>"),i("endobj"),n.objectNumber=o(),i("<<"),i("/Type /Font"),i("/Subtype /Type0"),i("/ToUnicode "+f+" 0 R"),i("/BaseFont /"+Hn(n.fontName)),i("/Encoding /"+n.encoding),i("/DescendantFonts ["+p+" 0 R]"),i(">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]),e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,o=e.newObject,a=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"WinAnsiEncoding"===n.encoding){for(var s=n.metadata.rawData,l="",u=0;u<s.length;u++)l+=String.fromCharCode(s[u]);var c=o();a({data:l,addLength1:!0,objectId:c}),i("endobj");var h=o();a({data:r(n.metadata.toUnicode),addLength1:!0,objectId:h}),i("endobj");var f=o();i("<<"),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i("/StemV "+n.metadata.stemV),i("/Type /FontDescriptor"),i("/FontFile2 "+c+" 0 R"),i("/Flags 96"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/FontName /"+Hn(n.fontName)),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i(">>"),i("endobj"),n.objectNumber=o();for(var d=0;d<n.metadata.hmtx.widths.length;d++)n.metadata.hmtx.widths[d]=parseInt(n.metadata.hmtx.widths[d]*(1e3/n.metadata.head.unitsPerEm));i("<</Subtype/TrueType/Type/Font/ToUnicode "+h+" 0 R/BaseFont/"+Hn(n.fontName)+"/FontDescriptor "+f+" 0 R/Encoding/"+n.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(n.metadata.hmtx.widths)+">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]);var i=function(t){var e,r=t.text||"",i=t.x,o=t.y,a=t.options||{},s=t.mutex||{},l=s.pdfEscape,u=s.activeFontKey,c=s.fonts,h=u,f="",d=0,p="",g=c[h].encoding;if("Identity-H"!==c[h].encoding)return{text:r,x:i,y:o,options:a,mutex:s};for(p=r,h=u,Array.isArray(r)&&(p=r[0]),d=0;d<p.length;d+=1)c[h].metadata.hasOwnProperty("cmap")&&(e=c[h].metadata.cmap.unicode.codeMap[p[d].charCodeAt(0)]),e||p[d].charCodeAt(0)<256&&c[h].metadata.hasOwnProperty("Unicode")?f+=p[d]:f+="";var m="";return parseInt(h.slice(1))<14||"WinAnsiEncoding"===g?m=l(f,h).split("").map((function(t){return t.charCodeAt(0).toString(16)})).join(""):"Identity-H"===g&&(m=n(f,c[h])),s.isHex=!0,{text:m,x:i,y:o,options:a,mutex:s}};e.events.push(["postProcessText",function(t){var e=t.text||"",n=[],r={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var o=0;for(o=0;o<e.length;o+=1)Array.isArray(e[o])&&3===e[o].length?n.push([i(Object.assign({},r,{text:e[o][0]})).text,e[o][1],e[o][2]]):n.push(i(Object.assign({},r,{text:e[o]})).text);t.text=n}else t.text=i(Object.assign({},r,{text:e})).text}])}(Xn),
/**
 * @license
 * jsPDF virtual FileSystem functionality
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]},t.addFileToVFS=function(t,n){return e.call(this),this.internal.vFS[t]=n,this},t.getFileFromVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null}}(Xn.API),
/**
 * @license
 * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
 * MIT License
 */
function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var n,r,i,o,a,s,l,u=e,c=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],h=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],f={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},d={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},p=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],g=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),m=!1,v=0;this.__bidiEngine__={};var b=function(t){var e=t.charCodeAt(),n=e>>8,r=d[n];return void 0!==r?u[256*r+(255&e)]:252===n||253===n?"AL":g.test(n)?"L":8===n?"R":"N"},y=function(t){for(var e,n=0;n<t.length;n++){if("L"===(e=b(t.charAt(n))))return!1;if("R"===e)return!0}return!1},w=function(t,e,a,s){var l,u,c,h,f=e[s];switch(f){case"L":case"R":case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":m=!1;break;case"N":case"AN":break;case"EN":m&&(f="AN");break;case"AL":m=!0,f="R";break;case"WS":case"BN":f="N";break;case"CS":s<1||s+1>=e.length||"EN"!==(l=a[s-1])&&"AN"!==l||"EN"!==(u=e[s+1])&&"AN"!==u?f="N":m&&(u="AN"),f=u===l?u:"N";break;case"ES":f="EN"===(l=s>0?a[s-1]:"B")&&s+1<e.length&&"EN"===e[s+1]?"EN":"N";break;case"ET":if(s>0&&"EN"===a[s-1]){f="EN";break}if(m){f="N";break}for(c=s+1,h=e.length;c<h&&"ET"===e[c];)c++;f=c<h&&"EN"===e[c]?"EN":"N";break;case"NSM":if(i&&!o){for(h=e.length,c=s+1;c<h&&"NSM"===e[c];)c++;if(c<h){var d=t[s],p=d>=1425&&d<=2303||64286===d;if(l=e[c],p&&("R"===l||"AL"===l)){f="R";break}}}f=s<1||"B"===(l=e[s-1])?"N":a[s-1];break;case"B":m=!1,n=!0,f=v;break;case"S":r=!0,f="N"}return f},x=function(t,e,n){var r=t.split("");return n&&N(r,n,{hiLevel:v}),r.reverse(),e&&e.reverse(),r.join("")},N=function(t,e,i){var o,a,s,l,u,d=-1,p=t.length,g=0,y=[],x=v?h:c,N=[];for(m=!1,n=!1,r=!1,a=0;a<p;a++)N[a]=b(t[a]);for(s=0;s<p;s++){if(u=g,y[s]=w(t,N,y,s),o=240&(g=x[u][f[y[s]]]),g&=15,e[s]=l=x[g][5],o>0)if(16===o){for(a=d;a<s;a++)e[a]=1;d=-1}else d=-1;if(x[g][6])-1===d&&(d=s);else if(d>-1){for(a=d;a<s;a++)e[a]=l;d=-1}"B"===N[s]&&(e[s]=0),i.hiLevel|=l}r&&function(t,e,n){for(var r=0;r<n;r++)if("S"===t[r]){e[r]=v;for(var i=r-1;i>=0&&"WS"===t[i];i--)e[i]=v}}(N,e,p)},L=function(t,e,r,i,o){if(!(o.hiLevel<t)){if(1===t&&1===v&&!n)return e.reverse(),void(r&&r.reverse());for(var a,s,l,u,c=e.length,h=0;h<c;){if(i[h]>=t){for(l=h+1;l<c&&i[l]>=t;)l++;for(u=h,s=l-1;u<s;u++,s--)a=e[u],e[u]=e[s],e[s]=a,r&&(a=r[u],r[u]=r[s],r[s]=a);h=l}h++}}},A=function(t,e,n){var r=t.split(""),i={hiLevel:v};return n||(n=[]),N(r,n,i),function(t,e,n){if(0!==n.hiLevel&&l)for(var r,i=0;i<t.length;i++)1===e[i]&&(r=p.indexOf(t[i]))>=0&&(t[i]=p[r+1])}(r,n,i),L(2,r,e,n,i),L(1,r,e,n,i),r.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,n){if(function(t,e){if(e)for(var n=0;n<t.length;n++)e[n]=n;void 0===o&&(o=y(t)),void 0===s&&(s=y(t))}(t,e),i||!a||s)if(i&&a&&o^s)v=o?1:0,t=x(t,e,n);else if(!i&&a&&s)v=o?1:0,t=A(t,e,n),t=x(t,e);else if(!i||o||a||s){if(i&&!a&&o^s)t=x(t,e),o?(v=0,t=A(t,e,n)):(v=1,t=A(t,e,n),t=x(t,e));else if(i&&o&&!a&&s)v=1,t=A(t,e,n),t=x(t,e);else if(!i&&!a&&o^s){var r=l;o?(v=1,t=A(t,e,n),v=0,l=!1,t=A(t,e,n),l=r):(v=0,t=A(t,e,n),t=x(t,e),v=1,l=!1,t=A(t,e,n),l=r,t=x(t,e))}}else v=0,t=A(t,e,n);else v=o?1:0,t=A(t,e,n);return t},this.__bidiEngine__.setOptions=function(t){t&&(i=t.isInputVisual,a=t.isOutputVisual,o=t.isInputRtl,s=t.isOutputRtl,l=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text;t.x,t.y;var r=t.options||{};t.mutex,r.lang;var i=[];if(r.isInputVisual="boolean"!=typeof r.isInputVisual||r.isInputVisual,n.setOptions(r),"[object Array]"===Object.prototype.toString.call(e)){var o=0;for(i=[],o=0;o<e.length;o+=1)"[object Array]"===Object.prototype.toString.call(e[o])?i.push([n.doBidiReorder(e[o][0]),e[o][1],e[o][2]]):i.push([n.doBidiReorder(e[o])]);t.text=i}else t.text=n.doBidiReorder(e);n.setOptions({isInputVisual:!0})}])}(Xn),Xn.API.TTFFont=function(){function t(t){var e;if(this.rawData=t,e=this.contents=new mi(t),this.contents.pos=4,"ttcf"===e.readString(4))throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new Ri(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new vi(this.contents),this.head=new wi(this),this.name=new Pi(this),this.cmap=new Ni(this),this.toUnicode={},this.hhea=new Li(this),this.maxp=new ki(this),this.hmtx=new Fi(this),this.post=new Si(this),this.os2=new Ai(this),this.loca=new Ei(this),this.glyf=new ji(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var t,e,n,r,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var e,n,r,i;for(i=[],e=0,n=(r=this.bbox).length;e<n;e++)t=r[e],i.push(Math.round(t*this.scaleFactor));return i}.call(this),this.stemV=0,this.post.exists?(n=255&(r=this.post.italic_angle),!!(32768&(e=r>>16))&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+n)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,n){var r,i,o,a;for(o=0,i=0,a=(t=""+t).length;0<=a?i<a:i>a;i=0<=a?++i:--i)r=t.charCodeAt(i),o+=this.widthOfGlyph(this.characterToGlyph(r))+n*(1e3/e)||0;return o*(e/1e3)},t.prototype.lineHeight=function(t,e){var n;return null==e&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},t}();var gi,mi=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},t.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)n[e]=String.fromCharCode(this.readByte());return n.join("")},t.prototype.writeString=function(t){var e,n,r;for(r=[],e=0,n=t.length;0<=n?e<n:e>n;e=0<=n?++e:--e)r.push(this.writeByte(t.charCodeAt(e)));return r},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,n,r,i,o,a,s;return t=this.readByte(),e=this.readByte(),n=this.readByte(),r=this.readByte(),i=this.readByte(),o=this.readByte(),a=this.readByte(),s=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^r)+16777216*(255^i)+65536*(255^o)+256*(255^a)+(255^s)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*r+16777216*i+65536*o+256*a+s},t.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=**********&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:n>t;n=0<=t?++n:--n)e.push(this.readByte());return e},t.prototype.write=function(t){var e,n,r,i;for(i=[],n=0,r=t.length;n<r;n++)e=t[n],i.push(this.writeByte(e));return i},t}(),vi=function(){var t;function e(t){var e,n,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},n=0,r=this.tableCount;0<=r?n<r:n>r;n=0<=r?++n:--n)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return e.prototype.encode=function(e){var n,r,i,o,a,s,l,u,c,h,f,d,p;for(p in f=Object.keys(e).length,s=Math.log(2),c=16*Math.floor(Math.log(f)/s),o=Math.floor(c/s),u=16*f-c,(r=new mi).writeInt(this.scalarType),r.writeShort(f),r.writeShort(c),r.writeShort(o),r.writeShort(u),i=16*f,l=r.pos+i,a=null,d=[],e)for(h=e[p],r.writeString(p),r.writeInt(t(h)),r.writeInt(l),r.writeInt(h.length),d=d.concat(h),"head"===p&&(a=l),l+=h.length;l%4;)d.push(0),l++;return r.write(d),n=2981146554-t(r.data),r.pos=a+8,r.writeUInt32(n),r.data},t=function(t){var e,n,r,i;for(t=Ci.call(t);t.length%4;)t.push(0);for(r=new mi(t),n=0,e=0,i=t.length;e<i;e=e+=4)n+=r.readUInt32();return **********&n},e}(),bi={}.hasOwnProperty,yi=function(t,e){for(var n in e)bi.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t};gi=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var wi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="head",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},t.prototype.encode=function(t){var e;return(e=new mi).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},t}(),xi=function(){function t(t,e){var n,r,i,o,a,s,l,u,c,h,f,d,p,g,m,v,b;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),c=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=0;s<256;++s)this.codeMap[s]=t.readByte();break;case 4:for(f=t.readUInt16(),h=f/2,t.pos+=6,i=function(){var e,n;for(n=[],s=e=0;0<=h?e<h:e>h;s=0<=h?++e:--e)n.push(t.readUInt16());return n}(),t.pos+=2,p=function(){var e,n;for(n=[],s=e=0;0<=h?e<h:e>h;s=0<=h?++e:--e)n.push(t.readUInt16());return n}(),l=function(){var e,n;for(n=[],s=e=0;0<=h?e<h:e>h;s=0<=h?++e:--e)n.push(t.readUInt16());return n}(),u=function(){var e,n;for(n=[],s=e=0;0<=h?e<h:e>h;s=0<=h?++e:--e)n.push(t.readUInt16());return n}(),r=(this.length-t.pos+this.offset)/2,a=function(){var e,n;for(n=[],s=e=0;0<=r?e<r:e>r;s=0<=r?++e:--e)n.push(t.readUInt16());return n}(),s=m=0,b=i.length;m<b;s=++m)for(g=i[s],n=v=d=p[s];d<=g?v<=g:v>=g;n=d<=g?++v:--v)0===u[s]?o=n+l[s]:0!==(o=a[u[s]/2+(n-d)-(h-s)]||0)&&(o+=l[s]),this.codeMap[n]=65535&o}t.pos=c}return t.encode=function(t,e){var n,r,i,o,a,s,l,u,c,h,f,d,p,g,m,v,b,y,w,x,N,L,A,S,_,P,k,F,C,j,I,O,E,R,B,D,T,M,q,U,z,W,H,V,G,J;switch(F=new mi,o=Object.keys(t).sort((function(t,e){return t-e})),e){case"macroman":for(p=0,g=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),v={0:0},i={},C=0,E=o.length;C<E;C++)null==v[H=t[r=o[C]]]&&(v[H]=++p),i[r]={old:t[r],new:v[t[r]]},g[r]=v[t[r]];return F.writeUInt16(1),F.writeUInt16(0),F.writeUInt32(12),F.writeUInt16(0),F.writeUInt16(262),F.writeUInt16(0),F.write(g),{charMap:i,subtable:F.data,maxGlyphID:p+1};case"unicode":for(P=[],c=[],b=0,v={},n={},m=l=null,j=0,R=o.length;j<R;j++)null==v[w=t[r=o[j]]]&&(v[w]=++b),n[r]={old:w,new:v[w]},a=v[w]-r,null!=m&&a===l||(m&&c.push(m),P.push(r),l=a),m=r;for(m&&c.push(m),c.push(65535),P.push(65535),S=2*(A=P.length),L=2*Math.pow(Math.log(A)/Math.LN2,2),h=Math.log(L/2)/Math.LN2,N=2*A-L,s=[],x=[],f=[],d=I=0,B=P.length;I<B;d=++I){if(_=P[d],u=c[d],65535===_){s.push(0),x.push(0);break}if(_-(k=n[_].new)>=32768)for(s.push(0),x.push(2*(f.length+A-d)),r=O=_;_<=u?O<=u:O>=u;r=_<=u?++O:--O)f.push(n[r].new);else s.push(k-_),x.push(0)}for(F.writeUInt16(3),F.writeUInt16(1),F.writeUInt32(12),F.writeUInt16(4),F.writeUInt16(16+8*A+2*f.length),F.writeUInt16(0),F.writeUInt16(S),F.writeUInt16(L),F.writeUInt16(h),F.writeUInt16(N),z=0,D=c.length;z<D;z++)r=c[z],F.writeUInt16(r);for(F.writeUInt16(0),W=0,T=P.length;W<T;W++)r=P[W],F.writeUInt16(r);for(V=0,M=s.length;V<M;V++)a=s[V],F.writeUInt16(a);for(G=0,q=x.length;G<q;G++)y=x[G],F.writeUInt16(y);for(J=0,U=f.length;J<U;J++)p=f[J],F.writeUInt16(p);return{charMap:n,subtable:F.data,maxGlyphID:b+1}}},t}(),Ni=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="cmap",t.prototype.parse=function(t){var e,n,r;for(t.pos=this.offset,this.version=t.readUInt16(),r=t.readUInt16(),this.tables=[],this.unicode=null,n=0;0<=r?n<r:n>r;n=0<=r?++n:--n)e=new xi(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},t.encode=function(t,e){var n,r;return null==e&&(e="macroman"),n=xi.encode(t,e),(r=new mi).writeUInt16(0),r.writeUInt16(1),n.table=r.data.concat(n.subtable),n},t}(),Li=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="hhea",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},t}(),Ai=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="OS/2",t.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var e,n;for(n=[],e=0;e<10;++e)n.push(t.readByte());return n}(),this.charRange=function(){var e,n;for(n=[],e=0;e<4;++e)n.push(t.readInt());return n}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var e,n;for(n=[],e=0;e<2;e=++e)n.push(t.readInt());return n}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},t}(),Si=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="post",t.prototype.parse=function(t){var e,n,r;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:case 196608:break;case 131072:var i;for(n=t.readUInt16(),this.glyphNameIndex=[],i=0;0<=n?i<n:i>n;i=0<=n?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],r=[];t.pos<this.offset+this.length;)e=t.readByte(),r.push(this.names.push(t.readString(e)));return r;case 151552:return n=t.readUInt16(),this.offsets=t.read(n);case 262144:return this.map=function(){var e,n,r;for(r=[],i=e=0,n=this.file.maxp.numGlyphs;0<=n?e<n:e>n;i=0<=n?++e:--e)r.push(t.readUInt32());return r}.call(this)}},t}(),_i=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},Pi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="name",t.prototype.parse=function(t){var e,n,r,i,o,a,s,l,u,c,h;for(t.pos=this.offset,t.readShort(),e=t.readShort(),a=t.readShort(),n=[],i=0;0<=e?i<e:i>e;i=0<=e?++i:--i)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+a+t.readShort()});for(s={},i=u=0,c=n.length;u<c;i=++u)r=n[i],t.pos=r.offset,l=t.readString(r.length),o=new _i(l,r),null==s[h=r.nameID]&&(s[h]=[]),s[r.nameID].push(o);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(f){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},t}(),ki=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="maxp",t.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},t}(),Fi=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="hmtx",t.prototype.parse=function(t){var e,n,r,i,o,a,s;for(t.pos=this.offset,this.metrics=[],e=0,a=this.file.hhea.numberOfMetrics;0<=a?e<a:e>a;e=0<=a?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(r=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var n,i;for(i=[],e=n=0;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(t.readInt16());return i}(),this.widths=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.metrics).length;t<e;t++)i=n[t],r.push(i.advance);return r}.call(this),n=this.widths[this.widths.length-1],s=[],e=o=0;0<=r?o<r:o>r;e=0<=r?++o:--o)s.push(this.widths.push(n));return s},t.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},t}(),Ci=[].slice,ji=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="glyf",t.prototype.parse=function(){return this.cache={}},t.prototype.glyphFor=function(t){var e,n,r,i,o,a,s,l,u,c;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,n=i.indexOf(t),0===(r=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+n,o=(a=new mi(e.read(r))).readShort(),l=a.readShort(),c=a.readShort(),s=a.readShort(),u=a.readShort(),this.cache[t]=-1===o?new Oi(a,l,c,s,u):new Ii(a,o,l,c,s,u),this.cache[t]))},t.prototype.encode=function(t,e,n){var r,i,o,a,s;for(o=[],i=[],a=0,s=e.length;a<s;a++)r=t[e[a]],i.push(o.length),r&&(o=o.concat(r.encode(n)));return i.push(o.length),{table:o,offsets:i}},t}(),Ii=function(){function t(t,e,n,r,i,o){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=r,this.xMax=i,this.yMax=o,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),Oi=function(){function t(t,e,n,r,i){var o,a;for(this.raw=t,this.xMin=e,this.yMin=n,this.xMax=r,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],o=this.raw;a=o.readShort(),this.glyphOffsets.push(o.pos),this.glyphIDs.push(o.readUInt16()),32&a;)o.pos+=1&a?4:2,128&a?o.pos+=8:64&a?o.pos+=4:8&a&&(o.pos+=2)}return t.prototype.encode=function(){var t,e,n;for(e=new mi(Ci.call(this.raw.data)),t=0,n=this.glyphIDs.length;t<n;++t)e.pos=this.glyphOffsets[t];return e.data},t}(),Ei=function(){function t(){return t.__super__.constructor.apply(this,arguments)}return yi(t,gi),t.prototype.tag="loca",t.prototype.parse=function(t){var e,n;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=0===e?function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=2)r.push(2*t.readUInt16());return r}.call(this):function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=4)r.push(t.readUInt32());return r}.call(this)},t.prototype.indexOf=function(t){return this.offsets[t]},t.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},t.prototype.encode=function(t,e){for(var n=new Uint32Array(this.offsets.length),r=0,i=0,o=0;o<n.length;++o)if(n[o]=r,i<e.length&&e[i]==o){++i,n[o]=r;var a=this.offsets[o],s=this.offsets[o+1]-a;s>0&&(r+=s)}for(var l=new Array(4*n.length),u=0;u<n.length;++u)l[4*u+3]=255&n[u],l[4*u+2]=(65280&n[u])>>8,l[4*u+1]=(16711680&n[u])>>16,l[4*u]=(**********&n[u])>>24;return l},t}(),Ri=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,n,r,i;for(e in r=this.font.cmap.tables[0].codeMap,t={},i=this.subset)n=i[e],t[e]=r[n];return t},t.prototype.glyphsFor=function(t){var e,n,r,i,o,a,s;for(r={},o=0,a=t.length;o<a;o++)r[i=t[o]]=this.font.glyf.glyphFor(i);for(i in e=[],r)(null!=(n=r[i])?n.compound:void 0)&&e.push.apply(e,n.glyphIDs);if(e.length>0)for(i in s=this.glyphsFor(e))n=s[i],r[i]=n;return r},t.prototype.encode=function(t,e){var n,r,i,o,a,s,l,u,c,h,f,d,p,g,m;for(r in n=Ni.encode(this.generateCmap(),"unicode"),o=this.glyphsFor(t),f={0:0},m=n.charMap)f[(s=m[r]).old]=s.new;for(d in h=n.maxGlyphID,o)d in f||(f[d]=h++);return u=function(t){var e,n;for(e in n={},t)n[t[e]]=e;return n}(f),c=Object.keys(u).sort((function(t,e){return t-e})),p=function(){var t,e,n;for(n=[],t=0,e=c.length;t<e;t++)a=c[t],n.push(u[a]);return n}(),i=this.font.glyf.encode(o,p,f),l=this.font.loca.encode(i.offsets,p),g={cmap:this.font.cmap.raw(),glyf:i.table,loca:l,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(g["OS/2"]=this.font.os2.raw()),this.font.directory.encode(g)},t}();function Bi(t,e,n,r,i){r=r||{};var o=i.internal.scaleFactor,a=i.internal.getFontSize()/o,s=a*(i.getLineHeightFactor?i.getLineHeightFactor():1.15),l="",u=1;if("middle"!==r.valign&&"bottom"!==r.valign&&"center"!==r.halign&&"right"!==r.halign||(u=(l="string"==typeof t?t.split(/\r\n|\r|\n/g):t).length||1),n+=a*(2-1.15),"middle"===r.valign?n-=u/2*s:"bottom"===r.valign&&(n-=u*s),"center"===r.halign||"right"===r.halign){var c=a;if("center"===r.halign&&(c*=.5),l&&u>=1){for(var h=0;h<l.length;h++)i.text(l[h],e-i.getStringUnitWidth(l[h])*c,n),n+=s;return i}e-=i.getStringUnitWidth(t)*c}return"justify"===r.halign?i.text(t,e,n,{maxWidth:r.maxWidth||100,align:"justify"}):i.text(t,e,n),i}Xn.API.PDFObject=function(){var t;function e(){}return t=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},e.convert=function(n){var r,i,o,a;if(Array.isArray(n))return"["+function(){var t,i,o;for(o=[],t=0,i=n.length;t<i;t++)r=n[t],o.push(e.convert(r));return o}().join(" ")+"]";if("string"==typeof n)return"/"+n;if(null!=n?n.isString:void 0)return"("+n+")";if(n instanceof Date)return"(D:"+t(n.getUTCFullYear(),4)+t(n.getUTCMonth(),2)+t(n.getUTCDate(),2)+t(n.getUTCHours(),2)+t(n.getUTCMinutes(),2)+t(n.getUTCSeconds(),2)+"Z)";if("[object Object]"==={}.toString.call(n)){for(i in o=["<<"],n)a=n[i],o.push("/"+i+" "+e.convert(a));return o.push(">>"),o.join("\n")}return""+n},e}();var Di={},Ti=function(){function t(t){this.jsPDFDocument=t,this.userStyles={textColor:t.getTextColor?this.jsPDFDocument.getTextColor():0,fontSize:t.internal.getFontSize(),fontStyle:t.internal.getFont().fontStyle,font:t.internal.getFont().fontName,lineWidth:t.getLineWidth?this.jsPDFDocument.getLineWidth():0,lineColor:t.getDrawColor?this.jsPDFDocument.getDrawColor():0}}return t.setDefaults=function(t,e){void 0===e&&(e=null),e?e.__autoTableDocumentDefaults=t:Di=t},t.unifyColor=function(t){return Array.isArray(t)?t:"number"==typeof t?[t,t,t]:"string"==typeof t?[t]:null},t.prototype.applyStyles=function(e,n){var r,i,o;void 0===n&&(n=!1),e.fontStyle&&this.jsPDFDocument.setFontStyle&&this.jsPDFDocument.setFontStyle(e.fontStyle);var a=this.jsPDFDocument.internal.getFont(),s=a.fontStyle,l=a.fontName;if(e.font&&(l=e.font),e.fontStyle){s=e.fontStyle;var u=this.getFontList()[l];u&&-1===u.indexOf(s)&&this.jsPDFDocument.setFontStyle&&(this.jsPDFDocument.setFontStyle(u[0]),s=u[0])}if(this.jsPDFDocument.setFont(l,s),e.fontSize&&this.jsPDFDocument.setFontSize(e.fontSize),!n){var c=t.unifyColor(e.fillColor);c&&(r=this.jsPDFDocument).setFillColor.apply(r,c),(c=t.unifyColor(e.textColor))&&(i=this.jsPDFDocument).setTextColor.apply(i,c),(c=t.unifyColor(e.lineColor))&&(o=this.jsPDFDocument).setDrawColor.apply(o,c),"number"==typeof e.lineWidth&&this.jsPDFDocument.setLineWidth(e.lineWidth)}},t.prototype.splitTextToSize=function(t,e,n){return this.jsPDFDocument.splitTextToSize(t,e,n)},t.prototype.rect=function(t,e,n,r,i){return this.jsPDFDocument.rect(t,e,n,r,i)},t.prototype.getLastAutoTable=function(){return this.jsPDFDocument.lastAutoTable||null},t.prototype.getTextWidth=function(t){return this.jsPDFDocument.getTextWidth(t)},t.prototype.getDocument=function(){return this.jsPDFDocument},t.prototype.setPage=function(t){this.jsPDFDocument.setPage(t)},t.prototype.addPage=function(){return this.jsPDFDocument.addPage()},t.prototype.getFontList=function(){return this.jsPDFDocument.getFontList()},t.prototype.getGlobalOptions=function(){return Di||{}},t.prototype.getDocumentOptions=function(){return this.jsPDFDocument.__autoTableDocumentDefaults||{}},t.prototype.pageSize=function(){var t=this.jsPDFDocument.internal.pageSize;return null==t.width&&(t={width:t.getWidth(),height:t.getHeight()}),t},t.prototype.scaleFactor=function(){return this.jsPDFDocument.internal.scaleFactor},t.prototype.getLineHeightFactor=function(){var t=this.jsPDFDocument;return t.getLineHeightFactor?t.getLineHeightFactor():1.15},t.prototype.getLineHeight=function(t){return t/this.scaleFactor()*this.getLineHeightFactor()},t.prototype.pageNumber=function(){var t=this.jsPDFDocument.internal.getCurrentPageInfo();return t?t.pageNumber:this.jsPDFDocument.internal.getNumberOfPages()},t}(),Mi=function(t,e){return(Mi=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function qi(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}Mi(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}"function"==typeof SuppressedError&&SuppressedError;var Ui=function(t){function e(e){var n=t.call(this)||this;return n._element=e,n}return qi(e,t),e}(Array);function zi(t,e,n){return n.applyStyles(e,!0),(Array.isArray(t)?t:[t]).map((function(t){return n.getTextWidth(t)})).reduce((function(t,e){return Math.max(t,e)}),0)}function Wi(t,e,n,r){var i=e.settings.tableLineWidth,o=e.settings.tableLineColor;t.applyStyles({lineWidth:i,lineColor:o});var a=Hi(i,!1);a&&t.rect(n.x,n.y,e.getWidth(t.pageSize().width),r.y-n.y,a)}function Hi(t,e){var n=t>0,r=e||0===e;return n&&r?"DF":n?"S":r?"F":null}function Vi(t,e){var n,r,i,o;if(t=t||e,Array.isArray(t)){if(t.length>=4)return{top:t[0],right:t[1],bottom:t[2],left:t[3]};if(3===t.length)return{top:t[0],right:t[1],bottom:t[2],left:t[1]};if(2===t.length)return{top:t[0],right:t[1],bottom:t[0],left:t[1]};t=1===t.length?t[0]:e}return"object"==typeof t?("number"==typeof t.vertical&&(t.top=t.vertical,t.bottom=t.vertical),"number"==typeof t.horizontal&&(t.right=t.horizontal,t.left=t.horizontal),{left:null!==(n=t.left)&&void 0!==n?n:e,top:null!==(r=t.top)&&void 0!==r?r:e,right:null!==(i=t.right)&&void 0!==i?i:e,bottom:null!==(o=t.bottom)&&void 0!==o?o:e}):("number"!=typeof t&&(t=e),{top:t,right:t,bottom:t,left:t})}function Gi(t,e){var n=Vi(e.settings.margin,0);return t.pageSize().width-(n.left+n.right)}function Ji(t,e,n,r,i){var o={},a=96/72,s=Yi(e,(function(t){return i.getComputedStyle(t).backgroundColor}));null!=s&&(o.fillColor=s);var l=Yi(e,(function(t){return i.getComputedStyle(t).color}));null!=l&&(o.textColor=l);var u=function(t,e){var n=[t.paddingTop,t.paddingRight,t.paddingBottom,t.paddingLeft],r=96/(72/e),i=(parseInt(t.lineHeight)-parseInt(t.fontSize))/e/2,o=n.map((function(t){return parseInt(t||"0")/r})),a=Vi(o,0);i>a.top&&(a.top=i);i>a.bottom&&(a.bottom=i);return a}(r,n);u&&(o.cellPadding=u);var c="borderTopColor",h=a*n,f=r.borderTopWidth;if(r.borderBottomWidth===f&&r.borderRightWidth===f&&r.borderLeftWidth===f){var d=(parseFloat(f)||0)/h;d&&(o.lineWidth=d)}else o.lineWidth={top:(parseFloat(r.borderTopWidth)||0)/h,right:(parseFloat(r.borderRightWidth)||0)/h,bottom:(parseFloat(r.borderBottomWidth)||0)/h,left:(parseFloat(r.borderLeftWidth)||0)/h},o.lineWidth.top||(o.lineWidth.right?c="borderRightColor":o.lineWidth.bottom?c="borderBottomColor":o.lineWidth.left&&(c="borderLeftColor"));var p=Yi(e,(function(t){return i.getComputedStyle(t)[c]}));null!=p&&(o.lineColor=p);var g=["left","right","center","justify"];-1!==g.indexOf(r.textAlign)&&(o.halign=r.textAlign),-1!==(g=["middle","bottom","top"]).indexOf(r.verticalAlign)&&(o.valign=r.verticalAlign);var m=parseInt(r.fontSize||"");isNaN(m)||(o.fontSize=m/a);var v=function(t){var e="";("bold"===t.fontWeight||"bolder"===t.fontWeight||parseInt(t.fontWeight)>=700)&&(e="bold");"italic"!==t.fontStyle&&"oblique"!==t.fontStyle||(e+="italic");return e}(r);v&&(o.fontStyle=v);var b=(r.fontFamily||"").toLowerCase();return-1!==t.indexOf(b)&&(o.font=b),o}function Yi(t,e){var n=Ki(t,e);if(!n)return null;var r=n.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d*))?\)$/);if(!r||!Array.isArray(r))return null;var i=[parseInt(r[1]),parseInt(r[2]),parseInt(r[3])];return 0===parseInt(r[4])||isNaN(i[0])||isNaN(i[1])||isNaN(i[2])?null:i}function Ki(t,e){var n=e(t);return"rgba(0, 0, 0, 0)"===n||"transparent"===n||"initial"===n||"inherit"===n?null==t.parentElement?null:Ki(t.parentElement,e):n}function Xi(t,e,n,r,i){var o,a,s;void 0===r&&(r=!1),void 0===i&&(i=!1),s="string"==typeof e?n.document.querySelector(e):e;var l=Object.keys(t.getFontList()),u=t.scaleFactor(),c=[],h=[],f=[];if(!s)return{head:c,body:h,foot:f};for(var d=0;d<s.rows.length;d++){var p=s.rows[d],g=null===(a=null===(o=null==p?void 0:p.parentElement)||void 0===o?void 0:o.tagName)||void 0===a?void 0:a.toLowerCase(),m=$i(l,u,n,p,r,i);m&&("thead"===g?c.push(m):"tfoot"===g?f.push(m):h.push(m))}return{head:c,body:h,foot:f}}function $i(t,e,n,r,i,o){for(var a=new Ui(r),s=0;s<r.cells.length;s++){var l=r.cells[s],u=n.getComputedStyle(l);if(i||"none"!==u.display){var c=void 0;o&&(c=Ji(t,l,e,u,n)),a.push({rowSpan:l.rowSpan,colSpan:l.colSpan,styles:c,_element:l,content:Zi(l)})}}var h=n.getComputedStyle(r);if(a.length>0&&(i||"none"!==h.display))return a}function Zi(t){var e=t.cloneNode(!0);return e.innerHTML=e.innerHTML.replace(/\n/g,"").replace(/ +/g," "),e.innerHTML=e.innerHTML.split(/<br.*?>/).map((function(t){return t.trim()})).join("\n"),e.innerText||e.textContent||""}function Qi(t,e,n,r,i){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var o=Object(t),a=1;a<arguments.length;a++){var s=arguments[a];if(null!=s)for(var l in s)Object.prototype.hasOwnProperty.call(s,l)&&(o[l]=s[l])}return o}function to(t,e){var n=new Ti(t),r=n.getDocumentOptions(),i=n.getGlobalOptions();!function(t,e,n){for(var r=0,i=[t,e,n];r<i.length;r++){var o=i[r];o.startY&&"number"!=typeof o.startY&&delete o.startY}}(i,r,e);var o,a=Qi({},i,r,e);"undefined"!=typeof window&&(o=window);var s=function(t,e,n){for(var r={styles:{},headStyles:{},bodyStyles:{},footStyles:{},alternateRowStyles:{},columnStyles:{}},i=function(i){if("columnStyles"===i){var o=t[i],a=e[i],s=n[i];r.columnStyles=Qi({},o,a,s)}else{var l=[t,e,n].map((function(t){return t[i]||{}}));r[i]=Qi({},l[0],l[1],l[2])}},o=0,a=Object.keys(r);o<a.length;o++){i(a[o])}return r}(i,r,e),l=function(t,e,n){for(var r={didParseCell:[],willDrawCell:[],didDrawCell:[],willDrawPage:[],didDrawPage:[]},i=0,o=[t,e,n];i<o.length;i++){var a=o[i];a.didParseCell&&r.didParseCell.push(a.didParseCell),a.willDrawCell&&r.willDrawCell.push(a.willDrawCell),a.didDrawCell&&r.didDrawCell.push(a.didDrawCell),a.willDrawPage&&r.willDrawPage.push(a.willDrawPage),a.didDrawPage&&r.didDrawPage.push(a.didDrawPage)}return r}(i,r,e),u=function(t,e){var n,r,i,o,a,s,l,u,c,h,f,d,p,g,m=Vi(e.margin,40/t.scaleFactor()),v=null!==(n=function(t,e){var n=t.getLastAutoTable(),r=t.scaleFactor(),i=t.pageNumber(),o=!1;if(n&&n.startPageNumber){o=n.startPageNumber+n.pageNumber-1===i}if("number"==typeof e)return e;if((null==e||!1===e)&&o&&null!=(null==n?void 0:n.finalY))return n.finalY+20/r;return null}(t,e.startY))&&void 0!==n?n:m.top;p=!0===e.showFoot?"everyPage":!1===e.showFoot?"never":null!==(r=e.showFoot)&&void 0!==r?r:"everyPage";g=!0===e.showHead?"everyPage":!1===e.showHead?"never":null!==(i=e.showHead)&&void 0!==i?i:"everyPage";var b=null!==(o=e.useCss)&&void 0!==o&&o,y=e.theme||(b?"plain":"striped"),w=!!e.horizontalPageBreak,x=null!==(a=e.horizontalPageBreakRepeat)&&void 0!==a?a:null;return{includeHiddenHtml:null!==(s=e.includeHiddenHtml)&&void 0!==s&&s,useCss:b,theme:y,startY:v,margin:m,pageBreak:null!==(l=e.pageBreak)&&void 0!==l?l:"auto",rowPageBreak:null!==(u=e.rowPageBreak)&&void 0!==u?u:"auto",tableWidth:null!==(c=e.tableWidth)&&void 0!==c?c:"auto",showHead:g,showFoot:p,tableLineWidth:null!==(h=e.tableLineWidth)&&void 0!==h?h:0,tableLineColor:null!==(f=e.tableLineColor)&&void 0!==f?f:200,horizontalPageBreak:w,horizontalPageBreakRepeat:x,horizontalPageBreakBehaviour:null!==(d=e.horizontalPageBreakBehaviour)&&void 0!==d?d:"afterAllRows"}}(n,a),c=function(t,e,n){var r=e.head||[],i=e.body||[],o=e.foot||[];if(e.html){var a=e.includeHiddenHtml;if(n){var s=Xi(t,e.html,n,a,e.useCss)||{};r=s.head||r,i=s.body||r,o=s.foot||r}}var l=e.columns||function(t,e,n){var r=t[0]||e[0]||n[0]||[],i=[];return Object.keys(r).filter((function(t){return"_element"!==t})).forEach((function(t){var e,n=1;"object"!=typeof(e=Array.isArray(r)?r[parseInt(t)]:r[t])||Array.isArray(e)||(n=(null==e?void 0:e.colSpan)||1);for(var o=0;o<n;o++){var a={dataKey:Array.isArray(r)?i.length:t+(o>0?"_".concat(o):"")};i.push(a)}})),i}(r,i,o);return{columns:l,head:r,body:i,foot:o}}(n,a,o);return{id:e.tableId,content:c,hooks:l,styles:s,settings:u}}var eo,no=function(){return function(t,e,n){this.table=e,this.pageNumber=e.pageNumber,this.settings=e.settings,this.cursor=n,this.doc=t.getDocument()}}(),ro=function(t){function e(e,n,r,i,o,a){var s=t.call(this,e,n,a)||this;return s.cell=r,s.row=i,s.column=o,s.section=i.section,s}return qi(e,t),e}(no),io=function(){function t(t,e){this.pageNumber=1,this.id=t.id,this.settings=t.settings,this.styles=t.styles,this.hooks=t.hooks,this.columns=e.columns,this.head=e.head,this.body=e.body,this.foot=e.foot}return t.prototype.getHeadHeight=function(t){return this.head.reduce((function(e,n){return e+n.getMaxCellHeight(t)}),0)},t.prototype.getFootHeight=function(t){return this.foot.reduce((function(e,n){return e+n.getMaxCellHeight(t)}),0)},t.prototype.allRows=function(){return this.head.concat(this.body).concat(this.foot)},t.prototype.callCellHooks=function(t,e,n,r,i,o){for(var a=0,s=e;a<s.length;a++){var l=!1===(0,s[a])(new ro(t,this,n,r,i,o));if(n.text=Array.isArray(n.text)?n.text:[n.text],l)return!1}return!0},t.prototype.callEndPageHooks=function(t,e){t.applyStyles(t.userStyles);for(var n=0,r=this.hooks.didDrawPage;n<r.length;n++){(0,r[n])(new no(t,this,e))}},t.prototype.callWillDrawPageHooks=function(t,e){for(var n=0,r=this.hooks.willDrawPage;n<r.length;n++){(0,r[n])(new no(t,this,e))}},t.prototype.getWidth=function(t){if("number"==typeof this.settings.tableWidth)return this.settings.tableWidth;if("wrap"===this.settings.tableWidth)return this.columns.reduce((function(t,e){return t+e.wrappedWidth}),0);var e=this.settings.margin;return t-e.left-e.right},t}(),oo=function(){function t(t,e,n,r,i){void 0===i&&(i=!1),this.height=0,this.raw=t,t instanceof Ui&&(this.raw=t._element,this.element=t._element),this.index=e,this.section=n,this.cells=r,this.spansMultiplePages=i}return t.prototype.getMaxCellHeight=function(t){var e=this;return t.reduce((function(t,n){var r;return Math.max(t,(null===(r=e.cells[n.index])||void 0===r?void 0:r.height)||0)}),0)},t.prototype.hasRowSpan=function(t){var e=this;return t.filter((function(t){var n=e.cells[t.index];return!!n&&n.rowSpan>1})).length>0},t.prototype.canEntireRowFit=function(t,e){return this.getMaxCellHeight(e)<=t},t.prototype.getMinimumRowHeight=function(t,e){var n=this;return t.reduce((function(t,r){var i=n.cells[r.index];if(!i)return 0;var o=e.getLineHeight(i.styles.fontSize),a=i.padding("vertical")+o;return a>t?a:t}),0)},t}(),ao=function(){function t(t,e,n){var r;this.contentHeight=0,this.contentWidth=0,this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.height=0,this.x=0,this.y=0,this.styles=e,this.section=n,this.raw=t;var i=t;null==t||"object"!=typeof t||Array.isArray(t)?(this.rowSpan=1,this.colSpan=1):(this.rowSpan=t.rowSpan||1,this.colSpan=t.colSpan||1,i=null!==(r=t.content)&&void 0!==r?r:t,t._element&&(this.raw=t._element));var o=null!=i?""+i:"";this.text=o.split(/\r\n|\r|\n/g)}return t.prototype.getTextPos=function(){var t,e;if("top"===this.styles.valign)t=this.y+this.padding("top");else if("bottom"===this.styles.valign)t=this.y+this.height-this.padding("bottom");else{var n=this.height-this.padding("vertical");t=this.y+n/2+this.padding("top")}if("right"===this.styles.halign)e=this.x+this.width-this.padding("right");else if("center"===this.styles.halign){var r=this.width-this.padding("horizontal");e=this.x+r/2+this.padding("left")}else e=this.x+this.padding("left");return{x:e,y:t}},t.prototype.getContentHeight=function(t,e){void 0===e&&(e=1.15);var n=(Array.isArray(this.text)?this.text.length:1)*(this.styles.fontSize/t*e)+this.padding("vertical");return Math.max(n,this.styles.minCellHeight)},t.prototype.padding=function(t){var e=Vi(this.styles.cellPadding,0);return"vertical"===t?e.top+e.bottom:"horizontal"===t?e.left+e.right:e[t]},t}(),so=function(){function t(t,e,n){this.wrappedWidth=0,this.minReadableWidth=0,this.minWidth=0,this.width=0,this.dataKey=t,this.raw=e,this.index=n}return t.prototype.getMaxCustomCellWidth=function(t){for(var e=0,n=0,r=t.allRows();n<r.length;n++){var i=r[n].cells[this.index];i&&"number"==typeof i.styles.cellWidth&&(e=Math.max(e,i.styles.cellWidth))}return e},t}();function lo(t,e){!function(t,e){var n=t.scaleFactor(),r=e.settings.horizontalPageBreak,i=Gi(t,e);e.allRows().forEach((function(o){for(var a=0,s=e.columns;a<s.length;a++){var l=s[a],u=o.cells[l.index];if(u){var c=e.hooks.didParseCell;e.callCellHooks(t,c,u,o,l,null);var h=u.padding("horizontal");u.contentWidth=zi(u.text,u.styles,t)+h;var f=zi(u.text.join(" ").split(/[^\S\u00A0]+/),u.styles,t);if(u.minReadableWidth=f+u.padding("horizontal"),"number"==typeof u.styles.cellWidth)u.minWidth=u.styles.cellWidth,u.wrappedWidth=u.styles.cellWidth;else if("wrap"===u.styles.cellWidth||!0===r)u.contentWidth>i?(u.minWidth=i,u.wrappedWidth=i):(u.minWidth=u.contentWidth,u.wrappedWidth=u.contentWidth);else{var d=10/n;u.minWidth=u.styles.minCellWidth||d,u.wrappedWidth=u.contentWidth,u.minWidth>u.wrappedWidth&&(u.wrappedWidth=u.minWidth)}}}})),e.allRows().forEach((function(t){for(var n=0,r=e.columns;n<r.length;n++){var i=r[n],o=t.cells[i.index];if(o&&1===o.colSpan)i.wrappedWidth=Math.max(i.wrappedWidth,o.wrappedWidth),i.minWidth=Math.max(i.minWidth,o.minWidth),i.minReadableWidth=Math.max(i.minReadableWidth,o.minReadableWidth);else{var a=e.styles.columnStyles[i.dataKey]||e.styles.columnStyles[i.index]||{},s=a.cellWidth||a.minCellWidth;s&&"number"==typeof s&&(i.minWidth=s,i.wrappedWidth=s)}o&&(o.colSpan>1&&!i.minWidth&&(i.minWidth=o.minWidth),o.colSpan>1&&!i.wrappedWidth&&(i.wrappedWidth=o.minWidth))}}))}(t,e);var n=[],r=0;e.columns.forEach((function(t){var i=t.getMaxCustomCellWidth(e);i?t.width=i:(t.width=t.wrappedWidth,n.push(t)),r+=t.width}));var i=e.getWidth(t.pageSize().width)-r;i&&(i=uo(n,i,(function(t){return Math.max(t.minReadableWidth,t.minWidth)}))),i&&(i=uo(n,i,(function(t){return t.minWidth}))),i=Math.abs(i),!e.settings.horizontalPageBreak&&i>.1/t.scaleFactor()&&(i=i<1?i:Math.round(i)),function(t){for(var e=t.allRows(),n=0;n<e.length;n++)for(var r=e[n],i=null,o=0,a=0,s=0;s<t.columns.length;s++){var l=t.columns[s];if((a-=1)>1&&t.columns[s+1])o+=l.width,delete r.cells[l.index];else if(i){var u=i;delete r.cells[l.index],i=null,u.width=l.width+o}else{if(!(u=r.cells[l.index]))continue;if(a=u.colSpan,o=0,u.colSpan>1){i=u,o+=l.width;continue}u.width=l.width+o}}}(e),function(t,e){for(var n={count:0,height:0},r=0,i=t.allRows();r<i.length;r++){for(var o=i[r],a=0,s=t.columns;a<s.length;a++){var l=s[a],u=o.cells[l.index];if(u){e.applyStyles(u.styles,!0);var c=u.width-u.padding("horizontal");if("linebreak"===u.styles.overflow)u.text=e.splitTextToSize(u.text,c+1/e.scaleFactor(),{fontSize:u.styles.fontSize});else if("ellipsize"===u.styles.overflow)u.text=co(u.text,c,u.styles,e,"...");else if("hidden"===u.styles.overflow)u.text=co(u.text,c,u.styles,e,"");else if("function"==typeof u.styles.overflow){var h=u.styles.overflow(u.text,c);u.text="string"==typeof h?[h]:h}u.contentHeight=u.getContentHeight(e.scaleFactor(),e.getLineHeightFactor());var f=u.contentHeight/u.rowSpan;u.rowSpan>1&&n.count*n.height<f*u.rowSpan?n={height:f,count:u.rowSpan}:n&&n.count>0&&n.height>f&&(f=n.height),f>o.height&&(o.height=f)}}n.count--}}(e,t),function(t){for(var e={},n=1,r=t.allRows(),i=0;i<r.length;i++)for(var o=r[i],a=0,s=t.columns;a<s.length;a++){var l=s[a],u=e[l.index];if(n>1)n--,delete o.cells[l.index];else if(u)u.cell.height+=o.height,n=u.cell.colSpan,delete o.cells[l.index],u.left--,u.left<=1&&delete e[l.index];else{var c=o.cells[l.index];if(!c)continue;if(c.height=o.height,c.rowSpan>1){var h=r.length-i,f=c.rowSpan>h?h:c.rowSpan;e[l.index]={cell:c,left:f,row:o}}}}}(e)}function uo(t,e,n){for(var r=e,i=t.reduce((function(t,e){return t+e.wrappedWidth}),0),o=0;o<t.length;o++){var a=t[o],s=r*(a.wrappedWidth/i),l=a.width+s,u=n(a),c=l<u?u:l;e-=c-a.width,a.width=c}if(e=Math.round(1e10*e)/1e10){var h=t.filter((function(t){return!(e<0)||t.width>n(t)}));h.length&&(e=uo(h,e,n))}return e}function co(t,e,n,r,i){return t.map((function(t){return function(t,e,n,r,i){var o=1e4*r.scaleFactor();if(e=Math.ceil(e*o)/o,e>=zi(t,n,r))return t;for(;e<zi(t+i,n,r)&&!(t.length<=1);)t=t.substring(0,t.length-1);return t.trim()+i}(t,e,n,r,i)}))}function ho(t,e){var n=new Ti(t),r=function(t,e){var n=t.content,r=function(t){return t.map((function(t,e){var n,r;return r="object"==typeof t&&null!==(n=t.dataKey)&&void 0!==n?n:e,new so(r,t,e)}))}(n.columns);if(0===n.head.length){(i=po(r,"head"))&&n.head.push(i)}if(0===n.foot.length){var i;(i=po(r,"foot"))&&n.foot.push(i)}var o=t.settings.theme,a=t.styles;return{columns:r,head:fo("head",n.head,r,a,o,e),body:fo("body",n.body,r,a,o,e),foot:fo("foot",n.foot,r,a,o,e)}}(e,n.scaleFactor()),i=new io(e,r);return lo(n,i),n.applyStyles(n.userStyles),i}function fo(t,e,n,r,i,o){var a={};return e.map((function(e,s){for(var l=0,u={},c=0,h=0,f=0,d=n;f<d.length;f++){var p=d[f];if(null==a[p.index]||0===a[p.index].left)if(0===h){var g=void 0,m={};"object"!=typeof(g=Array.isArray(e)?e[p.index-c-l]:e[p.dataKey])||Array.isArray(g)||(m=(null==g?void 0:g.styles)||{});var v=go(t,p,s,i,r,o,m),b=new ao(g,v,t);u[p.dataKey]=b,u[p.index]=b,h=b.colSpan-1,a[p.index]={left:b.rowSpan-1,times:h}}else h--,c++;else a[p.index].left--,h=a[p.index].times,l++}return new oo(e,s,t,u)}))}function po(t,e){var n={};return t.forEach((function(t){if(null!=t.raw){var r=function(t,e){if("head"===t){if("object"==typeof e)return e.header||null;if("string"==typeof e||"number"==typeof e)return e}else if("foot"===t&&"object"==typeof e)return e.footer;return null}(e,t.raw);null!=r&&(n[t.dataKey]=r)}})),Object.keys(n).length>0?n:null}function go(t,e,n,r,i,o,a){var s,l={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal"},head:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},body:{},foot:{textColor:255,fillColor:[41,128,185],fontStyle:"bold"},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1},head:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},body:{},foot:{textColor:255,fillColor:[26,188,156],fontStyle:"bold",lineWidth:0},alternateRow:{}},plain:{head:{fontStyle:"bold"},foot:{fontStyle:"bold"}}}[r];"head"===t?s=i.headStyles:"body"===t?s=i.bodyStyles:"foot"===t&&(s=i.footStyles);var u=Qi({},l.table,l[t],i.styles,s),c=i.columnStyles[e.dataKey]||i.columnStyles[e.index]||{},h="body"===t?c:{},f="body"===t&&n%2==0?Qi({},l.alternateRow,i.alternateRowStyles):{},d=function(t){return{font:"helvetica",fontStyle:"normal",overflow:"linebreak",fillColor:!1,textColor:20,halign:"left",valign:"top",fontSize:10,cellPadding:5/t,lineColor:200,lineWidth:0,cellWidth:"auto",minCellHeight:0,minCellWidth:0}}(o),p=Qi({},d,u,f,h);return Qi(p,a)}function mo(t,e,n){var r;void 0===n&&(n={});var i=Gi(t,e),o=new Map,a=[],s=[],l=[];Array.isArray(e.settings.horizontalPageBreakRepeat)?l=e.settings.horizontalPageBreakRepeat:"string"!=typeof e.settings.horizontalPageBreakRepeat&&"number"!=typeof e.settings.horizontalPageBreakRepeat||(l=[e.settings.horizontalPageBreakRepeat]),l.forEach((function(t){var n=e.columns.find((function(e){return e.dataKey===t||e.index===t}));n&&!o.has(n.index)&&(o.set(n.index,!0),a.push(n.index),s.push(e.columns[n.index]),i-=n.wrappedWidth)}));for(var u=!0,c=null!==(r=null==n?void 0:n.start)&&void 0!==r?r:0;c<e.columns.length;)if(o.has(c))c++;else{var h=e.columns[c].wrappedWidth;if(!(u||i>=h))break;u=!1,a.push(c),s.push(e.columns[c]),i-=h,c++}return{colIndexes:a,columns:s,lastIndex:c-1}}function vo(t,e){var n=e.settings,r=n.startY,i=n.margin,o={x:i.left,y:r},a=e.getHeadHeight(e.columns)+e.getFootHeight(e.columns),s=r+i.bottom+a;"avoid"===n.pageBreak&&(s+=e.body.reduce((function(t,e){return t+e.height}),0));var l=new Ti(t);("always"===n.pageBreak||null!=n.startY&&s>l.pageSize().height)&&(Po(l),o.y=i.top),e.callWillDrawPageHooks(l,o);var u=Qi({},o);e.startPageNumber=l.pageNumber(),n.horizontalPageBreak?function(t,e,n,r){var i=function(t,e){for(var n=[],r=0;r<e.columns.length;r++){var i=mo(t,e,{start:r});i.columns.length&&(n.push(i),r=i.lastIndex)}return n}(t,e),o=e.settings;if("afterAllRows"===o.horizontalPageBreakBehaviour)i.forEach((function(i,o){t.applyStyles(t.userStyles),o>0?_o(t,e,n,r,i.columns,!0):bo(t,e,r,i.columns),function(t,e,n,r,i){t.applyStyles(t.userStyles),e.body.forEach((function(o,a){var s=a===e.body.length-1;No(t,e,o,s,n,r,i)}))}(t,e,n,r,i.columns),wo(t,e,r,i.columns)}));else for(var a=-1,s=i[0],l=function(){var o=a;if(s){t.applyStyles(t.userStyles);var l=s.columns;a>=0?_o(t,e,n,r,l,!0):bo(t,e,r,l),o=yo(t,e,a+1,r,l),wo(t,e,r,l)}var u=o-a;i.slice(1).forEach((function(i){t.applyStyles(t.userStyles),_o(t,e,n,r,i.columns,!0),yo(t,e,a+1,r,i.columns,u),wo(t,e,r,i.columns)})),a=o};a<e.body.length-1;)l()}(l,e,u,o):(l.applyStyles(l.userStyles),"firstPage"!==n.showHead&&"everyPage"!==n.showHead||e.head.forEach((function(t){return Lo(l,e,t,o,e.columns)})),l.applyStyles(l.userStyles),e.body.forEach((function(t,n){var r=n===e.body.length-1;No(l,e,t,r,u,o,e.columns)})),l.applyStyles(l.userStyles),"lastPage"!==n.showFoot&&"everyPage"!==n.showFoot||e.foot.forEach((function(t){return Lo(l,e,t,o,e.columns)}))),Wi(l,e,u,o),e.callEndPageHooks(l,o),e.finalY=o.y,t.lastAutoTable=e,l.applyStyles(l.userStyles)}function bo(t,e,n,r){var i=e.settings;t.applyStyles(t.userStyles),"firstPage"!==i.showHead&&"everyPage"!==i.showHead||e.head.forEach((function(i){return Lo(t,e,i,n,r)}))}function yo(t,e,n,r,i,o){t.applyStyles(t.userStyles),o=null!=o?o:e.body.length;var a=Math.min(n+o,e.body.length),s=-1;return e.body.slice(n,a).forEach((function(o,a){var l=n+a===e.body.length-1,u=So(t,e,l,r);o.canEntireRowFit(u,i)&&(Lo(t,e,o,r,i),s=n+a)})),s}function wo(t,e,n,r){var i=e.settings;t.applyStyles(t.userStyles),"lastPage"!==i.showFoot&&"everyPage"!==i.showFoot||e.foot.forEach((function(i){return Lo(t,e,i,n,r)}))}function xo(t,e,n){var r=n.getLineHeight(t.styles.fontSize),i=t.padding("vertical"),o=Math.floor((e-i)/r);return Math.max(0,o)}function No(t,e,n,r,i,o,a){var s=So(t,e,r,o);if(n.canEntireRowFit(s,a))Lo(t,e,n,o,a);else if(function(t,e,n,r){var i=t.pageSize().height,o=r.settings.margin,a=i-(o.top+o.bottom);"body"===e.section&&(a-=r.getHeadHeight(r.columns)+r.getFootHeight(r.columns));var s=e.getMinimumRowHeight(r.columns,t);if(s>a)return!0;if(!(s<n))return!1;var l=e.hasRowSpan(r.columns);return!!(e.getMaxCellHeight(r.columns)>a)||!l&&"avoid"!==r.settings.rowPageBreak}(t,n,s,e)){var l=function(t,e,n,r){var i={};t.spansMultiplePages=!0,t.height=0;for(var o=0,a=0,s=n.columns;a<s.length;a++){var l=s[a];if(m=t.cells[l.index]){Array.isArray(m.text)||(m.text=[m.text]),(g=Qi(g=new ao(m.raw,m.styles,m.section),m)).text=[];var u=xo(m,e,r);m.text.length>u&&(g.text=m.text.splice(u,m.text.length));var c=r.scaleFactor(),h=r.getLineHeightFactor();m.contentHeight=m.getContentHeight(c,h),m.contentHeight>=e&&(m.contentHeight=e,g.styles.minCellHeight-=e),m.contentHeight>t.height&&(t.height=m.contentHeight),g.contentHeight=g.getContentHeight(c,h),g.contentHeight>o&&(o=g.contentHeight),i[l.index]=g}}var f=new oo(t.raw,-1,t.section,i,!0);f.height=o;for(var d=0,p=n.columns;d<p.length;d++){var g,m;l=p[d],(g=f.cells[l.index])&&(g.height=f.height),(m=t.cells[l.index])&&(m.height=t.height)}return f}(n,s,e,t);Lo(t,e,n,o,a),_o(t,e,i,o,a),No(t,e,l,r,i,o,a)}else _o(t,e,i,o,a),No(t,e,n,r,i,o,a)}function Lo(t,e,n,r,i){r.x=e.settings.margin.left;for(var o=0,a=i;o<a.length;o++){var s=a[o],l=n.cells[s.index];if(l)if(t.applyStyles(l.styles),l.x=r.x,l.y=r.y,!1!==e.callCellHooks(t,e.hooks.willDrawCell,l,n,s,r)){Ao(t,l,r);var u=l.getTextPos();Bi(l.text,u.x,u.y,{halign:l.styles.halign,valign:l.styles.valign,maxWidth:Math.ceil(l.width-l.padding("left")-l.padding("right"))},t.getDocument()),e.callCellHooks(t,e.hooks.didDrawCell,l,n,s,r),r.x+=s.width}else r.x+=s.width;else r.x+=s.width}r.y+=n.height}function Ao(t,e,n){var r=e.styles;if(t.getDocument().setFillColor(t.getDocument().getFillColor()),"number"==typeof r.lineWidth){var i=Hi(r.lineWidth,r.fillColor);i&&t.rect(e.x,n.y,e.width,e.height,i)}else"object"==typeof r.lineWidth&&(r.fillColor&&t.rect(e.x,n.y,e.width,e.height,"F"),function(t,e,n,r){var i,o,a,s;r.top&&(i=n.x,o=n.y,a=n.x+e.width,s=n.y,r.right&&(a+=.5*r.right),r.left&&(i-=.5*r.left),l(r.top,i,o,a,s));r.bottom&&(i=n.x,o=n.y+e.height,a=n.x+e.width,s=n.y+e.height,r.right&&(a+=.5*r.right),r.left&&(i-=.5*r.left),l(r.bottom,i,o,a,s));r.left&&(i=n.x,o=n.y,a=n.x,s=n.y+e.height,r.top&&(o-=.5*r.top),r.bottom&&(s+=.5*r.bottom),l(r.left,i,o,a,s));r.right&&(i=n.x+e.width,o=n.y,a=n.x+e.width,s=n.y+e.height,r.top&&(o-=.5*r.top),r.bottom&&(s+=.5*r.bottom),l(r.right,i,o,a,s));function l(e,n,r,i,o){t.getDocument().setLineWidth(e),t.getDocument().line(n,r,i,o,"S")}}(t,e,n,r.lineWidth))}function So(t,e,n,r){var i=e.settings.margin.bottom,o=e.settings.showFoot;return("everyPage"===o||"lastPage"===o&&n)&&(i+=e.getFootHeight(e.columns)),t.pageSize().height-r.y-i}function _o(t,e,n,r,i,o){void 0===i&&(i=[]),void 0===o&&(o=!1),t.applyStyles(t.userStyles),"everyPage"!==e.settings.showFoot||o||e.foot.forEach((function(n){return Lo(t,e,n,r,i)})),e.callEndPageHooks(t,r);var a=e.settings.margin;Wi(t,e,n,r),Po(t),e.pageNumber++,r.x=a.left,r.y=a.top,n.y=a.top,e.callWillDrawPageHooks(t,r),"everyPage"===e.settings.showHead&&(e.head.forEach((function(n){return Lo(t,e,n,r,i)})),t.applyStyles(t.userStyles))}function Po(t){var e=t.pageNumber();return t.setPage(e+1),t.pageNumber()===e&&(t.addPage(),!0)}function ko(t,e){vo(t,ho(t,to(t,e)))}try{if("undefined"!=typeof window&&window){var Fo=window,Co=Fo.jsPDF||(null===(eo=Fo.jspdf)||void 0===eo?void 0:eo.jsPDF);Co&&function(t){t.API.autoTable=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return vo(this,ho(this,to(this,t[0]))),this},t.API.lastAutoTable=!1,t.API.autoTableText=function(t,e,n,r){Bi(t,e,n,r,this)},t.API.autoTableSetDefaults=function(t){return Ti.setDefaults(t,this),this},t.autoTableSetDefaults=function(t,e){Ti.setDefaults(t,e)},t.API.autoTableHtmlToJson=function(t,e){var n;if(void 0===e&&(e=!1),"undefined"==typeof window)return null;var r=Xi(new Ti(this),t,window,e,!1),i=r.head,o=r.body;return{columns:(null===(n=i[0])||void 0===n?void 0:n.map((function(t){return t.content})))||[],rows:o,data:o}}}(Co)}}catch(jo){}export{Xn as E,Le as _,ie as a,ko as b};
