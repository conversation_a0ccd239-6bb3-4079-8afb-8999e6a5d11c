# Payment Statistics Logic

## Overview
This document explains the payment statistics calculation logic in the OrderManagement component.

## Payment Status Types
- `paid`: Order is fully paid
- `partial`: Order is partially paid
- `unpaid`: Order is not paid at all
- `undefined`: Treated as unpaid

## Statistics Calculated

### 1. Paid Orders Count (`stats.paid`)
- **Individual Orders**: Count of orders with `payment_status === 'paid'`
- **Order Groups**: Count of groups with `payment_status === 'paid'`

### 2. Partial Paid Orders Count (`stats.partialPaid`)
- **Individual Orders**: Count of orders with `payment_status === 'partial'`
- **Order Groups**: Count of groups with `payment_status === 'partial'`

### 3. Paid Amount (`stats.paidAmount`)
- **Individual Orders**: Sum of `total_amount` for orders with `payment_status === 'paid'`
- **Order Groups**: Sum of `total_group_amount` for groups with `payment_status === 'paid'`

### 4. Partial Paid Amount (`stats.partialPaidAmount`)
- **Individual Orders**: Sum of `paid_amount` for orders with `payment_status === 'partial'`
- **Order Groups**: Sum of `total_paid_amount` for groups with `payment_status === 'partial'`

### 5. Total Paid Display
- **Count**: `stats.paid + stats.partialPaid`
- **Amount**: `stats.paidAmount + stats.partialPaidAmount`

### 6. Unpaid Amount (`stats.unpaidAmount`)
For individual orders:
```javascript
orders.reduce((sum, order) => {
  if (order.payment_status === 'paid') return sum; // Fully paid, no unpaid amount
  if (order.payment_status === 'partial') {
    return sum + ((order.total_amount || 0) - (order.paid_amount || 0)); // Remaining amount
  }
  return sum + (order.total_amount || 0); // Full amount is unpaid
}, 0)
```

For order groups:
```javascript
orderGroups.reduce((sum, group) => {
  if (group.payment_status === 'paid') return sum; // Fully paid, no unpaid amount
  return sum + ((group.total_group_amount || 0) - (group.total_paid_amount || 0)); // Remaining amount
}, 0)
```

## UI Display

### Payment Card
- **Icon**: PaymentIcon (success color)
- **Title**: "Đã thanh toán" (Individual) / "Nhóm đã thanh toán" (Groups)
- **Main Number**: Total count of paid + partially paid orders/groups
- **Subtitle**: Total amount paid (fully paid + partial payments)

### Examples

#### Example 1: Mixed Payment Status
```javascript
const orders = [
  { total_amount: 1000000, paid_amount: 1000000, payment_status: 'paid' },     // Fully paid
  { total_amount: 2000000, paid_amount: 1200000, payment_status: 'partial' },  // Partially paid
  { total_amount: 1500000, paid_amount: 0, payment_status: 'unpaid' }          // Unpaid
];

// Results:
// stats.paid = 1
// stats.partialPaid = 1
// stats.paidAmount = 1,000,000
// stats.partialPaidAmount = 1,200,000
// Display count: 2 (1 + 1)
// Display amount: 2,200,000₫ (1,000,000 + 1,200,000)
// stats.unpaidAmount = 2,300,000 (0 + 800,000 + 1,500,000)
```

#### Example 2: All Paid
```javascript
const orders = [
  { total_amount: 1000000, paid_amount: 1000000, payment_status: 'paid' },
  { total_amount: 800000, paid_amount: 800000, payment_status: 'paid' }
];

// Results:
// stats.paid = 2
// stats.partialPaid = 0
// stats.paidAmount = 1,800,000
// stats.partialPaidAmount = 0
// Display count: 2
// Display amount: 1,800,000₫
// stats.unpaidAmount = 0
```

#### Example 3: All Unpaid
```javascript
const orders = [
  { total_amount: 1000000, paid_amount: 0, payment_status: 'unpaid' },
  { total_amount: 800000, payment_status: undefined } // Treated as unpaid
];

// Results:
// stats.paid = 0
// stats.partialPaid = 0
// stats.paidAmount = 0
// stats.partialPaidAmount = 0
// Display count: 0
// Display amount: 0₫
// stats.unpaidAmount = 1,800,000
```

## Data Flow
1. Orders/Groups are loaded from API
2. Statistics are calculated using React.useMemo for performance
3. For individual orders tab, filtered data is used if customer filter is applied
4. For order groups tab, original data is always used
5. UI displays the calculated statistics in cards

## Error Handling
- Missing `total_amount`: Treated as 0
- Missing `paid_amount`: Treated as 0
- Missing `payment_status`: Treated as 'unpaid'
- Negative amounts: Not specifically handled (should be validated at data entry level)

## Performance Considerations
- Statistics are memoized using React.useMemo
- Recalculated only when dependencies change (orders, filters, current tab)
- Efficient array operations using filter() and reduce()
